#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>

#include <zos_msg/common/common.hpp>
namespace zos
{
namespace iuniapp
{
enum class EDispCont
{
    DISP_CONT_OFF = 0,
    DISP_CONT_AVM = 1,
    DISP_CONT_PARKOPTION = 2,
    DISP_CONT_RPA = 3,
    DISP_CONT_HPA = 4,
    DISP_CONT_AVP = 5,
    DISP_CONT_BUT = 6,
    DISP_CONT_PARK_MENU = 7,
    DISP_CONT_PILOT = 8,
    DISP_CONT_NDEF = 9,
};
enum class EHmiSoftKeySt
{
    HMI_SOFT_KEY_NONE = 0,
    HMI_SOFT_KEY_APA_START = 1,
    HMI_SOFT_KEY_APA_ABORT = 2,
    HMI_SOFT_KEY_APA_FINISH = 3,
    HMI_SOFT_KEY_APA_OUT_START = 4,
    HMI_SOFT_KEY_APA_OUT_ABORT = 5,
    HMI_SOFT_KEY_APA_OUT_FINISH = 6,
    HMI_SOFT_KEY_RPA_START = 7,
    HMI_SOFT_KEY_RPA_ABORT = 8,
    HMI_SOFT_KEY_RPA_FINISH = 9,
    HMI_SOFT_KEY_HPA_LEARN_START = 10,
    HMI_SOFT_KEY_HPA_LEARN_ABORT = 11,
    HMI_SOFT_KEY_HPA_LEARN_FINISH = 12,
    HMI_SOFT_KEY_HPA_GUIDE_START = 13,
    HMI_SOFT_KEY_HPA_GUIDE_ABORT = 14,
    HMI_SOFT_KEY_HPA_GUIDE_FINISH = 15,
    HMI_SOFT_KEY_APA_CONTINUE = 16,
    HMI_SOFT_KEY_APA_CONFIRM = 17,
    HMI_SOFT_KEY_AUTO_IN_MODE = 18,
    HMI_SOFT_KEY_MANUAL_IN_MODE = 19,
    HMI_SOFT_KEY_AUTO_OUT_MODE = 20,
    HMI_SOFT_KEY_MANUAL_OUT_MODE = 21,
    HMI_SOFT_KEY_BUT_START = 22,
    HMI_SOFT_KEY_BUT_CONFIRM = 23,
    HMI_SOFT_KEY_BUT_CONTINUE = 24,
    HMI_SOFT_KEY_BUT_ABORT = 25,
    HMI_SOFT_KEY_BUT_ACTIVE = 26,
    HMI_SOFT_KEY_APA_BACK = 27,
    HMI_SOFT_KEY_CPA_START = 28,
    HMI_SOFT_KEY_CPA_CONTINUE = 29,
    HMI_SOFT_KEY_CPA_ABORT = 30,
};
enum class EActParkFct
{
    ACT_PARK_FCT_NO_FCT = 0,
    ACT_PARK_FCT_APC_IN = 1,
    ACT_PARK_FCT_APC_OUT = 2,
    ACT_PARK_FCT_HPA = 3,
    ACT_PARK_FCT_RPA = 4,
    ACT_PARK_FCT_AVP = 5,
    ACT_PARK_FCT_BUT = 6,
    ACT_PARK_FCT_PRKCTRL_REMOTE = 7,
    ACT_PARK_FCT_CPA = 8,
};
enum class EParkDir
{
    PARK_DIR_NO = 0,
    PARK_DIR_FRW = 1,
    PARK_DIR_BCKW = 2,
};
enum class EParkSide
{
    PARK_SIDE_BOTH = 0,
    PARK_SIDE_LEFT = 1,
    PARK_SIDE_RIGHT = 2,
};
struct CIuniHmiOutput
{
    EDispCont m_dispContRq;
    EHmiSoftKeySt m_hmiSoftKeyState;
    EActParkFct m_selParkFct;
    EParkDir m_parkDir;
    zos::uint16_t m_parkSlotID;
    EParkSide m_parkSide;
    zos::uint16_t m_visibleSlotsCount;
    zos::uint8_t m_visibleParkoutDirCount;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CIuniHmiOutput, m_dispContRq, m_hmiSoftKeyState, m_selParkFct, m_parkDir, m_parkSlotID, m_parkSide, m_visibleSlotsCount, m_visibleParkoutDirCount)

}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::iuniapp::CIuniHmiOutput, StdLayoutTag>
{
    using ValueType = ::zos::iuniapp::CIuniHmiOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::iuniapp::CIuniHmiOutput, StdLayoutTruncTag>
{
    using ValueType = ::zos::iuniapp::CIuniHmiOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
