#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/container/vector.hpp>
#include <basic_types/types.hpp>

namespace zos
{
namespace perception
{
namespace calibration
{
const zos::int32_t FULL_IMAGE_MAXFLOWPOINT = 3000;
const zos::int32_t SCAL_IMAGE_MAXFLOWPOINT = 1500;
const zos::uint32_t MAX_TV_FLOW_NUM_CONST = 4;
using FixedFloatArray=zos::FixedVector<zos::float32_t, SCAL_IMAGE_MAXFLOWPOINT>;
struct COpticalFlowOutput
{
    FixedFloatArray m_startX;
    FixedFloatArray m_startY;
    FixedFloatArray m_endX;
    FixedFloatArray m_endY;
    zos::uint16_t m_flowpts_number;
    zos::uint32_t m_imageNumber;
    zos::float32_t m_metaDataImg;
    zos::float32_t m_metaDataOdo;
    zos::float32_t m_valMetaData;
    zos::uint32_t m_isValid;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(COpticalFlowOutput, m_startX, m_startY, m_endX, m_endY, m_flowpts_number, m_imageNumber, m_metaDataImg, m_metaDataOdo, m_valMetaData, m_isValid)

using OflowPoint=zos::FixedVector<COpticalFlowOutput, MAX_TV_FLOW_NUM_CONST>;
}
}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::perception::calibration::COpticalFlowOutput, StdLayoutTag>
{
    using ValueType = ::zos::perception::calibration::COpticalFlowOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::calibration::COpticalFlowOutput, StdLayoutTruncTag>
{
    using ValueType = ::zos::perception::calibration::COpticalFlowOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
