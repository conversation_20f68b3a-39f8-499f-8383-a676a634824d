#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/container/array.hpp>
#include <basic_types/types.hpp>
#include <basic_types/container/vector.hpp>

#include <zos_msg/numerics/matrix.hpp>
#include <zos_msg/numerics/vector.hpp>
#include <zos_msg/common/timestamp.hpp>
namespace zos
{
namespace perception
{
namespace calibration
{
using ExtrinsicMatrixDatatype=::zos::numerics::Matrix44f;
using IntrinsicMatrixDatatype=::zos::numerics::Matrix33f;
using DistortMatrixDatatype=zos::Array<zos::Array<zos::float32_t, 1>, 10>;
enum class ECameraType
{
    MEI = 0,
    OCAM = 1,
    FISHEYE = 2,
    PINHOLE = 3,
    UNKNOWN = 4,
};
struct CCameraParams
{
    ECameraType m_cameraType;
    zos::uint32_t m_width;
    zos::uint32_t m_height;
    ExtrinsicMatrixDatatype m_extrinsic;
    IntrinsicMatrixDatatype m_intrinsic;
    DistortMatrixDatatype m_distort;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CCameraParams, m_cameraType, m_width, m_height, m_extrinsic, m_intrinsic, m_distort)

enum class EBevCameraLayoutTvRaw4V
{
    Back = 0,
    Left,
    Front,
    Right,
};
const zos::uint32_t BevCameraLayoutTvRaw4V_Count = 4;
using CameraInfoListTvRaw4V=zos::FixedVector<CCameraParams, BevCameraLayoutTvRaw4V_Count>;
enum class EBevCameraLayoutFFRSv7V
{
    Farview = 0,
    Forward,
    LeftForward,
    LeftBackward,
    RightForward,
    RightBackward,
    Backward,
};
const zos::uint32_t BevCameraLayoutFFRSv7V_Count = 7;
using CameraInfoListFFRSv7V=zos::FixedVector<CCameraParams, BevCameraLayoutFFRSv7V_Count>;
enum class EBevCameraLayoutFFTv6V
{
    Wide = 0,
    Front,
    Left,
    Right,
    Back,
    WideCrop,
    Count,
};
const zos::uint32_t BevCameraLayoutFFTv6V_Count = 6;
using CameraInfoListFFTv6V=zos::FixedVector<CCameraParams, BevCameraLayoutFFTv6V_Count>;
struct CCameraParamsBev
{
    zos::common::Timestamp timestamp;
    CameraInfoListTvRaw4V tvraw4v;
    CameraInfoListFFRSv7V ffrsv7v;
    CameraInfoListFFTv6V fftv6v;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CCameraParamsBev, timestamp, tvraw4v, ffrsv7v, fftv6v)

}
}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::perception::calibration::CCameraParams, StdLayoutTag>
{
    using ValueType = ::zos::perception::calibration::CCameraParams;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::calibration::CCameraParams, StdLayoutTruncTag>
{
    using ValueType = ::zos::perception::calibration::CCameraParams;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::calibration::CCameraParamsBev, StdLayoutTag>
{
    using ValueType = ::zos::perception::calibration::CCameraParamsBev;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::calibration::CCameraParamsBev, StdLayoutTruncTag>
{
    using ValueType = ::zos::perception::calibration::CCameraParamsBev;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, fftv6v) + sizeof(uint32_t);
        size += sample.fftv6v.size() * sizeof(decltype(sample.fftv6v)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,fftv6v));
        uint32_t des_size = offsetof(ValueType,fftv6v) + element_size * sizeof(decltype(sample.fftv6v)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
