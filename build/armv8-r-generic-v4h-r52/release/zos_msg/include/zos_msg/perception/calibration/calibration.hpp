#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/container/string.hpp>
#include <basic_types/container/vector.hpp>
#include <basic_types/types.hpp>

namespace zos
{
namespace perception
{
namespace calibration
{
using String=zos::FixedString<128>;
enum class FvSequence
{
    FVWIDE = 0,
    FVFAR = 1,
    FV0 = 2,
    FV1 = 3,
    MAXFVNUM = 4,
};
enum class TvSequence
{
    TVREAR = 0,
    TVLEFT = 1,
    TVFRONT = 2,
    TVRIGHT = 3,
    MAXTVNUM = 4,
};
enum class RvSequence
{
    RVWIDE = 0,
    RVFAR = 1,
    RV0 = 2,
    RV1 = 3,
    MAXRVNUM = 4,
};
enum class SvSequence
{
    SVLEFTFRONT = 0,
    SVLEFTREAR = 1,
    SVRIGHTFRONT = 2,
    SVRIGHTREAR = 3,
    MAXSVNUM = 4,
};
enum class LidarSequence
{
    LIDARWIDE = 0,
    LIDARFAR = 1,
    LIDAR0 = 2,
    LIDAR1 = 3,
    MAXLIDARNUM = 4,
};
enum class IntrinsicModel
{
    MEI = 0,
    OCAM = 1,
    FISHEYE = 2,
    PINHOLE = 3,
    UNKNOWN = 4,
};
enum class IntrinsicStatus
{
    DEFAULT = 0,
    TARCALIB = 1,
    OEMCALIB = 2,
    UNKNOWN = 3,
};
enum class ExtrinsicStatus
{
    UNCALIB = 0,
    INROGRESS = 1,
    CALIBED = 2,
    UNKNOWN = 3,
};
using TangentialDistParams=zos::FixedVector<zos::float32_t, 6>;
using RadialDistParams=zos::FixedVector<zos::float32_t, 20>;
using TangentialUnDistParams=zos::FixedVector<zos::float32_t, 6>;
using RadialUnDistParams=zos::FixedVector<zos::float32_t, 6>;
struct IntrinsicParam
{
    IntrinsicModel m_intrinsicModel;
    IntrinsicStatus m_intrinsicStatus;
    zos::uint32_t m_flags;
    zos::float32_t m_xi;
    zos::float32_t m_skew;
    zos::float32_t m_gamma;
    zos::float32_t m_aspect;
    zos::float32_t m_ppx;
    zos::float32_t m_ppy;
    zos::float32_t m_c;
    zos::float32_t m_d;
    zos::float32_t m_e;
    zos::float32_t m_validFov;
    zos::float32_t m_validImageRadius;
    TangentialDistParams m_tangentialDistParams;
    RadialDistParams m_radialDistParams;
    TangentialUnDistParams m_tangentialUnDistParams;
    RadialUnDistParams m_radialUnDistParams;
    zos::uint16_t m_roiBaseX;
    zos::uint16_t m_roiBaseY;
    zos::uint16_t m_fullResolutionX;
    zos::uint16_t m_fullResolutionY;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(IntrinsicParam, m_intrinsicModel, m_intrinsicStatus, m_flags, m_xi, m_skew, m_gamma, m_aspect, m_ppx, m_ppy, m_c, m_d, m_e, m_validFov, m_validImageRadius, m_tangentialDistParams, m_radialDistParams, m_tangentialUnDistParams, m_radialUnDistParams, m_roiBaseX, m_roiBaseY, m_fullResolutionX, m_fullResolutionY)

struct ExtrinsicParam
{
    ExtrinsicStatus m_extrinsicStatus;
    zos::float32_t pitchAngle;
    zos::float32_t yawAngle;
    zos::float32_t rollAngle;
    zos::float32_t xPosition;
    zos::float32_t yPosition;
    zos::float32_t zPosition;
    zos::float32_t quatW;
    zos::float32_t quatX;
    zos::float32_t quatY;
    zos::float32_t quatZ;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(ExtrinsicParam, m_extrinsicStatus, pitchAngle, yawAngle, rollAngle, xPosition, yPosition, zPosition, quatW, quatX, quatY, quatZ)

struct calibParam
{
    IntrinsicParam m_intrinsicParam;
    ExtrinsicParam m_staticExtrinsicParam;
    ExtrinsicParam m_dynamicExtrinsicParam;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(calibParam, m_intrinsicParam, m_staticExtrinsicParam, m_dynamicExtrinsicParam)

const zos::uint32_t MAX_FV_NUM_CONST = 4;
const zos::uint32_t MAX_TV_NUM_CONST = 4;
const zos::uint32_t MAX_RV_NUM_CONST = 4;
const zos::uint32_t MAX_SV_NUM_CONST = 4;
const zos::uint32_t MAX_LIDAR_NUM_CONST = 4;
using FvCalibParam=zos::FixedVector<calibParam, MAX_FV_NUM_CONST>;
using TvCalibParam=zos::FixedVector<calibParam, MAX_TV_NUM_CONST>;
using RvCalibParam=zos::FixedVector<calibParam, MAX_RV_NUM_CONST>;
using SvCalibParam=zos::FixedVector<calibParam, MAX_SV_NUM_CONST>;
using LidarCalibParam=zos::FixedVector<calibParam, MAX_LIDAR_NUM_CONST>;
}
}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::perception::calibration::IntrinsicParam, StdLayoutTag>
{
    using ValueType = ::zos::perception::calibration::IntrinsicParam;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::calibration::IntrinsicParam, StdLayoutTruncTag>
{
    using ValueType = ::zos::perception::calibration::IntrinsicParam;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::calibration::ExtrinsicParam, StdLayoutTag>
{
    using ValueType = ::zos::perception::calibration::ExtrinsicParam;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::calibration::ExtrinsicParam, StdLayoutTruncTag>
{
    using ValueType = ::zos::perception::calibration::ExtrinsicParam;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::calibration::calibParam, StdLayoutTag>
{
    using ValueType = ::zos::perception::calibration::calibParam;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::calibration::calibParam, StdLayoutTruncTag>
{
    using ValueType = ::zos::perception::calibration::calibParam;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
