#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/container/array.hpp>
#include <basic_types/types.hpp>

namespace zos
{
namespace perception
{
namespace calibration
{
enum class EScalDiagJobIds
{
    Online_CAM_TV = 0,
    Online_CAM_FSV = 1,
    Online_Lidar = 2,
    EOL_CAM_4V = 3,
    EOL_CAM_5V = 4,
    EOL_CAM_6V = 5,
    EOL_CAM_7V = 6,
    EOL_CAM_10V = 7,
    EOL_CAM_11V = 8,
    EOL_CAM_10V_Lidar = 9,
    EOL_CAM_11V_Lidar = 10,
    EOL_CAM_FV = 11,
    EOL_CAM_RV = 12,
    EOL_CAM_RVSC = 13,
};
const zos::uint32_t CalibRequestSIZE = 1;
using reqSizesl2rArray=zos::Array<zos::uint16_t, CalibRequestSIZE>;
using reqCountersl2rArray=zos::Array<zos::uint8_t, CalibRequestSIZE>;
struct TIPCSyncCalibRequestHeader
{
    zos::uint8_t m_numStructs;
    reqSizesl2rArray m_sizes;
    reqCountersl2rArray m_counters;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(TIPCSyncCalibRequestHeader, m_numStructs, m_sizes, m_counters)

using IPCSyncHeader_CalibRequest_st=TIPCSyncCalibRequestHeader;
struct CalibRequest
{
    zos::int32_t locked;
    IPCSyncHeader_CalibRequest_st m_syncHeader_CalibRequest;
    EScalDiagJobIds m_job_id;
    zos::uint16_t m_vehicle_id;
    zos::uint16_t m_factory_id;
    zos::uint16_t m_reserve;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CalibRequest, locked, m_syncHeader_CalibRequest, m_job_id, m_vehicle_id, m_factory_id, m_reserve)

}
}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::perception::calibration::TIPCSyncCalibRequestHeader, StdLayoutTag>
{
    using ValueType = ::zos::perception::calibration::TIPCSyncCalibRequestHeader;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::calibration::TIPCSyncCalibRequestHeader, StdLayoutTruncTag>
{
    using ValueType = ::zos::perception::calibration::TIPCSyncCalibRequestHeader;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::calibration::CalibRequest, StdLayoutTag>
{
    using ValueType = ::zos::perception::calibration::CalibRequest;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::calibration::CalibRequest, StdLayoutTruncTag>
{
    using ValueType = ::zos::perception::calibration::CalibRequest;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
