#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>
#include <basic_types/container/vector.hpp>
#include <basic_types/container/array.hpp>

namespace zos
{
namespace perception
{
namespace calibration
{
enum class ESCalState
{
    CalibSuccessful = 0,
    CalibRunning = 1,
    CalibFailed = 2,
};
enum class ESensorID
{
    CameraDirectionFrontWide = 0,
    CameraDirectionFrontNarrow = 1,
    CameraDirectionBack = 2,
    CameraDirectionLeftFront = 3,
    CameraDirectionRightFront = 4,
    CameraDirectionLeftBack = 5,
    CameraDirectionRightBack = 6,
    CameraDirectionFishFront = 7,
    CameraDirectionFishBack = 8,
    CameraDirectionFishLeft = 9,
    CameraDirectionFishRight = 10,
    Lidar = 11,
};
enum class ECalibrationErrCode
{
    CALIB_STATE_SUCCESS = 0,
    CALIB_STATE_FIND_CORNERS_ERROR = 1,
    CALIB_STATE_INTERNAL_PARAM_ERROR = 2,
    CALIB_STATE_IMAGE_RECOGNITION_ERROR = 3,
    CALIB_STATE_REPROJECTION_ERROR = 4,
    CALIB_STATE_PENDING = 5,
    CALIB_STATE_PITCH_OUTOF_RANGE = 6,
    CALIB_STATE_ROLL_OUTOF_RANGE = 7,
    CALIB_STATE_YAW_OUTOF_RANGE = 8,
    CALIB_STATE_PARAM_SAVE_ERROR = 9,
    CALIB_STATE_TIMEOUT_ERROR = 10,
    CALIB_STATE_IMAGE_ACQUISITION_FAILED = 11,
    CALIB_STATE_PRECHECK_FAILED = 12,
    CALIB_STATE_XPUINTERNAL_FAILED = 13,
    CALIB_STATE_OTHER_FAILED = 14,
    CALIB_STATE_TDCUASHGTERROR = 15,
};
struct SensorResult
{
    ESensorID m_sensor_id;
    zos::uint32_t m_calibrationErrCode;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(SensorResult, m_sensor_id, m_calibrationErrCode)

const zos::uint32_t CameraNumber = 11;
using SensorCalibResult=zos::FixedVector<SensorResult, CameraNumber>;
struct ExtrinsicParameter
{
    zos::float32_t m_roll;
    zos::float32_t m_pitch;
    zos::float32_t m_yaw;
    zos::float32_t m_x;
    zos::float32_t m_y;
    zos::float32_t m_z;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(ExtrinsicParameter, m_roll, m_pitch, m_yaw, m_x, m_y, m_z)

struct SingleSensorParameter
{
    ESensorID m_sensor_id;
    ExtrinsicParameter m_extern_param;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(SingleSensorParameter, m_sensor_id, m_extern_param)

const zos::uint32_t CalibDcomResultSIZE = 1;
using resultSizesl2rArray=zos::Array<zos::uint16_t, CalibDcomResultSIZE>;
using resultCountersl2rArray=zos::Array<zos::uint8_t, CalibDcomResultSIZE>;
struct TIPCSyncCalibDcomResultHeader
{
    zos::uint8_t m_numStructs;
    resultSizesl2rArray m_sizes;
    resultCountersl2rArray m_counters;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(TIPCSyncCalibDcomResultHeader, m_numStructs, m_sizes, m_counters)

using CSensorParameter=zos::FixedVector<SingleSensorParameter, CameraNumber>;
using IPCSyncHeader_CalibDcomResult_st=TIPCSyncCalibDcomResultHeader;
struct CalibDcomResult
{
    zos::int32_t locked;
    IPCSyncHeader_CalibDcomResult_st m_syncHeader_CalibDcomResult;
    ESCalState m_sCalState;
    SensorCalibResult m_calibresult;
    CSensorParameter m_sensorparameter;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CalibDcomResult, locked, m_syncHeader_CalibDcomResult, m_sCalState, m_calibresult, m_sensorparameter)

}
}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::perception::calibration::SensorResult, StdLayoutTag>
{
    using ValueType = ::zos::perception::calibration::SensorResult;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::calibration::SensorResult, StdLayoutTruncTag>
{
    using ValueType = ::zos::perception::calibration::SensorResult;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::calibration::ExtrinsicParameter, StdLayoutTag>
{
    using ValueType = ::zos::perception::calibration::ExtrinsicParameter;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::calibration::ExtrinsicParameter, StdLayoutTruncTag>
{
    using ValueType = ::zos::perception::calibration::ExtrinsicParameter;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::calibration::SingleSensorParameter, StdLayoutTag>
{
    using ValueType = ::zos::perception::calibration::SingleSensorParameter;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::calibration::SingleSensorParameter, StdLayoutTruncTag>
{
    using ValueType = ::zos::perception::calibration::SingleSensorParameter;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::calibration::TIPCSyncCalibDcomResultHeader, StdLayoutTag>
{
    using ValueType = ::zos::perception::calibration::TIPCSyncCalibDcomResultHeader;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::calibration::TIPCSyncCalibDcomResultHeader, StdLayoutTruncTag>
{
    using ValueType = ::zos::perception::calibration::TIPCSyncCalibDcomResultHeader;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::calibration::CalibDcomResult, StdLayoutTag>
{
    using ValueType = ::zos::perception::calibration::CalibDcomResult;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::calibration::CalibDcomResult, StdLayoutTruncTag>
{
    using ValueType = ::zos::perception::calibration::CalibDcomResult;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, m_sensorparameter) + sizeof(uint32_t);
        size += sample.m_sensorparameter.size() * sizeof(decltype(sample.m_sensorparameter)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,m_sensorparameter));
        uint32_t des_size = offsetof(ValueType,m_sensorparameter) + element_size * sizeof(decltype(sample.m_sensorparameter)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
