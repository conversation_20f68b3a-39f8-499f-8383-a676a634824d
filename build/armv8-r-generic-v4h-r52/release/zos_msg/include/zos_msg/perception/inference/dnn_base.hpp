#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/container/string.hpp>
#include <basic_types/container/array.hpp>
#include <basic_types/types.hpp>
#include <basic_types/container/vector.hpp>

#include <zos_msg/common/common.hpp>
#include <zos_msg/common/timestamp.hpp>
#include <zos_msg/geometry/point.hpp>
#include <zos_msg/numerics/matrix.hpp>
#include <zos_msg/numerics/vector.hpp>
#include <zos_msg/geometry/rectangle.hpp>
namespace zos
{
namespace perception
{
namespace inference
{
using String=zos::FixedString<128>;
using ExtrinsicMatrixDatatype=::zos::numerics::Matrix44f;
using IntrinsicMatrixDatatype=::zos::numerics::Matrix33f;
using DistortMatrixDatatype=zos::Array<zos::Array<zos::float32_t, 1>, 10>;
const zos::uint32_t ViewsMaxCount = 10;
using ExtrinsicMatrixDatatypes=zos::FixedVector<ExtrinsicMatrixDatatype, ViewsMaxCount>;
using IntrinsicMatrixDatatypes=zos::FixedVector<IntrinsicMatrixDatatype, ViewsMaxCount>;
using DistortMatrixDatatypes=zos::FixedVector<DistortMatrixDatatype, ViewsMaxCount>;
enum class ECameraType
{
    EPinhole = 0,
    EFisheye_KB = 1,
    EFisheye_OCam = 2,
    EFisheye_MEI = 3,
    Count = 4,
    Invalid = 5,
};
struct CCameraParams
{
    String m_cameraName;
    ECameraType m_cameraType;
    ::zos::geometry::Size2i m_resolution;
    ExtrinsicMatrixDatatype m_extrinsic;
    IntrinsicMatrixDatatype m_intrinsic;
    DistortMatrixDatatype m_distort;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CCameraParams, m_cameraName, m_cameraType, m_resolution, m_extrinsic, m_intrinsic, m_distort)

using CameraParamsList=zos::FixedVector<CCameraParams, ViewsMaxCount>;
struct DebugInfo
{
    String key;
    zos::uint64_t value;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(DebugInfo, key, value)

using DebugInfoSequence=zos::FixedVector<zos::perception::inference::DebugInfo, 16>;
enum class EBevCameraLayout
{
    Nuscenes = 0,
    TvRaw4V,
    FFRSv7V,
    FFTv6V,
};
const zos::uint32_t BevCameraLayout_Count = 4;
enum class EBevCameraLayoutNuscenes
{
    Front = 0,
    FrontRight,
    FrontLeft,
    Back,
    BackLeft,
    BackRight,
};
const zos::uint32_t BevCameraLayoutNuscenes_Count = 6;
using CameraParamsListNuscenes=zos::Array<CCameraParams, BevCameraLayoutNuscenes_Count>;
enum class EBevCameraLayoutTvRaw4V
{
    Back = 0,
    Left,
    Front,
    Right,
};
const zos::uint32_t BevCameraLayoutTvRaw4V_Count = 4;
using CameraParamsListTvRaw4V=zos::Array<CCameraParams, BevCameraLayoutTvRaw4V_Count>;
enum class EBevCameraLayoutFFRSv7V
{
    Farview = 0,
    Forward,
    LeftForward,
    LeftBackward,
    RightForward,
    RightBackward,
    Backward,
};
const zos::uint32_t BevCameraLayoutFFRSv7V_Count = 7;
using CameraParamsListFFRSv7V=zos::Array<CCameraParams, BevCameraLayoutFFRSv7V_Count>;
enum class EBevCameraLayoutFFTv6V
{
    Wide = 0,
    Front,
    Left,
    Right,
    Back,
    WideCrop,
    Count,
};
const zos::uint32_t BevCameraLayoutFFTv6V_Count = 6;
using CameraParamsListFFTv6V=zos::Array<CCameraParams, BevCameraLayoutFFTv6V_Count>;
}
}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::perception::inference::CCameraParams, StdLayoutTag>
{
    using ValueType = ::zos::perception::inference::CCameraParams;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::inference::CCameraParams, StdLayoutTruncTag>
{
    using ValueType = ::zos::perception::inference::CCameraParams;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::inference::DebugInfo, StdLayoutTag>
{
    using ValueType = ::zos::perception::inference::DebugInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::inference::DebugInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::perception::inference::DebugInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
