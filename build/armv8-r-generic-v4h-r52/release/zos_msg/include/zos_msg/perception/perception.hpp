#pragma once
#include <basic_types/serialization/serialization_traits.hpp>

#include <zos_msg/perception/inference/inference.hpp>
#include <zos_msg/perception/calibration/calibration.hpp>
#include <zos_msg/perception/calibration/calibDcomReq.hpp>
#include <zos_msg/perception/calibration/calibDcomResult.hpp>
#include <zos_msg/perception/calibration/calib_info.hpp>
#include <zos_msg/perception/common/common.hpp>
namespace zos
{
namespace serialization
{
}
}
