#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/container/vector.hpp>
#include <basic_types/types.hpp>

#include <zos_msg/perception/inference/inference.hpp>
namespace zos
{
namespace perception
{
namespace common
{
namespace lane3d
{
enum class LaneLineType
{
    NORMAL = 0,
    FISHBONE,
    STOPLINE,
    CROSSGUIDE_LINE,
    LEFTWAITLINE,
    STRAIGHTWAITLINE,
    RIGHT<PERSON>ITLINE,
    UNKNOWN,
};
enum class LaneLineShape
{
    SOLID = 0,
    DASHED,
    DOUBLE_SOLID,
    DOUBLE_DASHED,
    DOUBLELEFTSOLID,
    DOU<PERSON>ERIGHTSOLID,
    LEFTSOLID_RIGHTDASHED,
    RIGHTSOLID_LEFTDASHED,
    CROSS_GUIDE,
    SHORTED_DASHED,
    FISHBONE,
    FISHBONE_DASHEDLINE,
    COLOREDTHREELINE,
    THICK<PERSON>SHED,
    POIN<PERSON>IN<PERSON>,
    REVERSIBLELINE,
    OTHER,
    UNKNOWN,
};
enum class LaneLineColor
{
    WHITE = 0,
    YELLOW,
    BLUE,
    ORANGE,
    RED,
    <PERSON>TH<PERSON>,
    UNKNOWN,
};
enum class LaneLineScene
{
    NORM = 0,
    CONVERGE,
    FORK,
    UNKNOWN,
};
enum class RoadSignType
{
    ARROW = 0,
    ZEBRA_CROSS,
    LEFTWAITINGZONE,
    STRAIGHTWAITINGZONE,
    NO_PARKING,
    SLOW_DOWN,
    STOP_LINE,
    UNKNOWN,
};
enum class ArrowType
{
    STRAIGHT_FORWARD = 0,
    STRAIGHT_FORWARD_OR_TURN_LEFT,
    STRAIGHT_FORWARD_OR_TURN_RIGHT,
    STRAIGHT_FORWARD_OR_TURN_AROUND,
    TURN_LEFT,
    TURN_LEFT_OR_TURN_AROUND,
    TURN_LEFT_OR_MERGE_LEFT,
    TURN_RIGHT,
    TURN_RIGHT_OR_MERGE_RIGHT,
    TURN_LEFT_OR_TURN_RIGHT,
    TURN_AROUND,
    FORBID_MARK,
    FORBID_TURN_LEFT,
    FORBID_TURN_RIGHT,
    FORBID_TURN_AROUND,
    STRAIGHT_FORWARD_OR_TURN_LEFT_OR_TURN_RIGHT,
    STRAIGHT_FORWARD_OR_TURN_AROUND_OR_TURN_LEFT,
    TURN_RIGHT_OR_TURN_AROUND,
    FRONT_NEAR_CROSSWALK,
    UNKNOWN,
};
enum class PtSplitType
{
    SPLIT = 0,
    MERGE,
    UNKNOWN,
};
enum class RoadEdgeType
{
    ROAD = 0,
    GROUND,
    CONE,
    WATERHOUSE,
    FENCE,
    UNKNOWN,
};
}
const zos::uint32_t LanePointsMaxCount = 20;
using LanePoints=zos::FixedVector<::zos::geometry::Point3f, LanePointsMaxCount>;
struct LaneLineInfo
{
    lane3d::LaneLineType line_type;
    zos::float32_t line_type_confidence;
    lane3d::LaneLineShape shape;
    zos::float32_t shape_confidence;
    lane3d::LaneLineColor color;
    zos::float32_t color_confidence;
    LanePoints pts;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(LaneLineInfo, line_type, line_type_confidence, shape, shape_confidence, color, color_confidence, pts)

using PolygonPoints=zos::FixedVector<::zos::geometry::Point3f, 4>;
struct PolygonInfo
{
    lane3d::RoadSignType road_sign;
    zos::float32_t road_sign_confidence;
    lane3d::ArrowType arrow;
    zos::float32_t arrow_confidence;
    PolygonPoints pts;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(PolygonInfo, road_sign, road_sign_confidence, arrow, arrow_confidence, pts)

struct SplitPointInfo
{
    lane3d::PtSplitType split_type;
    zos::float32_t split_type_confidence;
    zos::geometry::Point3f pt;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(SplitPointInfo, split_type, split_type_confidence, pt)

struct RoadEdgeInfo
{
    lane3d::RoadEdgeType type;
    zos::float32_t type_confidence;
    LanePoints pts;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(RoadEdgeInfo, type, type_confidence, pts)

using LaneLineInfoSequence=zos::FixedVector<LaneLineInfo, 20>;
using PolygonInfoSequence=zos::FixedVector<PolygonInfo, 20>;
using SplitPointInfoSequence=zos::FixedVector<SplitPointInfo, 10>;
using RoadEdgeInfoSequence=zos::FixedVector<RoadEdgeInfo, 10>;
struct PerceptionLaneLine3ds
{
    zos::common::Timestamp timestamp;
    zos::uint32_t seq_num;
    zos::perception::inference::EBevCoordinateSystemType bcs;
    LaneLineInfoSequence lanelineOutputs;
    PolygonInfoSequence polygonOutputs;
    SplitPointInfoSequence splitPointOutputs;
    RoadEdgeInfoSequence roadEdgeOutputs;
    zos::perception::inference::CameraParamsList cameraParamsList;
    zos::perception::inference::DebugInfoSequence debugInfos;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(PerceptionLaneLine3ds, timestamp, seq_num, bcs, lanelineOutputs, polygonOutputs, splitPointOutputs, roadEdgeOutputs, cameraParamsList, debugInfos)

}
}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::perception::common::LaneLineInfo, StdLayoutTag>
{
    using ValueType = ::zos::perception::common::LaneLineInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::common::LaneLineInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::perception::common::LaneLineInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, pts) + sizeof(uint32_t);
        size += sample.pts.size() * sizeof(decltype(sample.pts)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,pts));
        uint32_t des_size = offsetof(ValueType,pts) + element_size * sizeof(decltype(sample.pts)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::common::PolygonInfo, StdLayoutTag>
{
    using ValueType = ::zos::perception::common::PolygonInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::common::PolygonInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::perception::common::PolygonInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, pts) + sizeof(uint32_t);
        size += sample.pts.size() * sizeof(decltype(sample.pts)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,pts));
        uint32_t des_size = offsetof(ValueType,pts) + element_size * sizeof(decltype(sample.pts)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::common::SplitPointInfo, StdLayoutTag>
{
    using ValueType = ::zos::perception::common::SplitPointInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::common::SplitPointInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::perception::common::SplitPointInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::common::RoadEdgeInfo, StdLayoutTag>
{
    using ValueType = ::zos::perception::common::RoadEdgeInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::common::RoadEdgeInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::perception::common::RoadEdgeInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, pts) + sizeof(uint32_t);
        size += sample.pts.size() * sizeof(decltype(sample.pts)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,pts));
        uint32_t des_size = offsetof(ValueType,pts) + element_size * sizeof(decltype(sample.pts)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::common::PerceptionLaneLine3ds, StdLayoutTag>
{
    using ValueType = ::zos::perception::common::PerceptionLaneLine3ds;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::common::PerceptionLaneLine3ds, StdLayoutTruncTag>
{
    using ValueType = ::zos::perception::common::PerceptionLaneLine3ds;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, debugInfos) + sizeof(uint32_t);
        size += sample.debugInfos.size() * sizeof(decltype(sample.debugInfos)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,debugInfos));
        uint32_t des_size = offsetof(ValueType,debugInfos) + element_size * sizeof(decltype(sample.debugInfos)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
