#pragma once
#include <basic_types/serialization/serialization_traits.hpp>

#include <zos_msg/perception/common/object3d.hpp>
#include <zos_msg/perception/common/laneline3d.hpp>
#include <zos_msg/perception/common/parking_slot3d.hpp>
#include <zos_msg/perception/common/occupancy3d.hpp>
#include <zos_msg/perception/common/traffic_light.hpp>
#include <zos_msg/perception/common/traffic_sign.hpp>
#include <zos_msg/perception/common/ihbc.hpp>
#include <zos_msg/perception/common/scene.hpp>
namespace zos
{
namespace serialization
{
}
}
