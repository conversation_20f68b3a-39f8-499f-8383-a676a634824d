#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>
#include <basic_types/container/vector.hpp>

#include <zos_msg/perception/inference/inference.hpp>
namespace zos
{
namespace perception
{
namespace common
{
namespace psd3d
{
enum class psdCls_v1
{
    LINE = 0,
    BRICKLAWN,
    MECHANICAL,
    CORNER,
};
using psdCls=psdCls_v1;
enum class psdType_v1
{
    PERPENDICULAR = 0,
    PARALLEL,
    ANGLE,
};
using psdType=psdType_v1;
enum class psdStatus_v1
{
    FREE = 0,
    OCCUPIED,
};
using psdStatus=psdStatus_v1;
enum class psdSensorType_v1
{
    VISION = 0,
    SPACE,
};
using psdSensorType=psdSensorType_v1;
}
struct PerceptionParkingSlot3d
{
    zos::uint32_t id;
    zos::geometry::Point3f pt0;
    zos::geometry::Point3f pt1;
    zos::geometry::Point3f pt2;
    zos::geometry::Point3f pt3;
    zos::float32_t pt0IsAccurate;
    zos::float32_t pt1IsAccurate;
    zos::float32_t pt2IsAccurate;
    zos::float32_t pt3IsAccurate;
    zos::int32_t occId;
    psd3d::psdType type;
    psd3d::psdStatus status;
    psd3d::psdSensorType sensorType;
    psd3d::psdCls cls;
    zos::float32_t confidence;
    zos::bool_t doesChockPt0Exist;
    zos::bool_t doesChockPt1Exist;
    zos::geometry::Point3f chockPt0;
    zos::geometry::Point3f chockPt1;
    zos::bool_t updateTargetpose;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(PerceptionParkingSlot3d, id, pt0, pt1, pt2, pt3, pt0IsAccurate, pt1IsAccurate, pt2IsAccurate, pt3IsAccurate, occId, type, status, sensorType, cls, confidence, doesChockPt0Exist, doesChockPt1Exist, chockPt0, chockPt1, updateTargetpose)

using ParkingSlot3dSequence=zos::FixedVector<PerceptionParkingSlot3d, (4 * 50)>;
struct PerceptionParkingSlot3ds
{
    zos::common::Timestamp timestamp;
    zos::uint32_t seq_num;
    zos::perception::inference::EBevCoordinateSystemType bcs;
    ParkingSlot3dSequence parkingSlots;
    zos::perception::inference::CameraParamsList cameraParamsList;
    zos::perception::inference::DebugInfoSequence debugInfos;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(PerceptionParkingSlot3ds, timestamp, seq_num, bcs, parkingSlots, cameraParamsList, debugInfos)

}
}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::perception::common::PerceptionParkingSlot3d, StdLayoutTag>
{
    using ValueType = ::zos::perception::common::PerceptionParkingSlot3d;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::common::PerceptionParkingSlot3d, StdLayoutTruncTag>
{
    using ValueType = ::zos::perception::common::PerceptionParkingSlot3d;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::common::PerceptionParkingSlot3ds, StdLayoutTag>
{
    using ValueType = ::zos::perception::common::PerceptionParkingSlot3ds;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::common::PerceptionParkingSlot3ds, StdLayoutTruncTag>
{
    using ValueType = ::zos::perception::common::PerceptionParkingSlot3ds;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, debugInfos) + sizeof(uint32_t);
        size += sample.debugInfos.size() * sizeof(decltype(sample.debugInfos)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,debugInfos));
        uint32_t des_size = offsetof(ValueType,debugInfos) + element_size * sizeof(decltype(sample.debugInfos)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
