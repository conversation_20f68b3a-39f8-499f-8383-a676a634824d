#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>
#include <basic_types/container/vector.hpp>

#include <zos_msg/perception/inference/inference.hpp>
namespace zos
{
namespace perception
{
namespace common
{
enum class TraficSignType
{
    INVALID = 0,
    LIMIT_SPEED_MAX = 1,
    REMOVE_LIMIT_SPEED_MAX = 2,
    LIMIT_SPEED_MIN = 3,
    FORBID = 4,
    WARNING = 5,
};
enum class TraficSignSubType
{
    INVALID = 0,
    LIMIT_SPEED_MAX_130 = 1130,
    LIMIT_SPEED_MAX_120 = 1120,
    LIMIT_SPEED_MAX_110 = 1110,
    LIMIT_SPEED_MAX_100 = 1100,
    LIMIT_SPEED_MAX_90 = 1090,
    LIMIT_SPEED_MAX_80 = 1080,
    LIMIT_SPEED_MAX_70 = 1070,
    LIMIT_SPEED_MAX_60 = 1060,
    LIMIT_SPEED_MAX_50 = 1050,
    <PERSON>IM<PERSON>_SPEED_MAX_40 = 1040,
    LIMIT_SPEED_MAX_30 = 1030,
    LIMIT_SPEED_MAX_20 = 1020,
    LIMIT_SPEED_MAX_10 = 1010,
    REMOVE_LIMIT_SPEED_MAX_100 = 2100,
    REMOVE_LIMIT_SPEED_MAX_80 = 2080,
    REMOVE_LIMIT_SPEED_MAX_60 = 2060,
    REMOVE_LIMIT_SPEED_MAX_40 = 2040,
    REMOVE_LIMIT_SPEED_MAX_20 = 2020,
    LIMIT_SPEED_MIN_90 = 3090,
    LIMIT_SPEED_MIN_80 = 3080,
    LIMIT_SPEED_MIN_70 = 3070,
    LIMIT_SPEED_MIN_60 = 3060,
    LIMIT_SPEED_MIN_50 = 3050,
    LIMIT_SPEED_MIN_40 = 3040,
    LIMIT_SPEED_MIN_30 = 3030,
    FORBID_OVERTAKE = 4001,
    FORBID_OVERTAKE_REMOVE = 4002,
    FORBID_PARKING = 4003,
    FORBID_LEFT = 4004,
    FORBID_RIGHT = 4005,
    FORBID_AROUND = 4006,
    WARNING_PEDESTRIAN = 5001,
    WARNING_ROADWORKS = 5002,
    WARNING_CHILD = 5003,
};
struct TrafficSign
{
    zos::int32_t id;
    TraficSignType type;
    TraficSignSubType subtype;
    zos::float32_t confidence;
    zos::geometry::Rectf box2d;
    zos::geometry::Rectf box2d_roi;
    zos::numerics::Vector3f position_ego;
    zos::numerics::Vector3d position_world;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(TrafficSign, id, type, subtype, confidence, box2d, box2d_roi, position_ego, position_world)

using TrafficSignSequence=zos::FixedVector<TrafficSign, 100>;
struct TrafficSignFrame
{
    zos::common::Timestamp timestamp;
    zos::uint32_t seq_num;
    TrafficSignSequence traffic_signs;
    zos::perception::inference::CameraParamsList cameraParamsList;
    zos::perception::inference::DebugInfoSequence debugInfos;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(TrafficSignFrame, timestamp, seq_num, traffic_signs, cameraParamsList, debugInfos)

}
}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::perception::common::TrafficSign, StdLayoutTag>
{
    using ValueType = ::zos::perception::common::TrafficSign;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::common::TrafficSign, StdLayoutTruncTag>
{
    using ValueType = ::zos::perception::common::TrafficSign;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::common::TrafficSignFrame, StdLayoutTag>
{
    using ValueType = ::zos::perception::common::TrafficSignFrame;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::common::TrafficSignFrame, StdLayoutTruncTag>
{
    using ValueType = ::zos::perception::common::TrafficSignFrame;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, debugInfos) + sizeof(uint32_t);
        size += sample.debugInfos.size() * sizeof(decltype(sample.debugInfos)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,debugInfos));
        uint32_t des_size = offsetof(ValueType,debugInfos) + element_size * sizeof(decltype(sample.debugInfos)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
