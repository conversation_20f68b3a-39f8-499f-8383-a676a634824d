#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/container/vector.hpp>
#include <basic_types/types.hpp>

#include <zos_msg/perception/inference/inference.hpp>
namespace zos
{
namespace perception
{
namespace common
{
namespace object3d
{
enum class ObjectType_v1
{
    UNKNOWN = 0,
    UNKNOWN_UNMOVABLE,
    UNKNOWN_MOVABLE,
    PEDESTRIAN,
    BICYCLE,
    VEHIC<PERSON>,
    CYCLIST,
    STATIC_OBSTACLE,
    TRANSPORT_ELEMENT,
    CAR,
    SUV,
    VAN,
    LORRY,
    BUS,
    TRUCK,
    CYCLE,
    TRICYCLE,
    MOTOCYCILIST,
    TRICYCLIST,
    TRAFFIC_CONE,
    TRAFFIC_COLUNM,
    GROUND_LOCK,
    PILLARS,
    WATER_SAFETY_BARRIERS,
    SHOPPING_CART,
    NO_PARKING_SIGN,
    STONE_PIER,
    TRASH_BIN,
};
enum class ObjectSubType_v1
{
    UNKNOWN = 0,
    OPEN,
    CLOSE,
};
using ObjectSubType=ObjectSubType_v1;
using ObjectType=ObjectType_v1;
using Point3dSequence=zos::FixedVector<zos::geometry::Point3d, 10>;
using Point3fSequence=zos::FixedVector<zos::geometry::Point3f, 10>;
enum class MotionType
{
    UNKNOWN = 0,
    MOVING_EGO_DIRECTION_DRIVING,
    MOVING_EGO_DIRECTION_REVERSING,
    MOVING_ON_COMING,
    MOVING_CROSSING,
    MOVING_EGO_DIRECTION_STOPPED,
    STATIONARY,
};
}
struct PerceptionObject3d
{
    zos::uint32_t id;
    zos::geometry::Point3f center;
    zos::numerics::Matrix33f center_uncertainty;
    zos::geometry::Point3d center_global;
    zos::numerics::Matrix33f center_global_uncertainty;
    zos::geometry::Point3f size;
    zos::geometry::Point3f size_variance;
    zos::float32_t theta;
    zos::float32_t theta_global;
    zos::float32_t theta_variance;
    object3d::ObjectType object_type;
    zos::numerics::Vectorf type_probs;
    object3d::ObjectSubType object_subtype;
    zos::numerics::Vectorf sub_type_probs;
    zos::geometry::Point3f velocity;
    zos::numerics::Matrix33f velocity_uncertainty;
    zos::geometry::Point3f velocity_relative;
    zos::numerics::Matrix33f velocity_relative_uncertainty;
    zos::geometry::Point3f velocity_global;
    zos::numerics::Matrix33f velocity_global_uncertainty;
    zos::bool_t velocity_converged;
    zos::geometry::Point3f acceleration;
    zos::numerics::Matrix33f acceleration_uncertainty;
    zos::geometry::Point3f acceleration_global;
    zos::numerics::Matrix33f acceleration_global_uncertainty;
    object3d::Point3fSequence polygon;
    object3d::Point3dSequence polygon_global;
    zos::float32_t existence_probability;
    zos::bool_t is_cipv;
    object3d::MotionType motion_type;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(PerceptionObject3d, id, center, center_uncertainty, center_global, center_global_uncertainty, size, size_variance, theta, theta_global, theta_variance, object_type, type_probs, object_subtype, sub_type_probs, velocity, velocity_uncertainty, velocity_relative, velocity_relative_uncertainty, velocity_global, velocity_global_uncertainty, velocity_converged, acceleration, acceleration_uncertainty, acceleration_global, acceleration_global_uncertainty, polygon, polygon_global, existence_probability, is_cipv, motion_type)

struct AssociateInfo
{
    zos::float64_t timestamp;
    zos::uint32_t measure_id;
    zos::uint32_t sensor_type;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(AssociateInfo, timestamp, measure_id, sensor_type)

using AssociateInfoSequence=zos::FixedVector<AssociateInfo, 10>;
struct ObjectFusionDebugInfo
{
    zos::uint32_t track_id;
    AssociateInfoSequence asso_info;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(ObjectFusionDebugInfo, track_id, asso_info)

using PerceptionObject3dSequence=zos::FixedVector<PerceptionObject3d, 200>;
using ObjectFusionDebugInfoSequence=zos::FixedVector<ObjectFusionDebugInfo, 200>;
struct PerceptionObject3ds
{
    zos::common::Timestamp timestamp;
    zos::uint32_t seq_num;
    zos::perception::inference::EBevCoordinateSystemType bcs;
    PerceptionObject3dSequence objects;
    zos::perception::inference::CameraParamsList cameraParamsList;
    zos::perception::inference::DebugInfoSequence debugInfos;
    ObjectFusionDebugInfoSequence objFusionDebugInfos;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(PerceptionObject3ds, timestamp, seq_num, bcs, objects, cameraParamsList, debugInfos, objFusionDebugInfos)

}
}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::perception::common::PerceptionObject3d, StdLayoutTag>
{
    using ValueType = ::zos::perception::common::PerceptionObject3d;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::common::PerceptionObject3d, StdLayoutTruncTag>
{
    using ValueType = ::zos::perception::common::PerceptionObject3d;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::common::AssociateInfo, StdLayoutTag>
{
    using ValueType = ::zos::perception::common::AssociateInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::common::AssociateInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::perception::common::AssociateInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::common::ObjectFusionDebugInfo, StdLayoutTag>
{
    using ValueType = ::zos::perception::common::ObjectFusionDebugInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::common::ObjectFusionDebugInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::perception::common::ObjectFusionDebugInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, asso_info) + sizeof(uint32_t);
        size += sample.asso_info.size() * sizeof(decltype(sample.asso_info)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,asso_info));
        uint32_t des_size = offsetof(ValueType,asso_info) + element_size * sizeof(decltype(sample.asso_info)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::common::PerceptionObject3ds, StdLayoutTag>
{
    using ValueType = ::zos::perception::common::PerceptionObject3ds;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::common::PerceptionObject3ds, StdLayoutTruncTag>
{
    using ValueType = ::zos::perception::common::PerceptionObject3ds;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, objFusionDebugInfos) + sizeof(uint32_t);
        size += sample.objFusionDebugInfos.size() * sizeof(decltype(sample.objFusionDebugInfos)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,objFusionDebugInfos));
        uint32_t des_size = offsetof(ValueType,objFusionDebugInfos) + element_size * sizeof(decltype(sample.objFusionDebugInfos)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
