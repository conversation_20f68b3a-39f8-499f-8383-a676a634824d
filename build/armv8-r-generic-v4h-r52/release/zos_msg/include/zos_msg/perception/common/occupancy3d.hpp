#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/container/vector.hpp>
#include <basic_types/types.hpp>

#include <zos_msg/perception/inference/inference.hpp>
namespace zos
{
namespace perception
{
namespace common
{
namespace occ3d
{
const zos::uint32_t MAX_OCCUPANCY_SIZE = (720 * 256);
enum class OccupancyState
{
    UNKNOWN = 0,
    FREE,
    OCCUPY,
    STATIC,
    DYNAMIC,
};
enum class OccupancyType
{
    UNKNOWN = 0,
    GROUND,
    STATICOBJECT,
    DYNAMICOBJECT,
    CURB,
    FENCE,
    VEGETATION,
    WALL,
    PILLAR,
    DRAININGWELL,
    LIMIT,
    BUMP,
    NOISE,
    HARDISOLATION,
};
const zos::uint32_t STATEOFFSET = 0;
const zos::uint32_t TYPEOFFSET = 4;
const zos::uint32_t HEIGHTOFFSET = 8;
const zos::uint32_t SCOREOFFSET = 16;
using OccupancyInfoSequence=zos::FixedVector<zos::uint32_t, MAX_OCCUPANCY_SIZE>;
using OccupancyVelocitySequence=zos::FixedVector<zos::numerics::Vector2f, MAX_OCCUPANCY_SIZE>;
}
struct PerceptionOccupancyLayer
{
    zos::numerics::Vector3f resolution;
    zos::numerics::Vector2d anchor;
    zos::numerics::Vector2i size;
    zos::numerics::Vector2f rangeZ;
    zos::float32_t strideScore;
    occ3d::OccupancyInfoSequence info;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(PerceptionOccupancyLayer, resolution, anchor, size, rangeZ, strideScore, info)

struct PerceptionOccupancy3d
{
    zos::common::Timestamp timestamp;
    zos::uint32_t seq_num;
    zos::perception::inference::EBevCoordinateSystemType bcs;
    PerceptionOccupancyLayer coarse_layer;
    PerceptionOccupancyLayer fine_layer;
    zos::perception::inference::CameraParamsList cameraParamsList;
    zos::perception::inference::DebugInfoSequence debugInfos;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(PerceptionOccupancy3d, timestamp, seq_num, bcs, coarse_layer, fine_layer, cameraParamsList, debugInfos)

}
}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::perception::common::PerceptionOccupancyLayer, StdLayoutTag>
{
    using ValueType = ::zos::perception::common::PerceptionOccupancyLayer;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::common::PerceptionOccupancyLayer, StdLayoutTruncTag>
{
    using ValueType = ::zos::perception::common::PerceptionOccupancyLayer;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, info) + sizeof(uint32_t);
        size += sample.info.size() * sizeof(decltype(sample.info)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,info));
        uint32_t des_size = offsetof(ValueType,info) + element_size * sizeof(decltype(sample.info)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::common::PerceptionOccupancy3d, StdLayoutTag>
{
    using ValueType = ::zos::perception::common::PerceptionOccupancy3d;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::common::PerceptionOccupancy3d, StdLayoutTruncTag>
{
    using ValueType = ::zos::perception::common::PerceptionOccupancy3d;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, debugInfos) + sizeof(uint32_t);
        size += sample.debugInfos.size() * sizeof(decltype(sample.debugInfos)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,debugInfos));
        uint32_t des_size = offsetof(ValueType,debugInfos) + element_size * sizeof(decltype(sample.debugInfos)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
