#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/container/vector.hpp>
#include <basic_types/types.hpp>

#include <zos_msg/perception/inference/inference.hpp>
namespace zos
{
namespace perception
{
namespace common
{
enum class InfoType
{
    IHBC_SCENE_HIGH_BEAM_NEEDED = 0,
    IHBC_SCENE_LOW_BEAM_NEEDED = 1,
    IHBC_SCENE_TWILIGHT_NEEDING_LOW_BEAMS = 2,
    IHBC_SCENE_UNKNOWN = 3,
    WEATHER_SUNNY = 4,
    WEATHER_OVERCAST = 5,
    WEATHER_HEAVY_RAIN = 6,
    WEATHER_SNOW = 7,
    WEATHER_FOG = 8,
    WEATHER_UNKNOWN = 9,
    ILLUMINATION_DAYLIGHT = 10,
    ILLUMINATION_TWILIGHT = 11,
    ILLUMINATION_NIGHT = 12,
    ILLUMINATION_UNKNOWN = 13,
    DRVING_OR_PARKING_SCENE_GROUND_PARKING_SCENE = 14,
    DRVING_OR_PARKING_SCENE_UNDERGROUND_PARKING_SCENE = 15,
    DRVING_OR_PARKING_SCENE_DRVING_SCENE = 16,
    DRVING_OR_PARKING_SCENE_UNKNOWN = 17,
    IMAGE_BLUR_LEVEL_LOW = 18,
    IMAGE_BLUR_LEVEL_MIDDLE = 19,
    IMAGE_BLUR_LEVEL_HIGH = 20,
    IMAGE_BLUR_LEVEL_UNKNOWN = 21,
};
struct Info
{
    InfoType info_type;
    zos::numerics::Vectorf info_type_confidence;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(Info, info_type, info_type_confidence)

using InfoSequence=zos::FixedVector<Info, 22>;
struct InfoFrame
{
    zos::common::Timestamp timestamp;
    zos::uint32_t seq_num;
    InfoSequence info;
    zos::perception::inference::DebugInfoSequence debugInfos;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(InfoFrame, timestamp, seq_num, info, debugInfos)

}
}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::perception::common::Info, StdLayoutTag>
{
    using ValueType = ::zos::perception::common::Info;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::common::Info, StdLayoutTruncTag>
{
    using ValueType = ::zos::perception::common::Info;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, info_type_confidence) + sizeof(uint32_t);
        size += sample.info_type_confidence.size() * sizeof(decltype(sample.info_type_confidence)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,info_type_confidence));
        uint32_t des_size = offsetof(ValueType,info_type_confidence) + element_size * sizeof(decltype(sample.info_type_confidence)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::common::InfoFrame, StdLayoutTag>
{
    using ValueType = ::zos::perception::common::InfoFrame;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::common::InfoFrame, StdLayoutTruncTag>
{
    using ValueType = ::zos::perception::common::InfoFrame;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, debugInfos) + sizeof(uint32_t);
        size += sample.debugInfos.size() * sizeof(decltype(sample.debugInfos)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,debugInfos));
        uint32_t des_size = offsetof(ValueType,debugInfos) + element_size * sizeof(decltype(sample.debugInfos)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
