#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>
#include <basic_types/container/vector.hpp>

#include <zos_msg/perception/inference/inference.hpp>
namespace zos
{
namespace perception
{
namespace common
{
enum class TrafficLightStatus
{
    INVALID = 0,
    CIRCLE = 1,
    FORWARD = 2,
    LEFT = 3,
    RIGHT = 4,
    TURN = 5,
    LEFT_AND_TURN = 6,
    FORWARD_AND_LEFT = 7,
    FORWARD_AND_RIGHT = 8,
    NO_DRIVE_INTO = 9,
    ALLOW_DRIVE_INTO = 10,
    PEDESTRIAN = 11,
    NON_MOTOR = 12,
};
enum class TrafficLightColor
{
    INVALID = 0,
    RED = 1,
    GREEN = 2,
    YELLOW = 3,
    WHITE = 4,
};
enum class TrafficLightType
{
    INVALID = 0,
    VEHICLE = 1,
    PEDESTRIAN = 2,
    BICYCLE = 3,
    OTHER = 4,
};
struct TrafficLight
{
    zos::int32_t id;
    TrafficLightType type;
    zos::numerics::Vector5f type_confidence;
    TrafficLightColor color;
    zos::numerics::Vector5f color_confidence;
    TrafficLightStatus status;
    zos::numerics::Vector13f status_confidence;
    zos::bool_t is_light_on;
    zos::numerics::Vector2f light_on_off_confidence;
    zos::int32_t count_down_time;
    zos::geometry::Rectf box2d;
    zos::geometry::Rectf box2d_roi;
    zos::numerics::Vector3f position_ego;
    zos::numerics::Vector3d position_world;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(TrafficLight, id, type, type_confidence, color, color_confidence, status, status_confidence, is_light_on, light_on_off_confidence, count_down_time, box2d, box2d_roi, position_ego, position_world)

using TrafficLightSequence=zos::FixedVector<TrafficLight, 10>;
struct TrafficLightFrame
{
    zos::common::Timestamp timestamp;
    zos::uint32_t seq_num;
    TrafficLightSequence traffic_lights;
    zos::bool_t m_isFvTeleL1;
    zos::perception::inference::CameraParamsList cameraParamsList;
    zos::perception::inference::DebugInfoSequence debugInfos;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(TrafficLightFrame, timestamp, seq_num, traffic_lights, m_isFvTeleL1, cameraParamsList, debugInfos)

}
}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::perception::common::TrafficLight, StdLayoutTag>
{
    using ValueType = ::zos::perception::common::TrafficLight;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::common::TrafficLight, StdLayoutTruncTag>
{
    using ValueType = ::zos::perception::common::TrafficLight;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::common::TrafficLightFrame, StdLayoutTag>
{
    using ValueType = ::zos::perception::common::TrafficLightFrame;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::common::TrafficLightFrame, StdLayoutTruncTag>
{
    using ValueType = ::zos::perception::common::TrafficLightFrame;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, debugInfos) + sizeof(uint32_t);
        size += sample.debugInfos.size() * sizeof(decltype(sample.debugInfos)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,debugInfos));
        uint32_t des_size = offsetof(ValueType,debugInfos) + element_size * sizeof(decltype(sample.debugInfos)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
