#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/container/vector.hpp>
#include <basic_types/types.hpp>

#include <zos_msg/perception/inference/inference.hpp>
namespace zos
{
namespace perception
{
namespace common
{
enum class IhbcType
{
    Scene_High_Beam_Needed = 0,
    Scene_Low_Beam_Needed = 1,
    Scene_Twilight_Needing_Low_Beams = 2,
    Scene_Unkwown = 3,
};
struct Ihbc
{
    IhbcType ihbc_type;
    zos::numerics::Vectorf ihbc_type_confidence;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(Ihbc, ihbc_type, ihbc_type_confidence)

using IhbcSequence=zos::FixedVector<Ihbc, 4>;
struct IhbcFrame
{
    zos::common::Timestamp timestamp;
    zos::uint32_t seq_num;
    IhbcSequence ihbc;
    zos::perception::inference::DebugInfoSequence debugInfos;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(IhbcFrame, timestamp, seq_num, ihbc, debugInfos)

}
}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::perception::common::Ihbc, StdLayoutTag>
{
    using ValueType = ::zos::perception::common::Ihbc;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::common::Ihbc, StdLayoutTruncTag>
{
    using ValueType = ::zos::perception::common::Ihbc;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, ihbc_type_confidence) + sizeof(uint32_t);
        size += sample.ihbc_type_confidence.size() * sizeof(decltype(sample.ihbc_type_confidence)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,ihbc_type_confidence));
        uint32_t des_size = offsetof(ValueType,ihbc_type_confidence) + element_size * sizeof(decltype(sample.ihbc_type_confidence)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::common::IhbcFrame, StdLayoutTag>
{
    using ValueType = ::zos::perception::common::IhbcFrame;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::common::IhbcFrame, StdLayoutTruncTag>
{
    using ValueType = ::zos::perception::common::IhbcFrame;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, debugInfos) + sizeof(uint32_t);
        size += sample.debugInfos.size() * sizeof(decltype(sample.debugInfos)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,debugInfos));
        uint32_t des_size = offsetof(ValueType,debugInfos) + element_size * sizeof(decltype(sample.debugInfos)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
