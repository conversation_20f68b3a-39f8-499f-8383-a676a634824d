#pragma once
#include <basic_types/serialization/serialization_traits.hpp>

namespace zos
{
namespace perception
{
namespace cam_capture
{
enum class ECamPipelineId
{
    ECamPplIdBegin = 0,
    ECamPplIdSvRightR = 0,
    ECamPplIdSvLeftR = 1,
    ECamPplIdSvLeftF = 2,
    ECamPplIdSvRightF = 3,
    ECamPplIdRvNorm = 4,
    ECamPplIdFvTele = 5,
    ECamPplIdFvFar = 5,
    ECamPplIdTvRear = 6,
    ECamPplIdTvLeft = 7,
    ECamPplIdTvFront = 8,
    ECamPplIdTvRight = 9,
    ECamPplIdFvMain = 10,
    ECamPplIdFvWide = 10,
    ECamPplIdMax = 11,
    ECamPplIdInvalide = 24,
};
}
}
}
namespace zos
{
namespace serialization
{
}
}
