#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/container/array.hpp>
#include <basic_types/types.hpp>

#include <zos_msg/perception/preprocess/cam_capture/cam_capture_common.hpp>
namespace zos
{
namespace perception
{
namespace cam_capture
{
enum class EHLBufType
{
    EHLBufTypeInvalid = 0,
    EHLBufTypeHbVio,
    EHLBufTypePymV3,
    EHLBufTypeEmdInfo,
};
enum class ELLMemType
{
    ELLMemTypeHbInvalid = 0,
    ELLMemTypeHbCommon,
    ELLMemTypeHbGraphic,
};
const zos::uint32_t HLBufSize = 768;
const zos::uint32_t LLMemSize = 256;
using HLBufInfo=zos::Array<zos::int8_t, HLBufSize>;
using LLMemInfo=zos::Array<zos::int8_t, LLMemSize>;
using VAddrList=zos::Array<zos::uint64_t, 3>;
using PAddrList=zos::Array<zos::uint64_t, 3>;
struct CZxCamImageInfo
{
    ECamPipelineId m_pipelineId;
    zos::uint64_t m_timestamp;
    zos::uint64_t m_frameId;
    zos::uint32_t m_width;
    zos::uint32_t m_height;
    zos::uint32_t m_stride;
    zos::uint32_t m_planeCount;
    VAddrList m_vaddrList;
    PAddrList m_paddrList;
    EHLBufType m_hlBufType;
    HLBufInfo m_hlBufInfo;
    ELLMemType m_llMemType;
    LLMemInfo m_llMemInfo;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CZxCamImageInfo, m_pipelineId, m_timestamp, m_frameId, m_width, m_height, m_stride, m_planeCount, m_vaddrList, m_paddrList, m_hlBufType, m_hlBufInfo, m_llMemType, m_llMemInfo)

}
}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::perception::cam_capture::CZxCamImageInfo, StdLayoutTag>
{
    using ValueType = ::zos::perception::cam_capture::CZxCamImageInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::perception::cam_capture::CZxCamImageInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::perception::cam_capture::CZxCamImageInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
