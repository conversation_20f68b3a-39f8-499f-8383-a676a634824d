#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>

#include <zos_msg/common/common.hpp>
#include <zos_msg/common/timestamp.hpp>
#include <zos_msg/geometry/point.hpp>
namespace zos
{
namespace parkcontrol
{
struct ParkCtrlCmd
{
    zos::float32_t StopDis;
    zos::float32_t SpeedTar;
    zos::float32_t TorqueTar;
    zos::float32_t AxTar;
    zos::bool_t DirverOff;
    zos::bool_t StopRequest;
    zos::bool_t TorqueEn;
    zos::bool_t AxEn;
    zos::uint8_t m_vlcVehicleIsBlocked;
    zos::float32_t SteerAngleTar;
    zos::float32_t m_apcAccTargetAxLowerComfortLimit;
    zos::float32_t m_apcAccTargetAxUpperComfortLimit;
    zos::float32_t m_apcAccTargetAxJerkLowerLimit;
    zos::float32_t m_apcAccTargetAxJerkUpperLimit;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(ParkCtrlCmd, StopDis, SpeedTar, TorqueTar, AxTar, DirverOff, StopRequest, TorqueEn, AxEn, m_vlcVehicleIsBlocked, SteerAngleTar, m_apcAccTargetAxLowerComfortLimit, m_apcAccTargetAxUpperComfortLimit, m_apcAccTargetAxJerkLowerLimit, m_apcAccTargetAxJerkUpperLimit)

}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::parkcontrol::ParkCtrlCmd, StdLayoutTag>
{
    using ValueType = ::zos::parkcontrol::ParkCtrlCmd;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkcontrol::ParkCtrlCmd, StdLayoutTruncTag>
{
    using ValueType = ::zos::parkcontrol::ParkCtrlCmd;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
