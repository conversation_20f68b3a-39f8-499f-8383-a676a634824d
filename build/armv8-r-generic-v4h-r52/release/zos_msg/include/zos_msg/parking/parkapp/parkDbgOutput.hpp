#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>

#include <zos_msg/common/common.hpp>
namespace zos
{
namespace parkapp
{
struct dbg_Abort_st
{
    zos::uint16_t dbg_Abort_CancelHoldP;
    zos::uint16_t dbg_Abort_CancelGo;
    zos::uint16_t dbg_Abort_Handover;
    zos::uint16_t dbg_Abort_ActInactive;
    zos::uint16_t dbg_Abort_Pause;
    zos::uint16_t dbg_Abort_SearchOut;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(dbg_Abort_st, dbg_Abort_CancelHoldP, dbg_Abort_CancelGo, dbg_Abort_Handover, dbg_Abort_ActInactive, dbg_Abort_Pause, dbg_Abort_SearchOut)

struct CDbgParkctlOutput
{
    dbg_Abort_st dbg_Output_st;
    zos::bool_t m_TimeoutPull_P_Gear_b;
    zos::uint8_t m_DebugApcState;
    zos::uint8_t m_DebugLcpOpMode;
    zos::uint8_t m_DebugEpsOpMode;
    zos::bool_t m_DebugLcpApcModStopRq;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CDbgParkctlOutput, dbg_Output_st, m_TimeoutPull_P_Gear_b, m_DebugApcState, m_DebugLcpOpMode, m_DebugEpsOpMode, m_DebugLcpApcModStopRq)

struct CDbgRpaPocDirSt
{
    zos::bool_t CrossFrontLeftOut;
    zos::bool_t CrossFrontRightOut;
    zos::bool_t FrontOut;
    zos::bool_t CrossRearLeftOut;
    zos::bool_t CrossRearRightOut;
    zos::bool_t ParallelFrontLeftOut;
    zos::bool_t ParallelFrontRightOut;
    zos::bool_t RearOut;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CDbgRpaPocDirSt, CrossFrontLeftOut, CrossFrontRightOut, FrontOut, CrossRearLeftOut, CrossRearRightOut, ParallelFrontLeftOut, ParallelFrontRightOut, RearOut)

struct Debug_RpaTransition_st
{
    zos::uint8_t m_DebugRpaPrepQuitMsgID_en;
    zos::uint8_t m_DebugRpaPrepErrorMsgID_en;
    zos::uint8_t m_DebugRpaPrepTermMsgID_en;
    zos::uint8_t m_DebugRpaHoldMsgID_en;
    zos::uint8_t m_DebugRpaInterMsgID_en;
    zos::uint8_t m_DebugRpaCancelMsgID_en;
    zos::uint8_t m_DebugRpaErrorMsgID_en;
    zos::uint8_t m_DebugRpaBreakTermMsgID_en;
    zos::uint8_t m_DebugRpaCancelTermMsgID_en;
    zos::uint8_t m_DebugRpaErrorTermMsgID_en;
    zos::uint8_t m_DebugRpaFinishMsgID_en;
    zos::bool_t m_DebugRpaTransFinishedEarly_b;
    zos::bool_t m_DebugRpaTrans_FinishedPTout_b;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(Debug_RpaTransition_st, m_DebugRpaPrepQuitMsgID_en, m_DebugRpaPrepErrorMsgID_en, m_DebugRpaPrepTermMsgID_en, m_DebugRpaHoldMsgID_en, m_DebugRpaInterMsgID_en, m_DebugRpaCancelMsgID_en, m_DebugRpaErrorMsgID_en, m_DebugRpaBreakTermMsgID_en, m_DebugRpaCancelTermMsgID_en, m_DebugRpaErrorTermMsgID_en, m_DebugRpaFinishMsgID_en, m_DebugRpaTransFinishedEarly_b, m_DebugRpaTrans_FinishedPTout_b)

struct CDbgRpactlAciOutput
{
    zos::uint8_t m_DebugRpaEpsOpMode;
    zos::uint8_t m_DebugRpaLcpOpMode;
    zos::uint8_t m_DebugRpaMirrFldRq_Mode;
    zos::uint8_t m_DebugRpaRemCtrlSelPosRq;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CDbgRpactlAciOutput, m_DebugRpaEpsOpMode, m_DebugRpaLcpOpMode, m_DebugRpaMirrFldRq_Mode, m_DebugRpaRemCtrlSelPosRq)

struct CDbgRpactlRpaOutput
{
    zos::uint8_t m_DebugRpaActive;
    zos::uint8_t m_DebugRpaCoded;
    zos::uint8_t m_DebugRpaState;
    zos::uint8_t m_DebugRpaStateIdle;
    zos::uint8_t m_DebugRpaOutDirSel;
    zos::bool_t m_DebugRpaStateIdleOut;
    CDbgRpaPocDirSt m_DebugRpaPocDirSt;
    Debug_RpaTransition_st m_DebugRpaTransitionOutput;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CDbgRpactlRpaOutput, m_DebugRpaActive, m_DebugRpaCoded, m_DebugRpaState, m_DebugRpaStateIdle, m_DebugRpaOutDirSel, m_DebugRpaStateIdleOut, m_DebugRpaPocDirSt, m_DebugRpaTransitionOutput)

struct CDbgRpactlOutput
{
    CDbgRpactlAciOutput m_DebugRpactlAciOutput;
    CDbgRpactlRpaOutput m_DebugRpactlRpaOutput;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CDbgRpactlOutput, m_DebugRpactlAciOutput, m_DebugRpactlRpaOutput)

struct CDbgButCtlOutput
{
    zos::uint8_t m_UndoCtlFuncCtlReq;
    zos::uint8_t m_UndoCtlActiveState;
    zos::uint8_t m_UndoCtlAbortReason;
    zos::uint8_t m_isUndoActive;
    zos::uint8_t m_isUndoPsbl;
    zos::uint8_t m_isUndoTargetPosReached;
    zos::uint8_t m_isUndoFwdPsbl;
    zos::uint8_t m_isUndoBwdPsbl;
    zos::uint8_t m_TurnIndRq;
    zos::uint8_t m_ButEpsMode;
    zos::uint8_t m_ButLcpMode;
    zos::uint8_t m_ButPauseStatus;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CDbgButCtlOutput, m_UndoCtlFuncCtlReq, m_UndoCtlActiveState, m_UndoCtlAbortReason, m_isUndoActive, m_isUndoPsbl, m_isUndoTargetPosReached, m_isUndoFwdPsbl, m_isUndoBwdPsbl, m_TurnIndRq, m_ButEpsMode, m_ButLcpMode, m_ButPauseStatus)

struct CHmism2hminotifier
{
    zos::uint8_t m_notifierOpMd;
    zos::uint8_t m_dispContRq;
    zos::uint8_t m_sv3dSoftkeyst;
    zos::uint8_t m_apaState;
    zos::uint8_t m_apaStCan;
    zos::uint8_t m_butStCan;
    zos::uint8_t m_hpaStCan;
    zos::uint8_t m_ctrlMdPrkApc;
    zos::uint8_t m_parkStateApc;
    zos::uint8_t m_ctrlMdPrkRpa;
    zos::uint8_t m_parkStateRpa;
    zos::uint8_t m_ctrlMdPrkHpa;
    zos::uint8_t m_parkStateHpa;
    zos::uint32_t m_nbOfVisibleSlots;
    zos::uint8_t m_rpaState;
    zos::uint8_t m_rpaStCan;
    zos::bool_t m_RpaStateIdleOut;
    zos::bool_t m_RpaStateIdlePrkCtrl;
    zos::uint8_t m_RpaOutDirSel;
    zos::uint8_t m_RpaPocDirSt;
    zos::uint8_t m_IdcDriveMode_en;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CHmism2hminotifier, m_notifierOpMd, m_dispContRq, m_sv3dSoftkeyst, m_apaState, m_apaStCan, m_butStCan, m_hpaStCan, m_ctrlMdPrkApc, m_parkStateApc, m_ctrlMdPrkRpa, m_parkStateRpa, m_ctrlMdPrkHpa, m_parkStateHpa, m_nbOfVisibleSlots, m_rpaState, m_rpaStCan, m_RpaStateIdleOut, m_RpaStateIdlePrkCtrl, m_RpaOutDirSel, m_RpaPocDirSt, m_IdcDriveMode_en)

struct CDbgHmismOutput
{
    zos::uint8_t m_hmiMainSt_e;
    zos::uint8_t m_hmiPrk_e;
    zos::uint8_t m_hmiPrkAct_e;
    zos::uint8_t m_hmiTl_e;
    zos::uint8_t m_visuOpMd;
    zos::uint8_t m_manactRq;
    zos::uint8_t m_actParkFct;
    zos::uint8_t m_parkDir;
    zos::uint16_t m_prkSlotID;
    zos::uint8_t m_parkSide;
    zos::bool_t m_apcSearchInManualFlag_b;
    zos::uint8_t m_hpaMode;
    zos::bool_t m_Takeoverrequest;
    zos::uint16_t m_prkSlotIDSelectedbyHPA;
    zos::uint8_t m_ButHmiStatus;
    zos::uint8_t m_manactParkFct;
    CHmism2hminotifier m_hmism2hminotifier;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CDbgHmismOutput, m_hmiMainSt_e, m_hmiPrk_e, m_hmiPrkAct_e, m_hmiTl_e, m_visuOpMd, m_manactRq, m_actParkFct, m_parkDir, m_prkSlotID, m_parkSide, m_apcSearchInManualFlag_b, m_hpaMode, m_Takeoverrequest, m_prkSlotIDSelectedbyHPA, m_ButHmiStatus, m_manactParkFct, m_hmism2hminotifier)

struct CParkDbgOutput
{
    CDbgParkctlOutput m_dbgParkCtlOutput;
    CDbgRpactlOutput m_dbgRpaCtlOutput;
    CDbgButCtlOutput m_dbgButCtlOutput;
    CDbgHmismOutput m_dbgHmismOutput;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CParkDbgOutput, m_dbgParkCtlOutput, m_dbgRpaCtlOutput, m_dbgButCtlOutput, m_dbgHmismOutput)

}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::parkapp::dbg_Abort_st, StdLayoutTag>
{
    using ValueType = ::zos::parkapp::dbg_Abort_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::dbg_Abort_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::parkapp::dbg_Abort_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::CDbgParkctlOutput, StdLayoutTag>
{
    using ValueType = ::zos::parkapp::CDbgParkctlOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::CDbgParkctlOutput, StdLayoutTruncTag>
{
    using ValueType = ::zos::parkapp::CDbgParkctlOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::CDbgRpaPocDirSt, StdLayoutTag>
{
    using ValueType = ::zos::parkapp::CDbgRpaPocDirSt;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::CDbgRpaPocDirSt, StdLayoutTruncTag>
{
    using ValueType = ::zos::parkapp::CDbgRpaPocDirSt;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::Debug_RpaTransition_st, StdLayoutTag>
{
    using ValueType = ::zos::parkapp::Debug_RpaTransition_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::Debug_RpaTransition_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::parkapp::Debug_RpaTransition_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::CDbgRpactlAciOutput, StdLayoutTag>
{
    using ValueType = ::zos::parkapp::CDbgRpactlAciOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::CDbgRpactlAciOutput, StdLayoutTruncTag>
{
    using ValueType = ::zos::parkapp::CDbgRpactlAciOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::CDbgRpactlRpaOutput, StdLayoutTag>
{
    using ValueType = ::zos::parkapp::CDbgRpactlRpaOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::CDbgRpactlRpaOutput, StdLayoutTruncTag>
{
    using ValueType = ::zos::parkapp::CDbgRpactlRpaOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::CDbgRpactlOutput, StdLayoutTag>
{
    using ValueType = ::zos::parkapp::CDbgRpactlOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::CDbgRpactlOutput, StdLayoutTruncTag>
{
    using ValueType = ::zos::parkapp::CDbgRpactlOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::CDbgButCtlOutput, StdLayoutTag>
{
    using ValueType = ::zos::parkapp::CDbgButCtlOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::CDbgButCtlOutput, StdLayoutTruncTag>
{
    using ValueType = ::zos::parkapp::CDbgButCtlOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::CHmism2hminotifier, StdLayoutTag>
{
    using ValueType = ::zos::parkapp::CHmism2hminotifier;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::CHmism2hminotifier, StdLayoutTruncTag>
{
    using ValueType = ::zos::parkapp::CHmism2hminotifier;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::CDbgHmismOutput, StdLayoutTag>
{
    using ValueType = ::zos::parkapp::CDbgHmismOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::CDbgHmismOutput, StdLayoutTruncTag>
{
    using ValueType = ::zos::parkapp::CDbgHmismOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::CParkDbgOutput, StdLayoutTag>
{
    using ValueType = ::zos::parkapp::CParkDbgOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::CParkDbgOutput, StdLayoutTruncTag>
{
    using ValueType = ::zos::parkapp::CParkDbgOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
