#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>

#include <zos_msg/parking/parkapp/parkBaseType.hpp>
namespace zos
{
namespace parkapp
{
enum class EVsm_Gear_en
{
    Park = 0,
    Reverse = 1,
    Neutral = 2,
    Drive = 3,
};
struct CParkControlOutput
{
    zos::float32_t m_VmctchAccel;
    zos::uint32_t m_DrivenDist;
    zos::float64_t m_vhmspeed;
    zos::uint16_t m_lcpRemainDist;
    zos::uint16_t m_lcpVehSpeedRq;
    ELcpGearRq_en m_lcpDrivePosRq;
    EVsm_Gear_en m_inVsmGear;
    ELcpFctStat_en m_lcpFctStatRq;
    ELcpStopRq_en m_lcpStopRq;
    zos::float32_t m_epsActAngle;
    zos::float32_t m_Axslope;
    zos::uint8_t m_apaLongControlActive;
    zos::uint8_t m_brakePedalApplied;
    zos::uint8_t m_cddHoldActive;
    zos::uint8_t m_dynamicSwitchGear;
    zos::float32_t m_pdcDistance;
    zos::float32_t m_drivenDistMeter;
    zos::float32_t m_accTargetAxJerkUpperLimit;
    zos::float32_t m_accTargetAxJerkLowerLimit;
    zos::float32_t m_accTargetAxUpperComfortLimit;
    zos::float32_t m_accTargetAxLowerComfortLimit;
    zos::float32_t m_accTargetAx;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CParkControlOutput, m_VmctchAccel, m_DrivenDist, m_vhmspeed, m_lcpRemainDist, m_lcpVehSpeedRq, m_lcpDrivePosRq, m_inVsmGear, m_lcpFctStatRq, m_lcpStopRq, m_epsActAngle, m_Axslope, m_apaLongControlActive, m_brakePedalApplied, m_cddHoldActive, m_dynamicSwitchGear, m_pdcDistance, m_drivenDistMeter, m_accTargetAxJerkUpperLimit, m_accTargetAxJerkLowerLimit, m_accTargetAxUpperComfortLimit, m_accTargetAxLowerComfortLimit, m_accTargetAx)

}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::parkapp::CParkControlOutput, StdLayoutTag>
{
    using ValueType = ::zos::parkapp::CParkControlOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::CParkControlOutput, StdLayoutTruncTag>
{
    using ValueType = ::zos::parkapp::CParkControlOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
