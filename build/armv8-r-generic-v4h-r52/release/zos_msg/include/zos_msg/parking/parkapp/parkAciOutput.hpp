#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>

#include <zos_msg/parking/parkapp/parkBaseType.hpp>
namespace zos
{
namespace parkapp
{
enum class ELcpPrkStateInfo_en
{
    Idle = 0,
    Srch = 1,
    Disclaimer = 2,
    Test = 3,
    BckStrk = 4,
    FwdStrk = 5,
    EndGo = 7,
    EndHld = 8,
    CnclDrv = 9,
    Flt = 10,
};
enum class ELcpGuidState_en
{
    Inactv = 0,
    Actv = 1,
    InactvLongActv = 2,
    RemStrt = 3,
};
enum class EFuSaState_en
{
    None = 0,
    ManeuverCtrl = 1,
    Ctrl = 2,
    RemoteRdy = 3,
    RpaCtrl = 4,
    SrpCtrl = 5,
    AvpCtrl = 6,
    HitchPsv = 7,
};
struct CAciLcpOutput
{
    zos::uint16_t m_lcpRemainDist;
    zos::uint16_t m_lcpVehSpeedRq;
    ELcpFctStat_en m_lcpFctStatRq;
    ELcpPrkType_en m_lcpPrkType;
    ELcpPrkStateInfo_en m_lcpPrkStateRq;
    ELcpGearRq_en m_lcpDrivePosRq;
    ELcpStopRq_en m_lcpStopRq;
    ELcpGuidState_en m_lcpGuidState;
    EFuSaState_en m_fusaStateOut;
    zos::uint8_t m_aliveCounter;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CAciLcpOutput, m_lcpRemainDist, m_lcpVehSpeedRq, m_lcpFctStatRq, m_lcpPrkType, m_lcpPrkStateRq, m_lcpDrivePosRq, m_lcpStopRq, m_lcpGuidState, m_fusaStateOut, m_aliveCounter)

enum class EParkStatEps_en
{
    Init = 0,
    Inactive = 1,
    CtrlRequest = 2,
    CtrlActive = 3,
    Disable = 4,
};
struct CAciEpsOutput
{
    EParkStatEps_en m_parkStateToEps;
    zos::float32_t m_steerWheelAngleToEps;
    zos::float32_t m_steerWheelAngleSpeedToEps;
    zos::float32_t m_frontWheelAngleToEps;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CAciEpsOutput, m_parkStateToEps, m_steerWheelAngleToEps, m_steerWheelAngleSpeedToEps, m_frontWheelAngleToEps)

enum class EMirrFldRq_en
{
    Idle = 0,
    Extn = 1,
    Retract = 2,
};
struct CAciMirrorOutput
{
    EMirrFldRq_en m_mirrFldRq;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CAciMirrorOutput, m_mirrFldRq)

enum class EMeblcpOpmode_en
{
    OFF = 0,
    INACTIVE = 1,
    PREFILL = 2,
    AEB = 3,
    FINISH = 4,
    ERROR = 5,
};
struct CAciMebLcpOutput
{
    EMeblcpOpmode_en m_mebLcpOpmode;
    zos::float32_t m_mebLcpBrkgReqVal;
    zos::float32_t m_mebLcpDistanceToCollision;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CAciMebLcpOutput, m_mebLcpOpmode, m_mebLcpBrkgReqVal, m_mebLcpDistanceToCollision)

struct CParkAciOutput
{
    CAciLcpOutput m_aciLcpOutput;
    CAciEpsOutput m_aciEpsOutput;
    CAciMirrorOutput m_aciMirrorOutput;
    CAciMebLcpOutput m_aciMebLcpOutput;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CParkAciOutput, m_aciLcpOutput, m_aciEpsOutput, m_aciMirrorOutput, m_aciMebLcpOutput)

}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::parkapp::CAciLcpOutput, StdLayoutTag>
{
    using ValueType = ::zos::parkapp::CAciLcpOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::CAciLcpOutput, StdLayoutTruncTag>
{
    using ValueType = ::zos::parkapp::CAciLcpOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::CAciEpsOutput, StdLayoutTag>
{
    using ValueType = ::zos::parkapp::CAciEpsOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::CAciEpsOutput, StdLayoutTruncTag>
{
    using ValueType = ::zos::parkapp::CAciEpsOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::CAciMirrorOutput, StdLayoutTag>
{
    using ValueType = ::zos::parkapp::CAciMirrorOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::CAciMirrorOutput, StdLayoutTruncTag>
{
    using ValueType = ::zos::parkapp::CAciMirrorOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::CAciMebLcpOutput, StdLayoutTag>
{
    using ValueType = ::zos::parkapp::CAciMebLcpOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::CAciMebLcpOutput, StdLayoutTruncTag>
{
    using ValueType = ::zos::parkapp::CAciMebLcpOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::CParkAciOutput, StdLayoutTag>
{
    using ValueType = ::zos::parkapp::CParkAciOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::CParkAciOutput, StdLayoutTruncTag>
{
    using ValueType = ::zos::parkapp::CParkAciOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
