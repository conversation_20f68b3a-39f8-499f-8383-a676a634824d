#pragma once
#include <basic_types/serialization/serialization_traits.hpp>

#include <zos_msg/common/common.hpp>
namespace zos
{
namespace parkapp
{
enum class ELcpFctStat_en
{
    Init = 0,
    Inactv = 1,
    ApgMd = 2,
    RpgMd = 3,
    SlpMd = 4,
    CmpMd = 5,
    AvpMd = 6,
    SltMd = 7,
    Sna = 8,
};
enum class ELcpPrkType_en
{
    Idle = 0,
    InLong = 1,
    InLat = 2,
    OutLong = 3,
    OutLat = 4,
    RemPrk = 5,
};
enum class ELcpGearRq_en
{
    Idle = 0,
    D = 5,
    N = 6,
    R = 7,
    P = 8,
};
enum class ELcpStopRq_en
{
    Idle = 0,
    Cdr = 1,
    Mdr = 2,
    Edr = 3,
    Secure = 4,
};
}
}
namespace zos
{
namespace serialization
{
}
}
