#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>
#include <basic_types/container/array.hpp>

#include <zos_msg/common/common.hpp>
namespace zos
{
namespace parkapp
{
enum class EHminotifierRq
{
    NO_RQ = 0,
    DR_FW_SRCH_PRK_SP = 1,
    SRCH_PRK_SP = 2,
    SRCH_PRK_SP_BLW_SPD = 3,
    STOP_VEH_PRK = 4,
    PRESS_KEY_STRT_PRK_PILOT = 5,
    WATCH_SR = 6,
    PRK_CFM = 7,
    PRK_SP_SEL_CFM = 8,
    PRK_DIR_SEL_CFM = 9,
    REM_PRK_PIL_CON_CFM = 10,
    ENG_REV = 11,
    PRK_PIL_ACT_RD2BRK = 12,
    ENG_FOR = 14,
    RPA_SUBSC_EXP = 16,
    HIT_PULLSTRAIGHT = 17,
    PRK_FINISHED = 18,
    PRK_FINISHED_TK_OVR = 19,
    PRK_ABRT_TK_OVR = 21,
    PRK_PRK_ASSIST_AVAIL_BELOW = 22,
    PRK_ASSIST_READY_ST2 = 23,
    PRK_ASSIST_DIR_CHOOSE_ST2 = 24,
    UNPRK_ACTV_BRK = 25,
    UNPRK_STOP_TO_UNPARK = 26,
    PRK_PRK_ACTV_CHNG_GEAR = 27,
    RPA_SLCT_CNFR_PD = 28,
    UNPRK_UNPRK_ACTV_CHNG_GEAR = 29,
    UNPRK_FINISHED_TK_OVR = 31,
    PRK_ASSIST_NOT_AVAIL_TRAILER_ATTACHED = 32,
    UNPRK_ABRT_TK_OVR = 33,
    RPA_CNTN_APP_WAIT_CNNCT = 34,
    RPA_LEAVE_CAR_TAKE_KEYS = 35,
    RPA_SEL_CNFR_PS = 36,
    RPA_ACTIVE = 37,
    RPA_FINISHED = 38,
    RPA_CNT_CNNCT_RPA_DEV = 39,
    RPA_PROC_ABORT = 41,
    PRK_VIEW_FRONT = 42,
    PRK_VIEW_REAR = 43,
    PRK_VIEW_REAR_HIT = 44,
    PRK_VIEW_SIDES = 45,
    PRK_VIEW_FRONT_WIDE_ANGLE = 46,
    PRK_VIEW_REAR_WIDE_ANGLE = 47,
    RPA_PRK_ASSIST_FINSIHED_PREP_RPA = 50,
    PRK_ASSIST_INOPERABLE_OFFROAD = 51,
    PRK_ASSIST_INOPERABLE_SLOPE_EXCEEDED = 52,
    TAP_THROTTLE_TO_RESUME = 53,
    TAP_THROTTLE_TO_RESUME_PAUSE = 54,
    PRK_ASSIST_TEMP_NOT_AVAIL = 55,
    PRK_CAM_DIS_SPEED2HIGH = 56,
    PRK_3D_CHOOSE_VIEW = 100,
    PRK_ASSIST_READY = 101,
    PRK_ASSIST_SLT_CHOOSE = 102,
    PRK_ASSIST_SLT_DIR_CHOOSE = 103,
    PRK_ASSIST_DIR_CHOOSE = 104,
    PRK_ASSIST_ABRT_NOT_REACH_POS = 106,
    PRK_ASSIST_ABRT_TIMEOUT = 107,
    PRK_ASSIST_ABRT_WRONG_VEHINFO = 108,
    PRK_ASSIST_ABRT_PTSOFF = 109,
    HPA_TL_MEM_PRK_ASSIT_ACT_STP_CAR_TO_FSH = 111,
    PRK_ASSIST_ABRT_DRV = 112,
    PRK_ASSIST_ABRT_INTERNAL_ERR = 113,
    PRK_ASSIST_ABRT_SPEED_EXCEEDED = 114,
    PRK_ASSIST_ABRT_SLOPE_EXCEEDED = 115,
    HPA_TL_STEER_ANGLE_TOO_HIGH = 116,
    HPA_TL_MEM_PRK_ASSIST_STOP_VEH_TO_START_REC = 117,
    HPA_TL_MEM_PRK_ASSIST_READY_PRESS_HK = 118,
    HPA_TL_NAR_PASSAGE_FOLD_IN_MIRROR = 119,
    HPA_TL_MEM_PRK_ASSIT_ABORT_PASSAGE_NAR = 120,
    HPA_TL_MEM_PRK_ASSIT_ABORT = 121,
    HPA_TL_MEM_PRK_ASSIT_ACT_DRV_ON_OR_PRESS_SAVE_BTN_TO_FSH = 122,
    HPA_TL_SEL_MAN_OR_REC_NEW_ONE = 123,
    HPA_TL_MEM_FULL = 124,
    HPA_TL_LIM_FUNC_WT_FOL_MIRROR = 125,
    HPA_TL_MOV_THE_VEH_CLOSER = 126,
    HPA_TL_SCA_FOR_START_PSN = 127,
    HPA_TL_MAN_REC_START_NOT_POS = 128,
    HPA_TL_PSN_CONF_PRESS_HK = 129,
    HPA_TL_PSN_CONF_PRESS_HK_ENG_P = 130,
    HPA_TL_FASTEN_YOUR_SEATBELT = 131,
    PRK_DISENGAGE_P = 132,
    HPA_TL_MEM_ERROR = 133,
    PRK_DISENGAGE_P_OR_START_REM_APP = 134,
    PRK_ASSIST_SLOPE_EXCEEDED = 135,
    PRK_ASSIST_NOT_AVAIL_OFFROAD = 136,
    PRK_ASSIST_NOT_AVAIL_SNOW_CHAINS = 137,
    HPA_TL_PRESS_REC_BTN_TO_REC_NEW_PRK_IN_PATH = 150,
    HPA_TL_PRESS_REC_BTN_TO_ADD_PRK_OUT_PATH = 151,
    DOOR_OPEN_PAUSE = 152,
    TRUNKAJAR_OPEN_PAUSE = 153,
    HOOD_OPEN_PAUSE = 154,
    BRAKEPEDAL_RELEASE_PAUSE = 155,
    DRIVER_SEAT_BELT_BUCKLED_PAUSE = 156,
    OBSTACLE_ON_PATH_PAUSE = 157,
    PRK_ASSIST_ABRT_DRV_STEERWHEEL = 158,
    PRK_ASSIST_ABRT_DRV_ACCPEDAL = 159,
    PRK_ASSIST_ABRT_DRV_GEAR = 160,
    PRK_ASSIST_ABRT_DRV_EPB = 161,
    PRK_ASSIST_ABRT_APACancelButton = 162,
    PRK_ASSIST_ABRT_MoveTimes = 163,
    PRK_ASSIST_ABRT_MenuveringTimeOut = 164,
    PRK_ASSIST_ABRT_PauseTimes = 165,
    PRK_ASSIST_ABRT_PathPlanningFail = 166,
    RPA_GESTURE_PAUSE = 167,
    PRK_OUT_NO_OBSTACLES_AHEAD = 168,
    PRK_ASSIST_ABRT_UNEXPECTED_GEAR = 169,
    PRK_ASSIST_ABRT_EXTERNAL_FUNCTION_ENABLED = 170,
    PRK_ASSIST_ABRT_NOT_PULL_P_EPB_GEAR = 171,
    PRK_ASSIST_ABRT_STUCK_WHEEL = 172,
    RPA_GEAR_NOT_P = 173,
    RPA_DOOR_NOT_CLS = 174,
    RPA_HOOD_NOT_CLS = 175,
    RPA_PHONE_TOO_FAR = 176,
    RPA_PHONE_SOC_LOW = 177,
    PRK_OUT_VEHICLE_NOT_STATIC = 178,
    PRK_OUT_NO_PRKOUT_PATH = 179,
    SRCHING_PARK_OUT = 180,
    PAUSE_CONTINUE_BUTTON = 181,
    RPA_NOT_P_WAIT_TOO_LONG = 182,
    RPA_NOT_CONNECT_WAIT_TOO_LONG = 183,
    RPA_PLEASE_SHIFT_INTO_P_GEAR = 184,
    RPA_PHONE_DISCONNECT_PAUSE = 185,
    RPA_PHONE_DISCONNECT_TAKEOVER = 186,
    RPA_PHONE_CONNECT_START_PARK = 187,
    RPA_PARKING_AND_HOOD_OPEN = 188,
    DOOR_OPEN_TIMEOUT = 189,
    TRUNKAJAR_OPEN_TIMEOUT = 190,
    HOOD_OPEN_TIMEOUT = 191,
    BRAKEPEDAL_RELEASE_TIMEOUT = 192,
    DRIVER_SEAT_BELT_BUCKLED_TIMEOUT = 193,
    OBSTACLE_ON_PATH_PAUSE_TIMEOUT = 194,
    DOOR_OPEN_SEARCH = 195,
    TRUNKAJAR_OPEN_SEARCH = 196,
    HOOD_OPEN_SEARCH = 197,
    BRAKEPEDAL_RELEASE_SEARCH = 198,
    DRIVER_SEAT_BELT_BUCKLED_SEARCH = 199,
    FUNC_FAILURE = 200,
    SRCH_PRK_SLIGHTLY_OVERSPEED = 201,
    SRCH_PRK_TIMEOUT = 202,
    FUNC_ACTIVE_FAILURE = 203,
    RPA_PARKING_AND_DOOR_OPEN = 204,
    RPA_DOOR_NOT_CLS_TIMEOUT = 205,
    RPA_HOOD_NOT_CLS_TIMEOUT = 206,
    RPA_GEAR_NOT_P_TIMEOUT = 207,
    SRCH_PRK_D_GEAR = 208,
    SRCH_PRK_P_GEAR_TIMEOUT = 209,
    APA_START_PARKING = 210,
    POC_SELECT_DIRECTION = 211,
    PRK_HOLD_BRAKE = 212,
    PRK_INVALID_SPOT = 213,
    PRK_END_INTERACTION = 214,
    RPA_ASSIST_ABRT_NOT_PULL_P_EPB_GEAR = 215,
    PRK_USER_EXIT_APA_VIEW = 216,
    RPA_PARKING_AND_BRAKEPEDAL_RELEASE_PAUSE = 217,
    RPA_PARKING_AND_OBSTACLE_ON_PATH_PAUSE = 218,
    RPA_PARKING_GESTURE_PAUSE_TIMEOUT = 219,
    RPA_PARKING_PHONE_SOC_LOW_TIMEOUT = 220,
    RPA_NO_PRKOUT_SLOT_SELECT_TIMEOUT = 221,
    RPA_BRAKEPEDAL_RELEASE_TIMEOUT = 222,
    RPA_OBSTACLE_ON_PATH_PAUSE_TIMEOUT = 223,
    RPA_PARKING_AND_PHONE_DISTANCE_PAUSE = 224,
    RPA_PHONE_DISTANCE_PAUSE_TIMEOUT = 225,
    POC_INVALID_DIRECTION = 226,
    APA_COLLISION_WARNING = 227,
    BUT_NO_ROUTE_INFORMATION_INVALID = 228,
    BUT_DOOR_OPEN_INVALID = 229,
    BUT_DRIVER_SEAT_BELT_BUCKLED_INVALID = 230,
    BUT_FOLD_IN_MIRROR_INVALID = 231,
    BUT_OVERSPEED_INVALID = 232,
    BUT_NOT_BRAKE_INVALID = 233,
    BUT_GEAR_NOT_P_INVALID = 234,
    BUT_FREE_GEAR_AND_STEERING_WHEEL = 235,
    BUT_NOTICE_ENVIRONMENT = 236,
    BUT_BRAKE_PAUSE = 237,
    BUT_OBSTACLE_ON_PATH_PAUSE = 238,
    BUT_PAUSE_TIMEOUT = 239,
    BUT_DRIVER_SEAT_BELT_BUCKLED_ABORT = 240,
    BUT_DOOR_OPEN_ABORT = 241,
    BUT_DRIVE_EPB_ABORT = 242,
    BUT_GEAR_INTERFER_ABORT = 243,
    BUT_STEERING_WHEEL_INTERFER_ABORT = 244,
    BUT_DRIVE_ACC_PEDAL_INTERFER_ABORT = 245,
    BUT_SYSTEM_ERROR_ABORT = 246,
    BUT_LAST_THREE_METERS = 247,
    BUT_REVERSE_SUCCESS = 248,
    PRK_ASSIST_ABRT_ABNORMAL_VEH_SPEED = 249,
    PRK_ASSIST_ABRT_INVALID_BRAKE_PEDAL = 250,
    PRK_ASSIST_ABRT_FOUR_DOOR_OPEN = 251,
    PRK_MIRROR_UNFOLDED = 252,
    PRK_ASSIST_ABRT_BT_DISCONNECT_TIMEOUT = 253,
    BUT_BRAKE_RESUME = 254,
    BUT_OBSTACLE_ON_PATH_RESUME = 255,
    ABRT_ERROR_BRAKE_PEDAL = 256,
    BUT_SYSTEM_FAILURE = 257,
    PRK_Adjust_Parking_Space = 258,
    PRK_Not_Enough_Parking_Space = 259,
    OBSTACLE_ON_PATH_RESUME = 260,
    SRCH_ASSOCIATE_SYSTEM_ERROR = 261,
    SRCH_NOT_MET_ENVIRONMENT_CONDITION = 262,
    SRCH_DRIVE_MODE_NOT_SUPPORT = 263,
    SRCH_ASSOCIATE_SYS_FUNCTION_ACTIVE = 264,
    SRCH_PARK_ASSIT_SYS_ERROR = 265,
    SRCH_VEHICLE_COLLISION = 266,
    PRK_SPACE_LIMITED = 267,
    PRK_ASSOCIATE_SYSTEM_ERROR = 268,
    PRK_VEHICLE_BLOCKED = 269,
    PRK_NOT_MET_ENVIRONMENT_CONDITION = 270,
    PRK_DRIVE_MODE_NOT_SUPPORT = 271,
    PRK_PARK_ASSIT_SYS_ERROR = 272,
    PRK_VEHICLE_COLLISION = 273,
    PRK_OUT_SPACE_LIMITED = 274,
    CALCULATING_PATH = 275,
    PRK_ASSIST_ABRT_DRV_TIRE_PRESSURE_LOW = 276,
    PRK_ASSIST_ABRT_DRV_LIGHTS = 277,
    PRK_ASSIST_ABRT_SPACE_OCCUPIED = 278,
    PRK_MAUNAL_FUNCTION_ENABLE_STP_CAR = 279,
    PRK_MAUNAL_NOT_ENOUGH_PARKING_SPACE = 280,
    PRK_MAUNAL_STP_CAR = 281,
    BUT_START_WITH_STANDSTILL = 282,
    BUT_START_NOT_STANDSTILL = 283,
    BUT_DOOR_PAUSE = 284,
    BUT_DOOR_RESUME = 285,
    BUT_BELT_PAUSE = 286,
    BUT_BELT_RESUME = 287,
    BUT_VEH_CRASHED_ABORT = 288,
    BUT_SLOPE_OVERFLOW_ABORT = 289,
    BUT_LIGHTMODE_INTERFER_ABORT = 290,
    BUT_WAIT_BUTTON_PRESS = 291,
    BUT_OVER_SPEED_ABORT = 292,
    BUT_OVER_PAUSETIMES_ABORT = 293,
    SRCH_PRK_TIRE_PRESSURE_LOW = 294,
    SRCH_PRK_SLOPE_EXCEEDED = 295,
    BUT_PARK_ASSIT_SYS_ERROR_ABORT = 296,
    BUT_ASSOCIATE_SYS_ERROR_ABORT = 297,
    BUT_TIRE_PRESSURE_LOW_ABORT = 298,
    BUT_SPACE_LIMITED_ABORT = 299,
    BUT_VEHICLE_BLOCKED_ABORT = 300,
    BUT_DRIVE_MODE_ABORT = 301,
    BUT_ENVIRONMENT_CONDITION_ABORT = 302,
    BUT_HOOD_PAUSE = 303,
    BUT_HOOD_RESUME = 304,
    BUT_TRUNK_PAUSE = 305,
    BUT_TRUNK_RESUME = 306,
    BUT_ASSOCIATE_SYS_ACTIVE_ABORT = 307,
    FOLD_MIRROR_SEARCH = 308,
    CHARGE_SEARCH = 309,
    PRK_NARROW_PARKING_SPACE = 310,
    PRK_PAY_ATTENTION_REAR_SPACE = 311,
    PRK_ASSIST_ABRT_DRV_DOOR_OPEN = 312,
    PRK_ASSIST_ABRT_DRV_HOOD_OPEN = 313,
    PRK_ASSIST_ABRT_CHARGE = 314,
    NOT_MET_ENVIRONMENT_CONDITION_PAUSE = 315,
    BUT_HOOD_OPEN_ABORT = 316,
    BUT_CHARGE_ABORT = 317,
    BUT_NOT_MET_ENVIRONMENT_CONDITION_PAUSE = 318,
    NOT_MET_ENVIRONMENT_CONDITION_PAUSE_TIMEOUT = 319,
    RPA_NOT_MET_ENVIRONMENT_CONDITION_PAUSE_TIMEOUT = 320,
    SRCH_VCU_ERROR = 321,
    APA_DYNAMIC_OBJECTS_COLLISION_WARNING = 322,
    SRCH_VOLTAGE_LOW = 323,
    SRCH_SPEED_EXCEEDED = 324,
    SRCH_IPOLITE_SWITCH_ON = 325,
    SRCH_CAMERA_BLOCKED = 326,
    BUT_TURN_LEFT = 327,
    BUT_TURN_RIGHT = 328,
    BUT_SLOPE_OVERFLOW_INVALID = 329,
    BUT_NOT_STANDSTILL_INVALID = 330,
    VOLTAGE_LOW = 331,
    BUT_DRIVE_MODE_INVALID = 332,
    ABORT_CAR_MODE = 333,
    BUT_POWERLOW_ABORT = 334,
    BUT_CARMODE_ABORT = 335,
};
enum class EHmiMsgDispAnotherRq
{
    NONE = 0,
    DRIVE_FORWARD = 1,
    DRIVE_BACKWARD = 2,
    DRIVE_ON = 3,
    PARK_FINISHED = 4,
};
enum class EHmiMsgDispTypeRq
{
    NORMAL = 0,
    PAUSE = 1,
    RESUME = 2,
    ABORT = 3,
    FAILURE = 4,
};
enum class EParkSoundRq
{
    NO_SOUND = 0,
    SOUND1 = 1,
    SOUND2 = 2,
};
enum class EParkSlotSide
{
    NOSIDE = 0,
    LEFTSIDE = 1,
    RIGHTSIDE = 2,
    BOTHSIDES = 3,
};
enum class EParkSlotType
{
    NONE = 0,
    PARALLEL = 1,
    CROSS = 2,
    DIAGONAL = 3,
};
enum class EParkDir
{
    NO = 0,
    FRW = 1,
    BCKW = 2,
};
enum class EParkSlotParkable
{
    NONE = 0,
    UNSUITABLE = 1,
    OK = 2,
    RESERVE = 3,
};
struct CParkPocPreScanSlotInfo
{
    zos::uint16_t parkSlotID;
    EParkSlotSide parkSlotSide;
    EParkSlotType parkSlotType;
    EParkDir parkSlotDir;
    EParkSlotParkable parkAble;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CParkPocPreScanSlotInfo, parkSlotID, parkSlotSide, parkSlotType, parkSlotDir, parkAble)

const zos::uint8_t PocPreScanSlotMAx = 8;
using PocPreScanSlotArray=zos::Array<CParkPocPreScanSlotInfo, PocPreScanSlotMAx>;
struct CParkPocPreScanDisp
{
    PocPreScanSlotArray m_pocPreScanSlotInfo;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CParkPocPreScanDisp, m_pocPreScanSlotInfo)

enum class EVisuOpMd
{
    APP_OFF = 0,
    PTS_POPUP = 1,
    SPEED2HIGH = 2,
    NORM_VIEWS = 3,
    MAN_SELN_VEH_DRVG_SPEED2HIGH = 4,
    MAN_SELN_VEH_DRVG_SRCH = 5,
    MAN_SELN_PARK_IN = 6,
    MAN_SELN_PARK_OUT = 7,
    MAN_ACTV = 8,
    MAN_FNSHD = 9,
    MAN_CANCEL = 10,
    RPA = 11,
    HPA_TRAINING = 12,
    AVP = 13,
    MAN_POC_HNDVR = 14,
    HPA = 15,
    HPA_GUIDANCE = 16,
    MAN_SELN_VEH_DRVG_APC_NOT_READY = 17,
    HIT_ANGLE_CTRL = 18,
    HIT_PULL_STRAIGHT = 19,
    HIT_LEFT_TURN = 20,
    HIT_RIGHT_TURN = 21,
    HIT_PASSIVE = 22,
    HIT_ACTIVE = 23,
    HIT_READY = 24,
    HIT_CANCEL = 25,
    MAN_SELN_PARK_OUT_VEH_MOVING = 26,
    MAN_NOT_READY = 27,
    RPA_EXPL_MD_CNFMD = 28,
    RPA_TAKE_OVER = 29,
    MAN_SELN_PARK_IN_MANUAL = 30,
    BUT_INIT = 31,
    BUT_NO_TRACK = 32,
    BUT_WAIT_GEAR = 33,
    BUT_BUT_INIT_FINISHED = 34,
    BUT_PREPARE = 35,
    MENU = 36,
};
enum class EAPA2IHUAudioRq
{
    AUDIO_DISABLE = 0,
    PARKINGSPACE_AVAILABLE = 1,
    AUDIO_SLEEP = 2,
};
struct CHminotifierOutput
{
    zos::uint64_t m_timestamp;
    zos::uint32_t m_msgDispTimeRq;
    EHminotifierRq m_msgDispRq;
    EHmiMsgDispAnotherRq m_msgDispAnotherRq;
    EHmiMsgDispTypeRq m_msgDispTypeRq;
    EParkSoundRq m_parkSoundRq;
    EVisuOpMd m_hmiVisuOpMd;
    EAPA2IHUAudioRq m_apa2ihuaudiorq;
    CParkPocPreScanDisp m_parkPocPreScanDisp;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CHminotifierOutput, m_timestamp, m_msgDispTimeRq, m_msgDispRq, m_msgDispAnotherRq, m_msgDispTypeRq, m_parkSoundRq, m_hmiVisuOpMd, m_apa2ihuaudiorq, m_parkPocPreScanDisp)

}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::parkapp::CParkPocPreScanSlotInfo, StdLayoutTag>
{
    using ValueType = ::zos::parkapp::CParkPocPreScanSlotInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::CParkPocPreScanSlotInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::parkapp::CParkPocPreScanSlotInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::CParkPocPreScanDisp, StdLayoutTag>
{
    using ValueType = ::zos::parkapp::CParkPocPreScanDisp;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::CParkPocPreScanDisp, StdLayoutTruncTag>
{
    using ValueType = ::zos::parkapp::CParkPocPreScanDisp;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::CHminotifierOutput, StdLayoutTag>
{
    using ValueType = ::zos::parkapp::CHminotifierOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::CHminotifierOutput, StdLayoutTruncTag>
{
    using ValueType = ::zos::parkapp::CHminotifierOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
