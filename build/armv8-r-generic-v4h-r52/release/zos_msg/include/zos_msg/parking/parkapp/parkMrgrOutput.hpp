#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>

#include <zos_msg/common/common.hpp>
namespace zos
{
namespace parkapp
{
struct CCoordPoint_st
{
    zos::float32_t x_f32;
    zos::float32_t y_f32;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CCoordPoint_st, x_f32, y_f32)

struct CCoordAngle_st
{
    zos::float32_t angle_f32;
    zos::float32_t sin_f32;
    zos::float32_t cos_f32;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CCoordAngle_st, angle_f32, sin_f32, cos_f32)

struct CNewCoordOrigin_st
{
    CCoordPoint_st p_st;
    CCoordAngle_st phi_st;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CNewCoordOrigin_st, p_st, phi_st)

enum class ESearchFunctionMode
{
    Off = 0,
    Inactive = 1,
    Functional_PSX = 2,
    Functional_POC = 3,
    Functional_PSX_POC = 4,
    Stdby = 5,
    Functional_CbP = 6,
};
enum class EGuidanceFunctionMode
{
    Off = 0,
    Inactive = 1,
    Active = 2,
    Stdby = 3,
};
enum class EParkingFunction
{
    NONE = 0,
    PSC = 1,
    POC = 2,
    CPSC_BI = 3,
    CPSC_FO = 4,
    CPSC_FI = 5,
    CPSC_BO = 6,
    DPSC_FO = 7,
    DPSC_BO = 8,
};
enum class EPocSide
{
    None = 0,
    Left = 1,
    Right = 2,
};
enum class ECrossPocSide
{
    None = 0,
    TurnLeftInFirstMove = 1,
    TurnRigtInFirstMove = 2,
    Straight = 3,
};
enum class EParkCmd
{
    Standby_Cmd = 0,
    Search_Cmd = 1,
    Active_Cmd = 2,
    Pause_Cmd = 3,
    Finish_Cmd = 4,
    Abort_Cmd = 5,
};
enum class EParkType
{
    None_Type = 0,
    Apa_Type = 1,
    Hpa_Type = 2,
};
enum class EParkPsType
{
    Normal_Type = 0,
    Mansel_Type = 1,
    Virtul_Type = 2,
};
enum class EParkMode
{
    ParkIdle = 0,
    ParkIn = 1,
    ParkOut = 2,
};
struct CPmaCtlInfo
{
    zos::uint16_t m_PSID;
    ESearchFunctionMode m_searchFunctionMode;
    EGuidanceFunctionMode m_guidanceFunctionMode;
    EParkingFunction m_parkingFunction;
    zos::bool_t m_triggerInitManeuver;
    CNewCoordOrigin_st m_newCoordOrigin;
    zos::uint8_t m_resetDrivenDistance;
    EPocSide m_PocSide;
    ECrossPocSide m_CrossPocSide;
    EParkCmd m_ctlParkCmd;
    EParkType m_ctlParkType;
    EParkPsType m_ctlParkSlotType;
    EParkMode m_ctlParkMode;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CPmaCtlInfo, m_PSID, m_searchFunctionMode, m_guidanceFunctionMode, m_parkingFunction, m_triggerInitManeuver, m_newCoordOrigin, m_resetDrivenDistance, m_PocSide, m_CrossPocSide, m_ctlParkCmd, m_ctlParkType, m_ctlParkSlotType, m_ctlParkMode)

struct CPmactlMrgrOutput
{
    zos::uint64_t m_timestamp;
    CPmaCtlInfo m_ctlInfo;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CPmactlMrgrOutput, m_timestamp, m_ctlInfo)

}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::parkapp::CCoordPoint_st, StdLayoutTag>
{
    using ValueType = ::zos::parkapp::CCoordPoint_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::CCoordPoint_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::parkapp::CCoordPoint_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::CCoordAngle_st, StdLayoutTag>
{
    using ValueType = ::zos::parkapp::CCoordAngle_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::CCoordAngle_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::parkapp::CCoordAngle_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::CNewCoordOrigin_st, StdLayoutTag>
{
    using ValueType = ::zos::parkapp::CNewCoordOrigin_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::CNewCoordOrigin_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::parkapp::CNewCoordOrigin_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::CPmaCtlInfo, StdLayoutTag>
{
    using ValueType = ::zos::parkapp::CPmaCtlInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::CPmaCtlInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::parkapp::CPmaCtlInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::CPmactlMrgrOutput, StdLayoutTag>
{
    using ValueType = ::zos::parkapp::CPmactlMrgrOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::parkapp::CPmactlMrgrOutput, StdLayoutTruncTag>
{
    using ValueType = ::zos::parkapp::CPmactlMrgrOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
