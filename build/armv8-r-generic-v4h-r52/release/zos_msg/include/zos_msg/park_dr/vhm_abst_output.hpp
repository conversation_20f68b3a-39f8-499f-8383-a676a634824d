#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>

#include <zos_msg/common/common.hpp>
#include <zos_msg/common/timestamp.hpp>
#include <zos_msg/geometry/pose.hpp>
#include <zos_msg/numerics/vector.hpp>
#include <zos_msg/numerics/matrix.hpp>
namespace zos
{
namespace park_dr
{
enum class EStatus
{
    Good = 0,
    NotGood = 1,
    PositionInactive = 2,
    OrentationInactive = 3,
    LinearVelocityInactive = 4,
    AngularVelocityInactive = 5,
    AccelerationInactive = 6,
    Unknown = 255,
};
enum class EPoseType
{
    EOdoTypeUnknow = 0,
    EOdoTypeXYYaw = 1,
    EOdoTypeXYZRollPitchYaw = 2,
};
enum class EPrecision
{
    Unknown = 0,
    LOW = 1,
    Medium = 2,
    High = 3,
};
enum class ESource
{
    None = 0,
    WHEEL = 1,
    YRS = 2,
    VISION = 3,
    GNSS = 4,
    IMU = 5,
    RTK = 6,
    RADAR = 7,
    LIDAR = 8,
    Unknown = 255,
};
enum class EInteralState
{
    NONE = 0,
    TimeStampInvalid = 1,
    StateValueInvalid = 2,
    StateFejInvalid = 3,
    WheelSpeedLostError = 4,
    WheelPulseLostError = 5,
    WheelAngleLostError = 6,
    ImuLostError = 7,
    YawRateLostError = 8,
    WheelSpeedInvalid = 9,
    WheelPulseInvalid = 10,
    WheelAngleInvalid = 11,
    ImuGyroInvalid = 12,
    ImuAccInvalid = 13,
    YawRateInvalid = 14,
    WheelSpeedLostWarning = 15,
    WheelPulseLostWarning = 16,
    WheelAngleLostWarning = 17,
    ImuLostWarning = 18,
    YawRateLostWarning = 19,
    GnssLostError = 20,
    GnssLostWarning = 21,
    GnssInvalid = 22,
    VisualLostError = 23,
    VisualLostWarning = 24,
    VisualLostInvalid = 25,
    RTKLostError = 26,
    RTKLostWarning = 27,
    RTKLostInvalid = 28,
    LIDARLostError = 29,
    LIDARLostWarning = 30,
    LIDARLostInvalid = 31,
};
enum class EDrivingDirection
{
    FORWARD = 0,
    BACKWARD = 1,
    UNKNOWN = 2,
    STANDSTILL = 3,
};
enum class EVhmUpdateState
{
    INIT = 0,
    UPDATED = 1,
    NOT_UPDATED = 2,
    REINIT = 3,
};
enum class EVehMoveDir
{
    FORWARD = 0,
    BACKWARD = 1,
    UNKNOWN = 2,
    STANDSTILL = 3,
};
struct CVehRec_st
{
    zos::float32_t m_halfWidth;
    zos::float32_t m_frontLength;
    zos::float32_t m_rearLength;
    zos::float32_t m_frontCornerRadius;
    zos::float32_t m_rearCornerRadius;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CVehRec_st, m_halfWidth, m_frontLength, m_rearLength, m_frontCornerRadius, m_rearCornerRadius)

struct CVhmAbstOutput
{
    zos::common::Timestamp m_timeStamp;
    zos::common::Coordinate m_coord;
    zos::common::Coordinate m_childCoord;
    EStatus m_status;
    EPoseType m_poseType;
    EPrecision m_precision;
    zos::uint64_t m_maskSource;
    zos::uint64_t m_maskInteralState;
    zos::float32_t m_velocity;
    zos::float32_t m_velocityX;
    zos::float32_t m_velocityY;
    zos::float32_t m_velocityZ;
    zos::float32_t m_yawAngleRate;
    zos::float32_t m_kappaRaw;
    zos::float32_t m_steeringAngleRear;
    zos::float32_t m_vehLongitudinalAcceleration;
    zos::float32_t m_vehLateralAcceleration;
    EVhmUpdateState m_odometryUpdateState;
    zos::float32_t m_curOdometry_X;
    zos::float32_t m_curOdometry_Y;
    zos::float32_t m_curOdometry_Z;
    zos::float32_t m_curOdometry_RollAngle;
    zos::float32_t m_curOdometry_PitchAngle;
    zos::float32_t m_curOdometry_YawAngle;
    zos::int32_t m_signedDrivenDist;
    zos::uint32_t m_drivenDistRaw;
    zos::float32_t m_theta;
    zos::uint8_t m_drivingDirection_e;
    zos::int8_t m_numberOfRotations;
    EVehMoveDir m_vehMoveDir;
    EVehMoveDir m_vehDriveDir;
    CVehRec_st m_vehRect;
    zos::geometry::Pose3d m_pose;
    zos::numerics::Matrix66d m_poseCovariance;
    zos::numerics::Matrix33d m_velocityCovariance;
    zos::numerics::Vector3d m_angularVelocity;
    zos::numerics::Vector3d m_linearAcceleration;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CVhmAbstOutput, m_timeStamp, m_coord, m_childCoord, m_status, m_poseType, m_precision, m_maskSource, m_maskInteralState, m_velocity, m_velocityX, m_velocityY, m_velocityZ, m_yawAngleRate, m_kappaRaw, m_steeringAngleRear, m_vehLongitudinalAcceleration, m_vehLateralAcceleration, m_odometryUpdateState, m_curOdometry_X, m_curOdometry_Y, m_curOdometry_Z, m_curOdometry_RollAngle, m_curOdometry_PitchAngle, m_curOdometry_YawAngle, m_signedDrivenDist, m_drivenDistRaw, m_theta, m_drivingDirection_e, m_numberOfRotations, m_vehMoveDir, m_vehDriveDir, m_vehRect, m_pose, m_poseCovariance, m_velocityCovariance, m_angularVelocity, m_linearAcceleration)

}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::park_dr::CVehRec_st, StdLayoutTag>
{
    using ValueType = ::zos::park_dr::CVehRec_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::park_dr::CVehRec_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::park_dr::CVehRec_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::park_dr::CVhmAbstOutput, StdLayoutTag>
{
    using ValueType = ::zos::park_dr::CVhmAbstOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::park_dr::CVhmAbstOutput, StdLayoutTruncTag>
{
    using ValueType = ::zos::park_dr::CVhmAbstOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
