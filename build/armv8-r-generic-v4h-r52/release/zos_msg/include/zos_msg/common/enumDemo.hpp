#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>

namespace zos
{
namespace common
{
enum class EEpsEndAngleCtrlReason
{
    ANG_CTL_END_RSN_INVALID = 1,
    ANG_CTL_END_RSN_NORMAL_OPERATION = 2,
    ANG_CTL_END_RSN_OVER_SPEED = 3,
    ANG_CTL_END_RSN_DRIVER_INTERFERENCE = 4,
    ANG_CTL_END_RSN_ABNORMAL_CAN_INPUT = 5,
    ANG_CTL_END_RSN_EXCESS_ANG_DEVIATION = 6,
    ANG_CTL_END_RSN_EPS_FAILURE = 7,
    ANG_CTL_END_RSN_EXCESS_TARGET_ANG_AREA = 8,
    ANG_CTL_END_RSN_EXCESS_LIMIT_ANG_GRADIENT = 9,
    ANG_CTL_END_RSN_RESERVED1 = 10,
};
struct enumDemo
{
    zos::int16_t testDate1;
    zos::int16_t testDate2;
    EEpsEndAngleCtrlReason CtrlReason;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(enumDemo, testDate1, testDate2, CtrlReason)

}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::common::enumDemo, StdLayoutTag>
{
    using ValueType = ::zos::common::enumDemo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::common::enumDemo, StdLayoutTruncTag>
{
    using ValueType = ::zos::common::enumDemo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
