#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/container/vector.hpp>
#include <basic_types/types.hpp>
#include <basic_types/container/array.hpp>

namespace zos
{
namespace numerics
{
using Vectord=zos::FixedVector<zos::float64_t, 128>;
using Vector2d=zos::Array<zos::float64_t, 2>;
using Vector3d=zos::Array<zos::float64_t, 3>;
using Vector4d=zos::Array<zos::float64_t, 4>;
using Vector5d=zos::Array<zos::float64_t, 5>;
using Vector6d=zos::Array<zos::float64_t, 6>;
using Vector7d=zos::Array<zos::float64_t, 7>;
using Vector8d=zos::Array<zos::float64_t, 8>;
using Vector9d=zos::Array<zos::float64_t, 9>;
using Vectorf=zos::FixedVector<zos::float32_t, 128>;
using Vector2f=zos::Array<zos::float32_t, 2>;
using Vector3f=zos::Array<zos::float32_t, 3>;
using Vector4f=zos::Array<zos::float32_t, 4>;
using Vector5f=zos::Array<zos::float32_t, 5>;
using Vector6f=zos::Array<zos::float32_t, 6>;
using Vector7f=zos::Array<zos::float32_t, 7>;
using Vector8f=zos::Array<zos::float32_t, 8>;
using Vector9f=zos::Array<zos::float32_t, 9>;
using Vector13f=zos::Array<zos::float32_t, 13>;
using Vector2i=zos::Array<zos::int32_t, 2>;
}
}
namespace zos
{
namespace serialization
{
}
}
