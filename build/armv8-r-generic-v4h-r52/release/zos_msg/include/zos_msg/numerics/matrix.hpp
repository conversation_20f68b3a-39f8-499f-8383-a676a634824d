#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/container/vector.hpp>
#include <basic_types/types.hpp>
#include <basic_types/container/array.hpp>

namespace zos
{
namespace numerics
{
using MatrixdData=zos::FixedVector<zos::float64_t, 128>;
struct Matrixd
{
    zos::int32_t rows;
    zos::int32_t cols;
    MatrixdData data;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(Matrixd, rows, cols, data)

using Matrix22d=zos::Array<zos::Array<zos::float64_t, 2>, 2>;
using Matrix33d=zos::Array<zos::Array<zos::float64_t, 3>, 3>;
using Matrix44d=zos::Array<zos::Array<zos::float64_t, 4>, 4>;
using Matrix55d=zos::Array<zos::Array<zos::float64_t, 5>, 5>;
using Matrix66d=zos::Array<zos::Array<zos::float64_t, 6>, 6>;
using MatrixfData=zos::FixedVector<zos::float32_t, 128>;
struct Matrixf
{
    zos::int32_t rows;
    zos::int32_t cols;
    MatrixfData data;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(Matrixf, rows, cols, data)

using Matrix22f=zos::Array<zos::Array<zos::float32_t, 2>, 2>;
using Matrix33f=zos::Array<zos::Array<zos::float32_t, 3>, 3>;
using Matrix44f=zos::Array<zos::Array<zos::float32_t, 4>, 4>;
using Matrix55f=zos::Array<zos::Array<zos::float32_t, 5>, 5>;
using Matrix66f=zos::Array<zos::Array<zos::float32_t, 6>, 6>;
}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::numerics::Matrixd, StdLayoutTag>
{
    using ValueType = ::zos::numerics::Matrixd;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::numerics::Matrixd, StdLayoutTruncTag>
{
    using ValueType = ::zos::numerics::Matrixd;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, data) + sizeof(uint32_t);
        size += sample.data.size() * sizeof(decltype(sample.data)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,data));
        uint32_t des_size = offsetof(ValueType,data) + element_size * sizeof(decltype(sample.data)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::numerics::Matrixf, StdLayoutTag>
{
    using ValueType = ::zos::numerics::Matrixf;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::numerics::Matrixf, StdLayoutTruncTag>
{
    using ValueType = ::zos::numerics::Matrixf;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, data) + sizeof(uint32_t);
        size += sample.data.size() * sizeof(decltype(sample.data)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,data));
        uint32_t des_size = offsetof(ValueType,data) + element_size * sizeof(decltype(sample.data)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
