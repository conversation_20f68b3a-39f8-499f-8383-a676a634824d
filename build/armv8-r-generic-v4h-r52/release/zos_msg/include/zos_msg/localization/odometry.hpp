#pragma once
#include <basic_types/serialization/serialization_traits.hpp>

#include <zos_msg/common/common.hpp>
#include <zos_msg/common/timestamp.hpp>
#include <zos_msg/geometry/pose.hpp>
#include <zos_msg/numerics/vector.hpp>
#include <zos_msg/numerics/matrix.hpp>
namespace zos
{
namespace localization
{
enum class OdometrySource
{
    ODOMETRY_SOURCE_GNSS = 0,
    ODOMETRY_SOURCE_IMU = 1,
    ODOMETRY_SOURCE_VISION = 2,
    ODOMETRY_SOURCE_WHEEL = 4,
    ODOMETRY_SOURCE_RADAR = 8,
    ODOMETRY_SOURCE_LIDAR = 16,
    ODOMETRY_SOURCE_SEMANTIC = 32,
    ODOMETRY_SOURCE_HD_MAP = 64,
    ODOMETRY_SOURCE_UNKNOWN = 65535,
};
enum class OdometryStatus
{
    ALL_GOOD = 0,
    NO_VALID_SOLUTION = 1,
    POSITION_INACTIVE = 2,
    ORIENTATION_INACTIVE = 4,
    LINEAR_VELOCITY_INACTIVE = 8,
    ANGULAR_VELOCITY_INACTIVE = 16,
    LINEAR_ACCELERATION_INACTIVE = 32,
};
enum class OdometryPrecision
{
    HIGH_PRECISION = 0,
    LOW_PRECISION = 1,
};
struct Odometryf
{
    zos::common::Timestamp timestamp;
    zos::common::Coordinate coord;
    zos::common::Coordinate child_coord;
    OdometryStatus status;
    OdometrySource source;
    OdometryPrecision precision;
    zos::geometry::Pose3f pose;
    zos::numerics::Matrix66f pose_covariance;
    zos::numerics::Vector3f velocity;
    zos::numerics::Matrix33f velocity_covariance;
    zos::numerics::Vector3f angular_velocity;
    zos::numerics::Vector3f linear_acceleration;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(Odometryf, timestamp, coord, child_coord, status, source, precision, pose, pose_covariance, velocity, velocity_covariance, angular_velocity, linear_acceleration)

struct Odometryd
{
    zos::common::Timestamp timestamp;
    zos::common::Coordinate coord;
    zos::common::Coordinate child_coord;
    OdometryStatus status;
    OdometrySource source;
    OdometryPrecision precision;
    zos::geometry::Pose3d pose;
    zos::numerics::Matrix66d pose_covariance;
    zos::numerics::Vector3d velocity;
    zos::numerics::Matrix33d velocity_covariance;
    zos::numerics::Vector3d angular_velocity;
    zos::numerics::Vector3d linear_acceleration;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(Odometryd, timestamp, coord, child_coord, status, source, precision, pose, pose_covariance, velocity, velocity_covariance, angular_velocity, linear_acceleration)

}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::localization::Odometryf, StdLayoutTag>
{
    using ValueType = ::zos::localization::Odometryf;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::localization::Odometryf, StdLayoutTruncTag>
{
    using ValueType = ::zos::localization::Odometryf;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::localization::Odometryd, StdLayoutTag>
{
    using ValueType = ::zos::localization::Odometryd;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::localization::Odometryd, StdLayoutTruncTag>
{
    using ValueType = ::zos::localization::Odometryd;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
