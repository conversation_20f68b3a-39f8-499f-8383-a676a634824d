#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>

#include <zos_msg/common/common.hpp>
namespace evm
{
const zos::uint8_t EvmPf_FidsSize_cu8 = 30;
struct EvmPf_FuncPermission
{
    zos::uint8_t m_FiMFID_ToAcoreCluster1;
    zos::uint8_t m_FiMFID_ToAcoreCluster2;
    zos::uint8_t m_FiMFID_ToAcoreCluster3;
    zos::uint8_t m_FiMFID_ToAcoreCluster4;
    zos::uint8_t m_FiMFID_ToAcoreCluster5;
    zos::uint8_t m_FiMFID_ToAcoreCluster6;
    zos::uint8_t m_FiMFID_ToAcoreCluster7;
    zos::uint8_t m_FiMFID_ToAcoreCluster8;
    zos::uint8_t m_FiMFID_ToAcoreCluster9;
    zos::uint8_t m_FiMFID_ToAcoreCluster10;
    zos::uint8_t m_FiMFID_ToAcoreCluster11;
    zos::uint8_t m_FiMFID_ToAcoreCluster12;
    zos::uint8_t m_FiMFID_ToAcoreCluster13;
    zos::uint8_t m_FiMFID_ToAcoreCluster14;
    zos::uint8_t m_FiMFID_ToAcoreCluster15;
    zos::uint8_t m_FiMFID_ToAcoreCluster16;
    zos::uint8_t m_FiMFID_ToAcoreCluster17;
    zos::uint8_t m_FiMFID_ToAcoreCluster18;
    zos::uint8_t m_FiMFID_ToAcoreCluster19;
    zos::uint8_t m_FiMFID_ToAcoreCluster20;
    zos::uint8_t m_FiMFID_ToAcoreCluster21;
    zos::uint8_t m_FiMFID_ToAcoreCluster22;
    zos::uint8_t m_FiMFID_ToAcoreCluster23;
    zos::uint8_t m_FiMFID_ToAcoreCluster24;
    zos::uint8_t m_FiMFID_ToAcoreCluster25;
    zos::uint8_t m_FiMFID_ToAcoreCluster26;
    zos::uint8_t m_FiMFID_ToAcoreCluster27;
    zos::uint8_t m_FiMFID_ToAcoreCluster28;
    zos::uint8_t m_FiMFID_ToAcoreCluster29;
    zos::uint8_t m_FiMFID_ToAcoreCluster30;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(EvmPf_FuncPermission, m_FiMFID_ToAcoreCluster1, m_FiMFID_ToAcoreCluster2, m_FiMFID_ToAcoreCluster3, m_FiMFID_ToAcoreCluster4, m_FiMFID_ToAcoreCluster5, m_FiMFID_ToAcoreCluster6, m_FiMFID_ToAcoreCluster7, m_FiMFID_ToAcoreCluster8, m_FiMFID_ToAcoreCluster9, m_FiMFID_ToAcoreCluster10, m_FiMFID_ToAcoreCluster11, m_FiMFID_ToAcoreCluster12, m_FiMFID_ToAcoreCluster13, m_FiMFID_ToAcoreCluster14, m_FiMFID_ToAcoreCluster15, m_FiMFID_ToAcoreCluster16, m_FiMFID_ToAcoreCluster17, m_FiMFID_ToAcoreCluster18, m_FiMFID_ToAcoreCluster19, m_FiMFID_ToAcoreCluster20, m_FiMFID_ToAcoreCluster21, m_FiMFID_ToAcoreCluster22, m_FiMFID_ToAcoreCluster23, m_FiMFID_ToAcoreCluster24, m_FiMFID_ToAcoreCluster25, m_FiMFID_ToAcoreCluster26, m_FiMFID_ToAcoreCluster27, m_FiMFID_ToAcoreCluster28, m_FiMFID_ToAcoreCluster29, m_FiMFID_ToAcoreCluster30)

}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::evm::EvmPf_FuncPermission, StdLayoutTag>
{
    using ValueType = ::evm::EvmPf_FuncPermission;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::evm::EvmPf_FuncPermission, StdLayoutTruncTag>
{
    using ValueType = ::evm::EvmPf_FuncPermission;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
