#pragma once
#include <basic_types/serialization/serialization_traits.hpp>

#include <zos_msg/common/common.hpp>
namespace evm
{
const zos::uint16_t EvmCfg_Max_Number_Event_cu16 = 1608;
const zos::uint16_t EvmCfg_Max_Number_Event_AlignedBit_cu16 = 201;
const zos::uint16_t EvmCfg_Max_Number_EventToMixedFID_Group_cu16 = 1;
const zos::uint16_t EvmCfg_Max_Number_FID_AlignedUint8_cu16 = 4;
}
namespace zos
{
namespace serialization
{
}
}
