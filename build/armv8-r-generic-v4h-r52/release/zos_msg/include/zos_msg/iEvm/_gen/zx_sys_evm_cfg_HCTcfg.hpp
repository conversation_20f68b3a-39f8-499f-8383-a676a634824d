#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/container/array.hpp>
#include <basic_types/types.hpp>

#include <zos_msg/common/common.hpp>
#include <zos_msg/iEvm/zx_sys_evm_interfaces.hpp>
namespace um_cfg
{
const zos::uint8_t HCTcfg_Runnable_EventSize_cu8 = 39;
using HCT_IpcBuffer=zos::Array<zos::uint8_t, HCTcfg_Runnable_EventSize_cu8>;
struct HCTcfg_Runnable_Events
{
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_HCT_M2X_GNSSOpenLoad;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_HCT_M2X_GNSSShortCircuit;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_HCT_M2X_TemperError;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_HCT_M2X_WheelInputError;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_HCT_M2X_GNSSLocationDataError;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_HCT_M2X_INSDataError;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_HCT_M2X_IMUDataError;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_HCT_M2X_HWError;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_HCT_M2X_PowerError;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_HCT_M2X_ComError;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_HCT_M2X_NotInputDataToSensorCenter;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_HCT_Visioni_Calib_7VNotValidCalibFileConfig;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_HCT_Visioni_Calib_4VNotValidCalibFileConfig;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_HCT_Vision_SensorCenterVIOCamInit;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_HCT_Vision_CamBlind_Cam_Front30;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_HCT_Vision_CamBlind_Cam_Front120;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_HCT_Vision_CamBlind_Cam_Rear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_HCT_Vision_CamBlind_Cam_SideRR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_HCT_Vision_CamBlind_Cam_SideRL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_HCT_Vision_CamBlind_Cam_SideFL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_HCT_Vision_CamBlind_Cam_SideFR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_HCT_Vision_CamBlind_AVMRear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_HCT_Vision_CamBlind_AVMLeft;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_HCT_Vision_CamBlind_AVMFront;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_HCT_Vision_CamBlind_AVMRight;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_HCT_Vision_SensorCenterNoImgInput_Cam_Front30;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_HCT_Vision_SensorCenterNoImgInput_Cam_Front120;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_HCT_Vision_SensorCenterNoImgInput_Cam_Rear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_HCT_Vision_SensorCenterNoImgInput_Cam_SideRR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_HCT_Vision_SensorCenterNoImgInput_Cam_SideRL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_HCT_Vision_SensorCenterNoImgInput_Cam_SideFL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_HCT_Vision_SensorCenterNoImgInput_Cam_SideFR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_HCT_Vision_SensorCenterNoImgInput_AVMRear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_HCT_Vision_SensorCenterNoImgInput_AVMLeft;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_HCT_Vision_SensorCenterNoImgInput_AVMFront;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_HCT_Vision_SensorCenterNoImgInput_AVMRight;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_HCT_Can2IPCTimeout;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_HCT_ThirdParty_RTKErrorCode_GGANotUpdate;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_HCT_ThirdParty_RTKErrorCode_ReceivedRTCMFailed;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(HCTcfg_Runnable_Events, m_FAULT_DemEvent_HCT_M2X_GNSSOpenLoad, m_FAULT_DemEvent_HCT_M2X_GNSSShortCircuit, m_FAULT_DemEvent_HCT_M2X_TemperError, m_FAULT_DemEvent_HCT_M2X_WheelInputError, m_FAULT_DemEvent_HCT_M2X_GNSSLocationDataError, m_FAULT_DemEvent_HCT_M2X_INSDataError, m_FAULT_DemEvent_HCT_M2X_IMUDataError, m_FAULT_DemEvent_HCT_M2X_HWError, m_FAULT_DemEvent_HCT_M2X_PowerError, m_FAULT_DemEvent_HCT_M2X_ComError, m_FAULT_DemEvent_HCT_M2X_NotInputDataToSensorCenter, m_FAULT_DemEvent_HCT_Visioni_Calib_7VNotValidCalibFileConfig, m_FAULT_DemEvent_HCT_Visioni_Calib_4VNotValidCalibFileConfig, m_FAULT_DemEvent_HCT_Vision_SensorCenterVIOCamInit, m_FAULT_DemEvent_HCT_Vision_CamBlind_Cam_Front30, m_FAULT_DemEvent_HCT_Vision_CamBlind_Cam_Front120, m_FAULT_DemEvent_HCT_Vision_CamBlind_Cam_Rear, m_FAULT_DemEvent_HCT_Vision_CamBlind_Cam_SideRR, m_FAULT_DemEvent_HCT_Vision_CamBlind_Cam_SideRL, m_FAULT_DemEvent_HCT_Vision_CamBlind_Cam_SideFL, m_FAULT_DemEvent_HCT_Vision_CamBlind_Cam_SideFR, m_FAULT_DemEvent_HCT_Vision_CamBlind_AVMRear, m_FAULT_DemEvent_HCT_Vision_CamBlind_AVMLeft, m_FAULT_DemEvent_HCT_Vision_CamBlind_AVMFront, m_FAULT_DemEvent_HCT_Vision_CamBlind_AVMRight, m_FAULT_DemEvent_HCT_Vision_SensorCenterNoImgInput_Cam_Front30, m_FAULT_DemEvent_HCT_Vision_SensorCenterNoImgInput_Cam_Front120, m_FAULT_DemEvent_HCT_Vision_SensorCenterNoImgInput_Cam_Rear, m_FAULT_DemEvent_HCT_Vision_SensorCenterNoImgInput_Cam_SideRR, m_FAULT_DemEvent_HCT_Vision_SensorCenterNoImgInput_Cam_SideRL, m_FAULT_DemEvent_HCT_Vision_SensorCenterNoImgInput_Cam_SideFL, m_FAULT_DemEvent_HCT_Vision_SensorCenterNoImgInput_Cam_SideFR, m_FAULT_DemEvent_HCT_Vision_SensorCenterNoImgInput_AVMRear, m_FAULT_DemEvent_HCT_Vision_SensorCenterNoImgInput_AVMLeft, m_FAULT_DemEvent_HCT_Vision_SensorCenterNoImgInput_AVMFront, m_FAULT_DemEvent_HCT_Vision_SensorCenterNoImgInput_AVMRight, m_FAULT_DemEvent_HCT_Can2IPCTimeout, m_FAULT_DemEvent_HCT_ThirdParty_RTKErrorCode_GGANotUpdate, m_FAULT_DemEvent_HCT_ThirdParty_RTKErrorCode_ReceivedRTCMFailed)

}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::um_cfg::HCTcfg_Runnable_Events, StdLayoutTag>
{
    using ValueType = ::um_cfg::HCTcfg_Runnable_Events;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::um_cfg::HCTcfg_Runnable_Events, StdLayoutTruncTag>
{
    using ValueType = ::um_cfg::HCTcfg_Runnable_Events;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
