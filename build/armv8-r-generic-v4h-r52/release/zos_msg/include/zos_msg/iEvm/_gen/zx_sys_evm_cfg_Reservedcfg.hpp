#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/container/array.hpp>
#include <basic_types/types.hpp>

#include <zos_msg/common/common.hpp>
#include <zos_msg/iEvm/zx_sys_evm_interfaces.hpp>
namespace um_cfg
{
const zos::uint8_t Reservedcfg_Runnable_EventSize_cu8 = 241;
using Reserved_IpcBuffer=zos::Array<zos::uint8_t, Reservedcfg_Runnable_EventSize_cu8>;
struct Reservedcfg_Runnable_Events
{
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_50;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_51;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_52;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_53;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_54;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_55;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_56;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_57;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_58;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_59;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_60;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_61;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_62;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_63;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_64;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_65;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_66;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_67;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_68;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_69;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_70;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_71;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_72;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_73;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_74;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_75;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_76;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_77;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_78;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_79;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_80;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_81;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_82;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_83;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_84;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_85;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_86;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_87;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_88;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_89;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_90;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_91;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_92;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_93;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_94;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_95;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_96;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_97;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_98;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_99;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_100;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_101;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_102;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_103;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_104;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_105;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_106;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_107;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_108;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_109;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_110;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_111;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_112;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_113;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_114;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_115;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_116;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_117;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_118;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_119;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_120;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_121;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_122;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_123;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_124;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_125;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_126;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Platform_Reserved_127;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_5;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_6;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_7;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_8;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_9;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_10;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_11;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_12;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_13;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_14;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_15;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_16;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_17;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_18;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_19;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_20;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_21;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_22;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_23;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_24;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_25;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_26;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_27;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_28;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_29;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_30;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_31;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_32;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_33;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_34;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_35;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_36;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_37;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_38;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_39;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_40;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_41;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_42;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_43;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_44;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_45;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_46;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_47;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_48;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_49;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_50;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_51;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_52;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_53;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_54;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_55;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_56;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_57;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_58;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_59;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_60;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_61;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_62;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_63;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_64;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_65;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_66;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_67;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_68;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_69;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_70;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_71;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_72;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_73;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_74;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_75;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_76;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_77;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_78;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_79;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_80;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_81;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_82;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_83;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_84;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_85;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_86;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_87;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_88;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_89;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_90;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_91;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_92;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_93;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_94;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_95;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_96;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_97;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_98;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_99;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_100;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_101;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_102;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_103;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_104;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_105;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_106;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_107;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_108;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_109;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_110;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_111;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_112;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_113;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_114;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_115;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_116;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_117;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_118;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_119;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_120;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_121;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_122;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_123;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_124;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_125;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_126;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_127;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_128;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_129;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_130;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_131;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_132;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_133;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_134;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_135;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_136;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_137;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_138;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_139;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_140;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_141;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_142;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_143;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_144;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_145;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_146;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_147;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_148;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_149;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_150;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_151;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_152;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_153;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_154;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_155;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_156;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_157;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_158;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_159;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_160;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_161;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_162;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_163;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_164;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_165;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_166;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_mcu_Reserved_167;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(Reservedcfg_Runnable_Events, m_FAULT_DemEvent_Platform_Reserved_50, m_FAULT_DemEvent_Platform_Reserved_51, m_FAULT_DemEvent_Platform_Reserved_52, m_FAULT_DemEvent_Platform_Reserved_53, m_FAULT_DemEvent_Platform_Reserved_54, m_FAULT_DemEvent_Platform_Reserved_55, m_FAULT_DemEvent_Platform_Reserved_56, m_FAULT_DemEvent_Platform_Reserved_57, m_FAULT_DemEvent_Platform_Reserved_58, m_FAULT_DemEvent_Platform_Reserved_59, m_FAULT_DemEvent_Platform_Reserved_60, m_FAULT_DemEvent_Platform_Reserved_61, m_FAULT_DemEvent_Platform_Reserved_62, m_FAULT_DemEvent_Platform_Reserved_63, m_FAULT_DemEvent_Platform_Reserved_64, m_FAULT_DemEvent_Platform_Reserved_65, m_FAULT_DemEvent_Platform_Reserved_66, m_FAULT_DemEvent_Platform_Reserved_67, m_FAULT_DemEvent_Platform_Reserved_68, m_FAULT_DemEvent_Platform_Reserved_69, m_FAULT_DemEvent_Platform_Reserved_70, m_FAULT_DemEvent_Platform_Reserved_71, m_FAULT_DemEvent_Platform_Reserved_72, m_FAULT_DemEvent_Platform_Reserved_73, m_FAULT_DemEvent_Platform_Reserved_74, m_FAULT_DemEvent_Platform_Reserved_75, m_FAULT_DemEvent_Platform_Reserved_76, m_FAULT_DemEvent_Platform_Reserved_77, m_FAULT_DemEvent_Platform_Reserved_78, m_FAULT_DemEvent_Platform_Reserved_79, m_FAULT_DemEvent_Platform_Reserved_80, m_FAULT_DemEvent_Platform_Reserved_81, m_FAULT_DemEvent_Platform_Reserved_82, m_FAULT_DemEvent_Platform_Reserved_83, m_FAULT_DemEvent_Platform_Reserved_84, m_FAULT_DemEvent_Platform_Reserved_85, m_FAULT_DemEvent_Platform_Reserved_86, m_FAULT_DemEvent_Platform_Reserved_87, m_FAULT_DemEvent_Platform_Reserved_88, m_FAULT_DemEvent_Platform_Reserved_89, m_FAULT_DemEvent_Platform_Reserved_90, m_FAULT_DemEvent_Platform_Reserved_91, m_FAULT_DemEvent_Platform_Reserved_92, m_FAULT_DemEvent_Platform_Reserved_93, m_FAULT_DemEvent_Platform_Reserved_94, m_FAULT_DemEvent_Platform_Reserved_95, m_FAULT_DemEvent_Platform_Reserved_96, m_FAULT_DemEvent_Platform_Reserved_97, m_FAULT_DemEvent_Platform_Reserved_98, m_FAULT_DemEvent_Platform_Reserved_99, m_FAULT_DemEvent_Platform_Reserved_100, m_FAULT_DemEvent_Platform_Reserved_101, m_FAULT_DemEvent_Platform_Reserved_102, m_FAULT_DemEvent_Platform_Reserved_103, m_FAULT_DemEvent_Platform_Reserved_104, m_FAULT_DemEvent_Platform_Reserved_105, m_FAULT_DemEvent_Platform_Reserved_106, m_FAULT_DemEvent_Platform_Reserved_107, m_FAULT_DemEvent_Platform_Reserved_108, m_FAULT_DemEvent_Platform_Reserved_109, m_FAULT_DemEvent_Platform_Reserved_110, m_FAULT_DemEvent_Platform_Reserved_111, m_FAULT_DemEvent_Platform_Reserved_112, m_FAULT_DemEvent_Platform_Reserved_113, m_FAULT_DemEvent_Platform_Reserved_114, m_FAULT_DemEvent_Platform_Reserved_115, m_FAULT_DemEvent_Platform_Reserved_116, m_FAULT_DemEvent_Platform_Reserved_117, m_FAULT_DemEvent_Platform_Reserved_118, m_FAULT_DemEvent_Platform_Reserved_119, m_FAULT_DemEvent_Platform_Reserved_120, m_FAULT_DemEvent_Platform_Reserved_121, m_FAULT_DemEvent_Platform_Reserved_122, m_FAULT_DemEvent_Platform_Reserved_123, m_FAULT_DemEvent_Platform_Reserved_124, m_FAULT_DemEvent_Platform_Reserved_125, m_FAULT_DemEvent_Platform_Reserved_126, m_FAULT_DemEvent_Platform_Reserved_127, m_FAULT_DemEvent_mcu_Reserved_5, m_FAULT_DemEvent_mcu_Reserved_6, m_FAULT_DemEvent_mcu_Reserved_7, m_FAULT_DemEvent_mcu_Reserved_8, m_FAULT_DemEvent_mcu_Reserved_9, m_FAULT_DemEvent_mcu_Reserved_10, m_FAULT_DemEvent_mcu_Reserved_11, m_FAULT_DemEvent_mcu_Reserved_12, m_FAULT_DemEvent_mcu_Reserved_13, m_FAULT_DemEvent_mcu_Reserved_14, m_FAULT_DemEvent_mcu_Reserved_15, m_FAULT_DemEvent_mcu_Reserved_16, m_FAULT_DemEvent_mcu_Reserved_17, m_FAULT_DemEvent_mcu_Reserved_18, m_FAULT_DemEvent_mcu_Reserved_19, m_FAULT_DemEvent_mcu_Reserved_20, m_FAULT_DemEvent_mcu_Reserved_21, m_FAULT_DemEvent_mcu_Reserved_22, m_FAULT_DemEvent_mcu_Reserved_23, m_FAULT_DemEvent_mcu_Reserved_24, m_FAULT_DemEvent_mcu_Reserved_25, m_FAULT_DemEvent_mcu_Reserved_26, m_FAULT_DemEvent_mcu_Reserved_27, m_FAULT_DemEvent_mcu_Reserved_28, m_FAULT_DemEvent_mcu_Reserved_29, m_FAULT_DemEvent_mcu_Reserved_30, m_FAULT_DemEvent_mcu_Reserved_31, m_FAULT_DemEvent_mcu_Reserved_32, m_FAULT_DemEvent_mcu_Reserved_33, m_FAULT_DemEvent_mcu_Reserved_34, m_FAULT_DemEvent_mcu_Reserved_35, m_FAULT_DemEvent_mcu_Reserved_36, m_FAULT_DemEvent_mcu_Reserved_37, m_FAULT_DemEvent_mcu_Reserved_38, m_FAULT_DemEvent_mcu_Reserved_39, m_FAULT_DemEvent_mcu_Reserved_40, m_FAULT_DemEvent_mcu_Reserved_41, m_FAULT_DemEvent_mcu_Reserved_42, m_FAULT_DemEvent_mcu_Reserved_43, m_FAULT_DemEvent_mcu_Reserved_44, m_FAULT_DemEvent_mcu_Reserved_45, m_FAULT_DemEvent_mcu_Reserved_46, m_FAULT_DemEvent_mcu_Reserved_47, m_FAULT_DemEvent_mcu_Reserved_48, m_FAULT_DemEvent_mcu_Reserved_49, m_FAULT_DemEvent_mcu_Reserved_50, m_FAULT_DemEvent_mcu_Reserved_51, m_FAULT_DemEvent_mcu_Reserved_52, m_FAULT_DemEvent_mcu_Reserved_53, m_FAULT_DemEvent_mcu_Reserved_54, m_FAULT_DemEvent_mcu_Reserved_55, m_FAULT_DemEvent_mcu_Reserved_56, m_FAULT_DemEvent_mcu_Reserved_57, m_FAULT_DemEvent_mcu_Reserved_58, m_FAULT_DemEvent_mcu_Reserved_59, m_FAULT_DemEvent_mcu_Reserved_60, m_FAULT_DemEvent_mcu_Reserved_61, m_FAULT_DemEvent_mcu_Reserved_62, m_FAULT_DemEvent_mcu_Reserved_63, m_FAULT_DemEvent_mcu_Reserved_64, m_FAULT_DemEvent_mcu_Reserved_65, m_FAULT_DemEvent_mcu_Reserved_66, m_FAULT_DemEvent_mcu_Reserved_67, m_FAULT_DemEvent_mcu_Reserved_68, m_FAULT_DemEvent_mcu_Reserved_69, m_FAULT_DemEvent_mcu_Reserved_70, m_FAULT_DemEvent_mcu_Reserved_71, m_FAULT_DemEvent_mcu_Reserved_72, m_FAULT_DemEvent_mcu_Reserved_73, m_FAULT_DemEvent_mcu_Reserved_74, m_FAULT_DemEvent_mcu_Reserved_75, m_FAULT_DemEvent_mcu_Reserved_76, m_FAULT_DemEvent_mcu_Reserved_77, m_FAULT_DemEvent_mcu_Reserved_78, m_FAULT_DemEvent_mcu_Reserved_79, m_FAULT_DemEvent_mcu_Reserved_80, m_FAULT_DemEvent_mcu_Reserved_81, m_FAULT_DemEvent_mcu_Reserved_82, m_FAULT_DemEvent_mcu_Reserved_83, m_FAULT_DemEvent_mcu_Reserved_84, m_FAULT_DemEvent_mcu_Reserved_85, m_FAULT_DemEvent_mcu_Reserved_86, m_FAULT_DemEvent_mcu_Reserved_87, m_FAULT_DemEvent_mcu_Reserved_88, m_FAULT_DemEvent_mcu_Reserved_89, m_FAULT_DemEvent_mcu_Reserved_90, m_FAULT_DemEvent_mcu_Reserved_91, m_FAULT_DemEvent_mcu_Reserved_92, m_FAULT_DemEvent_mcu_Reserved_93, m_FAULT_DemEvent_mcu_Reserved_94, m_FAULT_DemEvent_mcu_Reserved_95, m_FAULT_DemEvent_mcu_Reserved_96, m_FAULT_DemEvent_mcu_Reserved_97, m_FAULT_DemEvent_mcu_Reserved_98, m_FAULT_DemEvent_mcu_Reserved_99, m_FAULT_DemEvent_mcu_Reserved_100, m_FAULT_DemEvent_mcu_Reserved_101, m_FAULT_DemEvent_mcu_Reserved_102, m_FAULT_DemEvent_mcu_Reserved_103, m_FAULT_DemEvent_mcu_Reserved_104, m_FAULT_DemEvent_mcu_Reserved_105, m_FAULT_DemEvent_mcu_Reserved_106, m_FAULT_DemEvent_mcu_Reserved_107, m_FAULT_DemEvent_mcu_Reserved_108, m_FAULT_DemEvent_mcu_Reserved_109, m_FAULT_DemEvent_mcu_Reserved_110, m_FAULT_DemEvent_mcu_Reserved_111, m_FAULT_DemEvent_mcu_Reserved_112, m_FAULT_DemEvent_mcu_Reserved_113, m_FAULT_DemEvent_mcu_Reserved_114, m_FAULT_DemEvent_mcu_Reserved_115, m_FAULT_DemEvent_mcu_Reserved_116, m_FAULT_DemEvent_mcu_Reserved_117, m_FAULT_DemEvent_mcu_Reserved_118, m_FAULT_DemEvent_mcu_Reserved_119, m_FAULT_DemEvent_mcu_Reserved_120, m_FAULT_DemEvent_mcu_Reserved_121, m_FAULT_DemEvent_mcu_Reserved_122, m_FAULT_DemEvent_mcu_Reserved_123, m_FAULT_DemEvent_mcu_Reserved_124, m_FAULT_DemEvent_mcu_Reserved_125, m_FAULT_DemEvent_mcu_Reserved_126, m_FAULT_DemEvent_mcu_Reserved_127, m_FAULT_DemEvent_mcu_Reserved_128, m_FAULT_DemEvent_mcu_Reserved_129, m_FAULT_DemEvent_mcu_Reserved_130, m_FAULT_DemEvent_mcu_Reserved_131, m_FAULT_DemEvent_mcu_Reserved_132, m_FAULT_DemEvent_mcu_Reserved_133, m_FAULT_DemEvent_mcu_Reserved_134, m_FAULT_DemEvent_mcu_Reserved_135, m_FAULT_DemEvent_mcu_Reserved_136, m_FAULT_DemEvent_mcu_Reserved_137, m_FAULT_DemEvent_mcu_Reserved_138, m_FAULT_DemEvent_mcu_Reserved_139, m_FAULT_DemEvent_mcu_Reserved_140, m_FAULT_DemEvent_mcu_Reserved_141, m_FAULT_DemEvent_mcu_Reserved_142, m_FAULT_DemEvent_mcu_Reserved_143, m_FAULT_DemEvent_mcu_Reserved_144, m_FAULT_DemEvent_mcu_Reserved_145, m_FAULT_DemEvent_mcu_Reserved_146, m_FAULT_DemEvent_mcu_Reserved_147, m_FAULT_DemEvent_mcu_Reserved_148, m_FAULT_DemEvent_mcu_Reserved_149, m_FAULT_DemEvent_mcu_Reserved_150, m_FAULT_DemEvent_mcu_Reserved_151, m_FAULT_DemEvent_mcu_Reserved_152, m_FAULT_DemEvent_mcu_Reserved_153, m_FAULT_DemEvent_mcu_Reserved_154, m_FAULT_DemEvent_mcu_Reserved_155, m_FAULT_DemEvent_mcu_Reserved_156, m_FAULT_DemEvent_mcu_Reserved_157, m_FAULT_DemEvent_mcu_Reserved_158, m_FAULT_DemEvent_mcu_Reserved_159, m_FAULT_DemEvent_mcu_Reserved_160, m_FAULT_DemEvent_mcu_Reserved_161, m_FAULT_DemEvent_mcu_Reserved_162, m_FAULT_DemEvent_mcu_Reserved_163, m_FAULT_DemEvent_mcu_Reserved_164, m_FAULT_DemEvent_mcu_Reserved_165, m_FAULT_DemEvent_mcu_Reserved_166, m_FAULT_DemEvent_mcu_Reserved_167)

}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::um_cfg::Reservedcfg_Runnable_Events, StdLayoutTag>
{
    using ValueType = ::um_cfg::Reservedcfg_Runnable_Events;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::um_cfg::Reservedcfg_Runnable_Events, StdLayoutTruncTag>
{
    using ValueType = ::um_cfg::Reservedcfg_Runnable_Events;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
