#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/container/array.hpp>
#include <basic_types/types.hpp>

#include <zos_msg/common/common.hpp>
#include <zos_msg/iEvm/zx_sys_evm_interfaces.hpp>
namespace um_cfg
{
const zos::uint8_t Ethcfg_Runnable_EventSize_cu8 = 43;
using Eth_IpcBuffer=zos::Array<zos::uint8_t, Ethcfg_Runnable_EventSize_cu8>;
struct Ethcfg_Runnable_Events
{
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_IBS_Eth_100M_InitFail;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_IBS_Eth_100M_OpenLoad;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_IBS_Eth_100M_ShortCircuit_Ground;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_IBS_Eth_100M_ShortCircuit_Power;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_IBS_Eth_100M_NoCom;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_IBS_Eth_100M_FaultyCom;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_IBS_Eth_100M_OverTemperature;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_IBS_Eth0_1000M_Lidar_InitFail;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_IBS_Eth0_1000M_Lidar_OpenLoad;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_IBS_Eth0_1000M_Lidar_ShortCircuit_Ground;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_IBS_Eth0_1000M_Lidar_ShortCircuit_Power;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_IBS_Eth0_1000M_Lidar_NoCom;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_IBS_Eth0_1000M_Lidar_FaultyCom;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_IBS_Eth0_1000M_Lidar_OverTemperature;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_IBS_Eth0_1000M_Vehicle_InitFail;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_IBS_Eth0_1000M_Vehicle_OpenLoad;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_IBS_Eth0_1000M_Vehicle_ShortCircuit_Ground;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_IBS_Eth0_1000M_Vehicle_ShortCircuit_Power;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_IBS_Eth0_1000M_Vehicle_NoCom;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_IBS_Eth0_1000M_Vehicle_FaultyCom;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_IBS_Eth0_1000M_Vehicle_OverTemperature;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_IBS_Eth1_1000M_InitFail;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_IBS_Eth1_1000M_OpenLoad;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_IBS_Eth1_1000M_ShortCircuit_Ground;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_IBS_Eth1_1000M_ShortCircuit_Power;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_IBS_Eth1_1000M_NoCom;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_IBS_Eth1_1000M_FaultyCom;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_IBS_Eth1_1000M_OverTemperature;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_GNSSIMU_GNSS_OpenLoad;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_GNSSIMU_GNSS_ShortCircuit_Ground;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_GNSSIMU_GNSS_ShortCircuit_Power;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_GNSSIMU_GNSS_DualFreq_FreqPointLoss;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_GNSSIMU_GNSS_Location_Error;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_GNSSIMU_HWError;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_GNSSIMU_PowerError;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_GNSSIMU_ComError;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_GNSSIMU_TempError;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_GNSSIMU_WheelInputError;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_GNSSIMU_INSError;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_GNSSIMU_IMUError;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_CPULoadHigh;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_MemoryUsageHigh;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FileSystemCorrupted;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(Ethcfg_Runnable_Events, m_FAULT_DemEvent_IBS_Eth_100M_InitFail, m_FAULT_DemEvent_IBS_Eth_100M_OpenLoad, m_FAULT_DemEvent_IBS_Eth_100M_ShortCircuit_Ground, m_FAULT_DemEvent_IBS_Eth_100M_ShortCircuit_Power, m_FAULT_DemEvent_IBS_Eth_100M_NoCom, m_FAULT_DemEvent_IBS_Eth_100M_FaultyCom, m_FAULT_DemEvent_IBS_Eth_100M_OverTemperature, m_FAULT_DemEvent_IBS_Eth0_1000M_Lidar_InitFail, m_FAULT_DemEvent_IBS_Eth0_1000M_Lidar_OpenLoad, m_FAULT_DemEvent_IBS_Eth0_1000M_Lidar_ShortCircuit_Ground, m_FAULT_DemEvent_IBS_Eth0_1000M_Lidar_ShortCircuit_Power, m_FAULT_DemEvent_IBS_Eth0_1000M_Lidar_NoCom, m_FAULT_DemEvent_IBS_Eth0_1000M_Lidar_FaultyCom, m_FAULT_DemEvent_IBS_Eth0_1000M_Lidar_OverTemperature, m_FAULT_DemEvent_IBS_Eth0_1000M_Vehicle_InitFail, m_FAULT_DemEvent_IBS_Eth0_1000M_Vehicle_OpenLoad, m_FAULT_DemEvent_IBS_Eth0_1000M_Vehicle_ShortCircuit_Ground, m_FAULT_DemEvent_IBS_Eth0_1000M_Vehicle_ShortCircuit_Power, m_FAULT_DemEvent_IBS_Eth0_1000M_Vehicle_NoCom, m_FAULT_DemEvent_IBS_Eth0_1000M_Vehicle_FaultyCom, m_FAULT_DemEvent_IBS_Eth0_1000M_Vehicle_OverTemperature, m_FAULT_DemEvent_IBS_Eth1_1000M_InitFail, m_FAULT_DemEvent_IBS_Eth1_1000M_OpenLoad, m_FAULT_DemEvent_IBS_Eth1_1000M_ShortCircuit_Ground, m_FAULT_DemEvent_IBS_Eth1_1000M_ShortCircuit_Power, m_FAULT_DemEvent_IBS_Eth1_1000M_NoCom, m_FAULT_DemEvent_IBS_Eth1_1000M_FaultyCom, m_FAULT_DemEvent_IBS_Eth1_1000M_OverTemperature, m_FAULT_DemEvent_GNSSIMU_GNSS_OpenLoad, m_FAULT_DemEvent_GNSSIMU_GNSS_ShortCircuit_Ground, m_FAULT_DemEvent_GNSSIMU_GNSS_ShortCircuit_Power, m_FAULT_DemEvent_GNSSIMU_GNSS_DualFreq_FreqPointLoss, m_FAULT_DemEvent_GNSSIMU_GNSS_Location_Error, m_FAULT_DemEvent_GNSSIMU_HWError, m_FAULT_DemEvent_GNSSIMU_PowerError, m_FAULT_DemEvent_GNSSIMU_ComError, m_FAULT_DemEvent_GNSSIMU_TempError, m_FAULT_DemEvent_GNSSIMU_WheelInputError, m_FAULT_DemEvent_GNSSIMU_INSError, m_FAULT_DemEvent_GNSSIMU_IMUError, m_FAULT_DemEvent_CPULoadHigh, m_FAULT_DemEvent_MemoryUsageHigh, m_FAULT_DemEvent_FileSystemCorrupted)

}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::um_cfg::Ethcfg_Runnable_Events, StdLayoutTag>
{
    using ValueType = ::um_cfg::Ethcfg_Runnable_Events;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::um_cfg::Ethcfg_Runnable_Events, StdLayoutTruncTag>
{
    using ValueType = ::um_cfg::Ethcfg_Runnable_Events;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
