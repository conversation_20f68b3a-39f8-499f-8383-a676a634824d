#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/container/array.hpp>
#include <basic_types/types.hpp>

#include <zos_msg/common/common.hpp>
#include <zos_msg/iEvm/zx_sys_evm_interfaces.hpp>
namespace um_cfg
{
const zos::uint8_t SensLcfg_Runnable_EventSize_cu8 = 216;
using SensL_IpcBuffer=zos::Array<zos::uint8_t, SensLcfg_Runnable_EventSize_cu8>;
struct SensLcfg_Runnable_Events
{
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLS_Sensl_OpenLoad;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLS_Sensl_ShortCircuit_Ground;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLS_Sensl_ShortCircuit_Power;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLS_Sensl_IntFailed;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLS_Sensl_WrongID;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLS_Sensl_signalDisturb;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLS_Sensl_Blind;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLS_Sensl_NoCom;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLS_Sensl_Ringdown_Short;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLS_Sensl_Ringdown_Long;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLS_Sensl_OverTemp;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLS_Sensl_UnderTemp;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLS_Sensl_OverVoltage;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLS_Sensl_UnderVoltage;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLS_Sensl_HwErr;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLS_Sensl_FaultyCom;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLS_Sensl_RingFreqHigh;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLS_Sensl_RingFreqLow;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLC_Sensl_OpenLoad;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLC_Sensl_ShortCircuit_Ground;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLC_Sensl_ShortCircuit_Power;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLC_Sensl_IntFailed;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLC_Sensl_WrongID;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLC_Sensl_signalDisturb;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLC_Sensl_Blind;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLC_Sensl_NoCom;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLC_Sensl_Ringdown_Short;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLC_Sensl_Ringdown_Long;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLC_Sensl_OverTemp;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLC_Sensl_UnderTemp;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLC_Sensl_OverVoltage;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLC_Sensl_UnderVoltage;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLC_Sensl_HwErr;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLC_Sensl_FaultyCom;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLC_Sensl_RingFreqHigh;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLC_Sensl_RingFreqLow;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLM_Sensl_OpenLoad;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLM_Sensl_ShortCircuit_Ground;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLM_Sensl_ShortCircuit_Power;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLM_Sensl_IntFailed;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLM_Sensl_WrongID;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLM_Sensl_signalDisturb;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLM_Sensl_Blind;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLM_Sensl_NoCom;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLM_Sensl_Ringdown_Short;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLM_Sensl_Ringdown_Long;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLM_Sensl_OverTemp;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLM_Sensl_UnderTemp;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLM_Sensl_OverVoltage;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLM_Sensl_UnderVoltage;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLM_Sensl_HwErr;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLM_Sensl_FaultyCom;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLM_Sensl_RingFreqHigh;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FLM_Sensl_RingFreqLow;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRM_Sensl_OpenLoad;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRM_Sensl_ShortCircuit_Ground;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRM_Sensl_ShortCircuit_Power;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRM_Sensl_IntFailed;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRM_Sensl_WrongID;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRM_Sensl_signalDisturb;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRM_Sensl_Blind;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRM_Sensl_NoCom;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRM_Sensl_Ringdown_Short;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRM_Sensl_Ringdown_Long;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRM_Sensl_OverTemp;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRM_Sensl_UnderTemp;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRM_Sensl_OverVoltage;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRM_Sensl_UnderVoltage;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRM_Sensl_HwErr;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRM_Sensl_FaultyCom;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRM_Sensl_RingFreqHigh;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRM_Sensl_RingFreqLow;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRC_Sensl_OpenLoad;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRC_Sensl_ShortCircuit_Ground;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRC_Sensl_ShortCircuit_Power;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRC_Sensl_IntFailed;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRC_Sensl_WrongID;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRC_Sensl_signalDisturb;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRC_Sensl_Blind;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRC_Sensl_NoCom;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRC_Sensl_Ringdown_Short;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRC_Sensl_Ringdown_Long;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRC_Sensl_OverTemp;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRC_Sensl_UnderTemp;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRC_Sensl_OverVoltage;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRC_Sensl_UnderVoltage;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRC_Sensl_HwErr;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRC_Sensl_FaultyCom;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRC_Sensl_RingFreqHigh;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRC_Sensl_RingFreqLow;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRS_Sensl_OpenLoad;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRS_Sensl_ShortCircuit_Ground;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRS_Sensl_ShortCircuit_Power;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRS_Sensl_IntFailed;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRS_Sensl_WrongID;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRS_Sensl_signalDisturb;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRS_Sensl_Blind;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRS_Sensl_NoCom;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRS_Sensl_Ringdown_Short;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRS_Sensl_Ringdown_Long;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRS_Sensl_OverTemp;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRS_Sensl_UnderTemp;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRS_Sensl_OverVoltage;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRS_Sensl_UnderVoltage;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRS_Sensl_HwErr;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRS_Sensl_FaultyCom;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRS_Sensl_RingFreqHigh;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_FRS_Sensl_RingFreqLow;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRS_Sensl_OpenLoad;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRS_Sensl_ShortCircuit_Ground;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRS_Sensl_ShortCircuit_Power;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRS_Sensl_IntFailed;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRS_Sensl_WrongID;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRS_Sensl_signalDisturb;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRS_Sensl_Blind;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRS_Sensl_NoCom;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRS_Sensl_Ringdown_Short;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRS_Sensl_Ringdown_Long;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRS_Sensl_OverTemp;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRS_Sensl_UnderTemp;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRS_Sensl_OverVoltage;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRS_Sensl_UnderVoltage;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRS_Sensl_HwErr;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRS_Sensl_FaultyCom;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRS_Sensl_RingFreqHigh;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRS_Sensl_RingFreqLow;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRC_Sensl_OpenLoad;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRC_Sensl_ShortCircuit_Ground;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRC_Sensl_ShortCircuit_Power;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRC_Sensl_IntFailed;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRC_Sensl_WrongID;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRC_Sensl_signalDisturb;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRC_Sensl_Blind;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRC_Sensl_NoCom;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRC_Sensl_Ringdown_Short;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRC_Sensl_Ringdown_Long;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRC_Sensl_OverTemp;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRC_Sensl_UnderTemp;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRC_Sensl_OverVoltage;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRC_Sensl_UnderVoltage;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRC_Sensl_HwErr;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRC_Sensl_FaultyCom;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRC_Sensl_RingFreqHigh;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRC_Sensl_RingFreqLow;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRM_Sensl_OpenLoad;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRM_Sensl_ShortCircuit_Ground;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRM_Sensl_ShortCircuit_Power;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRM_Sensl_IntFailed;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRM_Sensl_WrongID;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRM_Sensl_signalDisturb;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRM_Sensl_Blind;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRM_Sensl_NoCom;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRM_Sensl_Ringdown_Short;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRM_Sensl_Ringdown_Long;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRM_Sensl_OverTemp;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRM_Sensl_UnderTemp;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRM_Sensl_OverVoltage;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRM_Sensl_UnderVoltage;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRM_Sensl_HwErr;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRM_Sensl_FaultyCom;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRM_Sensl_RingFreqHigh;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RRM_Sensl_RingFreqLow;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLM_Sensl_OpenLoad;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLM_Sensl_ShortCircuit_Ground;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLM_Sensl_ShortCircuit_Power;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLM_Sensl_IntFailed;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLM_Sensl_WrongID;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLM_Sensl_signalDisturb;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLM_Sensl_Blind;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLM_Sensl_NoCom;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLM_Sensl_Ringdown_Short;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLM_Sensl_Ringdown_Long;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLM_Sensl_OverTemp;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLM_Sensl_UnderTemp;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLM_Sensl_OverVoltage;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLM_Sensl_UnderVoltage;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLM_Sensl_HwErr;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLM_Sensl_FaultyCom;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLM_Sensl_RingFreqHigh;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLM_Sensl_RingFreqLow;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLC_Sensl_OpenLoad;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLC_Sensl_ShortCircuit_Ground;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLC_Sensl_ShortCircuit_Power;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLC_Sensl_IntFailed;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLC_Sensl_WrongID;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLC_Sensl_signalDisturb;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLC_Sensl_Blind;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLC_Sensl_NoCom;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLC_Sensl_Ringdown_Short;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLC_Sensl_Ringdown_Long;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLC_Sensl_OverTemp;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLC_Sensl_UnderTemp;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLC_Sensl_OverVoltage;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLC_Sensl_UnderVoltage;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLC_Sensl_HwErr;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLC_Sensl_FaultyCom;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLC_Sensl_RingFreqHigh;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLC_Sensl_RingFreqLow;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLS_Sensl_OpenLoad;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLS_Sensl_ShortCircuit_Ground;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLS_Sensl_ShortCircuit_Power;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLS_Sensl_IntFailed;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLS_Sensl_WrongID;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLS_Sensl_signalDisturb;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLS_Sensl_Blind;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLS_Sensl_NoCom;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLS_Sensl_Ringdown_Short;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLS_Sensl_Ringdown_Long;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLS_Sensl_OverTemp;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLS_Sensl_UnderTemp;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLS_Sensl_OverVoltage;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLS_Sensl_UnderVoltage;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLS_Sensl_HwErr;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLS_Sensl_FaultyCom;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLS_Sensl_RingFreqHigh;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_RLS_Sensl_RingFreqLow;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(SensLcfg_Runnable_Events, m_FAULT_DemEvent_FLS_Sensl_OpenLoad, m_FAULT_DemEvent_FLS_Sensl_ShortCircuit_Ground, m_FAULT_DemEvent_FLS_Sensl_ShortCircuit_Power, m_FAULT_DemEvent_FLS_Sensl_IntFailed, m_FAULT_DemEvent_FLS_Sensl_WrongID, m_FAULT_DemEvent_FLS_Sensl_signalDisturb, m_FAULT_DemEvent_FLS_Sensl_Blind, m_FAULT_DemEvent_FLS_Sensl_NoCom, m_FAULT_DemEvent_FLS_Sensl_Ringdown_Short, m_FAULT_DemEvent_FLS_Sensl_Ringdown_Long, m_FAULT_DemEvent_FLS_Sensl_OverTemp, m_FAULT_DemEvent_FLS_Sensl_UnderTemp, m_FAULT_DemEvent_FLS_Sensl_OverVoltage, m_FAULT_DemEvent_FLS_Sensl_UnderVoltage, m_FAULT_DemEvent_FLS_Sensl_HwErr, m_FAULT_DemEvent_FLS_Sensl_FaultyCom, m_FAULT_DemEvent_FLS_Sensl_RingFreqHigh, m_FAULT_DemEvent_FLS_Sensl_RingFreqLow, m_FAULT_DemEvent_FLC_Sensl_OpenLoad, m_FAULT_DemEvent_FLC_Sensl_ShortCircuit_Ground, m_FAULT_DemEvent_FLC_Sensl_ShortCircuit_Power, m_FAULT_DemEvent_FLC_Sensl_IntFailed, m_FAULT_DemEvent_FLC_Sensl_WrongID, m_FAULT_DemEvent_FLC_Sensl_signalDisturb, m_FAULT_DemEvent_FLC_Sensl_Blind, m_FAULT_DemEvent_FLC_Sensl_NoCom, m_FAULT_DemEvent_FLC_Sensl_Ringdown_Short, m_FAULT_DemEvent_FLC_Sensl_Ringdown_Long, m_FAULT_DemEvent_FLC_Sensl_OverTemp, m_FAULT_DemEvent_FLC_Sensl_UnderTemp, m_FAULT_DemEvent_FLC_Sensl_OverVoltage, m_FAULT_DemEvent_FLC_Sensl_UnderVoltage, m_FAULT_DemEvent_FLC_Sensl_HwErr, m_FAULT_DemEvent_FLC_Sensl_FaultyCom, m_FAULT_DemEvent_FLC_Sensl_RingFreqHigh, m_FAULT_DemEvent_FLC_Sensl_RingFreqLow, m_FAULT_DemEvent_FLM_Sensl_OpenLoad, m_FAULT_DemEvent_FLM_Sensl_ShortCircuit_Ground, m_FAULT_DemEvent_FLM_Sensl_ShortCircuit_Power, m_FAULT_DemEvent_FLM_Sensl_IntFailed, m_FAULT_DemEvent_FLM_Sensl_WrongID, m_FAULT_DemEvent_FLM_Sensl_signalDisturb, m_FAULT_DemEvent_FLM_Sensl_Blind, m_FAULT_DemEvent_FLM_Sensl_NoCom, m_FAULT_DemEvent_FLM_Sensl_Ringdown_Short, m_FAULT_DemEvent_FLM_Sensl_Ringdown_Long, m_FAULT_DemEvent_FLM_Sensl_OverTemp, m_FAULT_DemEvent_FLM_Sensl_UnderTemp, m_FAULT_DemEvent_FLM_Sensl_OverVoltage, m_FAULT_DemEvent_FLM_Sensl_UnderVoltage, m_FAULT_DemEvent_FLM_Sensl_HwErr, m_FAULT_DemEvent_FLM_Sensl_FaultyCom, m_FAULT_DemEvent_FLM_Sensl_RingFreqHigh, m_FAULT_DemEvent_FLM_Sensl_RingFreqLow, m_FAULT_DemEvent_FRM_Sensl_OpenLoad, m_FAULT_DemEvent_FRM_Sensl_ShortCircuit_Ground, m_FAULT_DemEvent_FRM_Sensl_ShortCircuit_Power, m_FAULT_DemEvent_FRM_Sensl_IntFailed, m_FAULT_DemEvent_FRM_Sensl_WrongID, m_FAULT_DemEvent_FRM_Sensl_signalDisturb, m_FAULT_DemEvent_FRM_Sensl_Blind, m_FAULT_DemEvent_FRM_Sensl_NoCom, m_FAULT_DemEvent_FRM_Sensl_Ringdown_Short, m_FAULT_DemEvent_FRM_Sensl_Ringdown_Long, m_FAULT_DemEvent_FRM_Sensl_OverTemp, m_FAULT_DemEvent_FRM_Sensl_UnderTemp, m_FAULT_DemEvent_FRM_Sensl_OverVoltage, m_FAULT_DemEvent_FRM_Sensl_UnderVoltage, m_FAULT_DemEvent_FRM_Sensl_HwErr, m_FAULT_DemEvent_FRM_Sensl_FaultyCom, m_FAULT_DemEvent_FRM_Sensl_RingFreqHigh, m_FAULT_DemEvent_FRM_Sensl_RingFreqLow, m_FAULT_DemEvent_FRC_Sensl_OpenLoad, m_FAULT_DemEvent_FRC_Sensl_ShortCircuit_Ground, m_FAULT_DemEvent_FRC_Sensl_ShortCircuit_Power, m_FAULT_DemEvent_FRC_Sensl_IntFailed, m_FAULT_DemEvent_FRC_Sensl_WrongID, m_FAULT_DemEvent_FRC_Sensl_signalDisturb, m_FAULT_DemEvent_FRC_Sensl_Blind, m_FAULT_DemEvent_FRC_Sensl_NoCom, m_FAULT_DemEvent_FRC_Sensl_Ringdown_Short, m_FAULT_DemEvent_FRC_Sensl_Ringdown_Long, m_FAULT_DemEvent_FRC_Sensl_OverTemp, m_FAULT_DemEvent_FRC_Sensl_UnderTemp, m_FAULT_DemEvent_FRC_Sensl_OverVoltage, m_FAULT_DemEvent_FRC_Sensl_UnderVoltage, m_FAULT_DemEvent_FRC_Sensl_HwErr, m_FAULT_DemEvent_FRC_Sensl_FaultyCom, m_FAULT_DemEvent_FRC_Sensl_RingFreqHigh, m_FAULT_DemEvent_FRC_Sensl_RingFreqLow, m_FAULT_DemEvent_FRS_Sensl_OpenLoad, m_FAULT_DemEvent_FRS_Sensl_ShortCircuit_Ground, m_FAULT_DemEvent_FRS_Sensl_ShortCircuit_Power, m_FAULT_DemEvent_FRS_Sensl_IntFailed, m_FAULT_DemEvent_FRS_Sensl_WrongID, m_FAULT_DemEvent_FRS_Sensl_signalDisturb, m_FAULT_DemEvent_FRS_Sensl_Blind, m_FAULT_DemEvent_FRS_Sensl_NoCom, m_FAULT_DemEvent_FRS_Sensl_Ringdown_Short, m_FAULT_DemEvent_FRS_Sensl_Ringdown_Long, m_FAULT_DemEvent_FRS_Sensl_OverTemp, m_FAULT_DemEvent_FRS_Sensl_UnderTemp, m_FAULT_DemEvent_FRS_Sensl_OverVoltage, m_FAULT_DemEvent_FRS_Sensl_UnderVoltage, m_FAULT_DemEvent_FRS_Sensl_HwErr, m_FAULT_DemEvent_FRS_Sensl_FaultyCom, m_FAULT_DemEvent_FRS_Sensl_RingFreqHigh, m_FAULT_DemEvent_FRS_Sensl_RingFreqLow, m_FAULT_DemEvent_RRS_Sensl_OpenLoad, m_FAULT_DemEvent_RRS_Sensl_ShortCircuit_Ground, m_FAULT_DemEvent_RRS_Sensl_ShortCircuit_Power, m_FAULT_DemEvent_RRS_Sensl_IntFailed, m_FAULT_DemEvent_RRS_Sensl_WrongID, m_FAULT_DemEvent_RRS_Sensl_signalDisturb, m_FAULT_DemEvent_RRS_Sensl_Blind, m_FAULT_DemEvent_RRS_Sensl_NoCom, m_FAULT_DemEvent_RRS_Sensl_Ringdown_Short, m_FAULT_DemEvent_RRS_Sensl_Ringdown_Long, m_FAULT_DemEvent_RRS_Sensl_OverTemp, m_FAULT_DemEvent_RRS_Sensl_UnderTemp, m_FAULT_DemEvent_RRS_Sensl_OverVoltage, m_FAULT_DemEvent_RRS_Sensl_UnderVoltage, m_FAULT_DemEvent_RRS_Sensl_HwErr, m_FAULT_DemEvent_RRS_Sensl_FaultyCom, m_FAULT_DemEvent_RRS_Sensl_RingFreqHigh, m_FAULT_DemEvent_RRS_Sensl_RingFreqLow, m_FAULT_DemEvent_RRC_Sensl_OpenLoad, m_FAULT_DemEvent_RRC_Sensl_ShortCircuit_Ground, m_FAULT_DemEvent_RRC_Sensl_ShortCircuit_Power, m_FAULT_DemEvent_RRC_Sensl_IntFailed, m_FAULT_DemEvent_RRC_Sensl_WrongID, m_FAULT_DemEvent_RRC_Sensl_signalDisturb, m_FAULT_DemEvent_RRC_Sensl_Blind, m_FAULT_DemEvent_RRC_Sensl_NoCom, m_FAULT_DemEvent_RRC_Sensl_Ringdown_Short, m_FAULT_DemEvent_RRC_Sensl_Ringdown_Long, m_FAULT_DemEvent_RRC_Sensl_OverTemp, m_FAULT_DemEvent_RRC_Sensl_UnderTemp, m_FAULT_DemEvent_RRC_Sensl_OverVoltage, m_FAULT_DemEvent_RRC_Sensl_UnderVoltage, m_FAULT_DemEvent_RRC_Sensl_HwErr, m_FAULT_DemEvent_RRC_Sensl_FaultyCom, m_FAULT_DemEvent_RRC_Sensl_RingFreqHigh, m_FAULT_DemEvent_RRC_Sensl_RingFreqLow, m_FAULT_DemEvent_RRM_Sensl_OpenLoad, m_FAULT_DemEvent_RRM_Sensl_ShortCircuit_Ground, m_FAULT_DemEvent_RRM_Sensl_ShortCircuit_Power, m_FAULT_DemEvent_RRM_Sensl_IntFailed, m_FAULT_DemEvent_RRM_Sensl_WrongID, m_FAULT_DemEvent_RRM_Sensl_signalDisturb, m_FAULT_DemEvent_RRM_Sensl_Blind, m_FAULT_DemEvent_RRM_Sensl_NoCom, m_FAULT_DemEvent_RRM_Sensl_Ringdown_Short, m_FAULT_DemEvent_RRM_Sensl_Ringdown_Long, m_FAULT_DemEvent_RRM_Sensl_OverTemp, m_FAULT_DemEvent_RRM_Sensl_UnderTemp, m_FAULT_DemEvent_RRM_Sensl_OverVoltage, m_FAULT_DemEvent_RRM_Sensl_UnderVoltage, m_FAULT_DemEvent_RRM_Sensl_HwErr, m_FAULT_DemEvent_RRM_Sensl_FaultyCom, m_FAULT_DemEvent_RRM_Sensl_RingFreqHigh, m_FAULT_DemEvent_RRM_Sensl_RingFreqLow, m_FAULT_DemEvent_RLM_Sensl_OpenLoad, m_FAULT_DemEvent_RLM_Sensl_ShortCircuit_Ground, m_FAULT_DemEvent_RLM_Sensl_ShortCircuit_Power, m_FAULT_DemEvent_RLM_Sensl_IntFailed, m_FAULT_DemEvent_RLM_Sensl_WrongID, m_FAULT_DemEvent_RLM_Sensl_signalDisturb, m_FAULT_DemEvent_RLM_Sensl_Blind, m_FAULT_DemEvent_RLM_Sensl_NoCom, m_FAULT_DemEvent_RLM_Sensl_Ringdown_Short, m_FAULT_DemEvent_RLM_Sensl_Ringdown_Long, m_FAULT_DemEvent_RLM_Sensl_OverTemp, m_FAULT_DemEvent_RLM_Sensl_UnderTemp, m_FAULT_DemEvent_RLM_Sensl_OverVoltage, m_FAULT_DemEvent_RLM_Sensl_UnderVoltage, m_FAULT_DemEvent_RLM_Sensl_HwErr, m_FAULT_DemEvent_RLM_Sensl_FaultyCom, m_FAULT_DemEvent_RLM_Sensl_RingFreqHigh, m_FAULT_DemEvent_RLM_Sensl_RingFreqLow, m_FAULT_DemEvent_RLC_Sensl_OpenLoad, m_FAULT_DemEvent_RLC_Sensl_ShortCircuit_Ground, m_FAULT_DemEvent_RLC_Sensl_ShortCircuit_Power, m_FAULT_DemEvent_RLC_Sensl_IntFailed, m_FAULT_DemEvent_RLC_Sensl_WrongID, m_FAULT_DemEvent_RLC_Sensl_signalDisturb, m_FAULT_DemEvent_RLC_Sensl_Blind, m_FAULT_DemEvent_RLC_Sensl_NoCom, m_FAULT_DemEvent_RLC_Sensl_Ringdown_Short, m_FAULT_DemEvent_RLC_Sensl_Ringdown_Long, m_FAULT_DemEvent_RLC_Sensl_OverTemp, m_FAULT_DemEvent_RLC_Sensl_UnderTemp, m_FAULT_DemEvent_RLC_Sensl_OverVoltage, m_FAULT_DemEvent_RLC_Sensl_UnderVoltage, m_FAULT_DemEvent_RLC_Sensl_HwErr, m_FAULT_DemEvent_RLC_Sensl_FaultyCom, m_FAULT_DemEvent_RLC_Sensl_RingFreqHigh, m_FAULT_DemEvent_RLC_Sensl_RingFreqLow, m_FAULT_DemEvent_RLS_Sensl_OpenLoad, m_FAULT_DemEvent_RLS_Sensl_ShortCircuit_Ground, m_FAULT_DemEvent_RLS_Sensl_ShortCircuit_Power, m_FAULT_DemEvent_RLS_Sensl_IntFailed, m_FAULT_DemEvent_RLS_Sensl_WrongID, m_FAULT_DemEvent_RLS_Sensl_signalDisturb, m_FAULT_DemEvent_RLS_Sensl_Blind, m_FAULT_DemEvent_RLS_Sensl_NoCom, m_FAULT_DemEvent_RLS_Sensl_Ringdown_Short, m_FAULT_DemEvent_RLS_Sensl_Ringdown_Long, m_FAULT_DemEvent_RLS_Sensl_OverTemp, m_FAULT_DemEvent_RLS_Sensl_UnderTemp, m_FAULT_DemEvent_RLS_Sensl_OverVoltage, m_FAULT_DemEvent_RLS_Sensl_UnderVoltage, m_FAULT_DemEvent_RLS_Sensl_HwErr, m_FAULT_DemEvent_RLS_Sensl_FaultyCom, m_FAULT_DemEvent_RLS_Sensl_RingFreqHigh, m_FAULT_DemEvent_RLS_Sensl_RingFreqLow)

}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::um_cfg::SensLcfg_Runnable_Events, StdLayoutTag>
{
    using ValueType = ::um_cfg::SensLcfg_Runnable_Events;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::um_cfg::SensLcfg_Runnable_Events, StdLayoutTruncTag>
{
    using ValueType = ::um_cfg::SensLcfg_Runnable_Events;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
