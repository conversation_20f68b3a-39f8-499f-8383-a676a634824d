#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/container/array.hpp>
#include <basic_types/types.hpp>

#include <zos_msg/common/common.hpp>
#include <zos_msg/iEvm/zx_sys_evm_interfaces.hpp>
namespace um_cfg
{
const zos::uint8_t cameracfg_Runnable_EventSize_cu8 = 308;
using camera_IpcBuffer=zos::Array<zos::uint8_t, cameracfg_Runnable_EventSize_cu8>;
struct cameracfg_Runnable_Events
{
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_UNLOCK_Cam_Front30;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_UNLOCK_Cam_Front120;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_UNLOCK_Cam_Rear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_UNLOCK_Cam_NA;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_UNLOCK_Cam_SideRR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_UNLOCK_Cam_SideRL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_UNLOCK_Cam_SideFL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_UNLOCK_Cam_SideFR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_UNLOCK_Cam_AVMRear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_UNLOCK_Cam_AVMLeft;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_UNLOCK_Cam_AVMFront;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_UNLOCK_Cam_AVMRight;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_VID_LOCK_Cam_Front30;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_VID_LOCK_Cam_Front120;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_VID_LOCK_Cam_Rear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_VID_LOCK_Cam_NA;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_VID_LOCK_Cam_SideRR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_VID_LOCK_Cam_SideRL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_VID_LOCK_Cam_SideFL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_VID_LOCK_Cam_SideFR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_VID_LOCK_Cam_AVMRear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_VID_LOCK_Cam_AVMLeft;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_VID_LOCK_Cam_AVMFront;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_VID_LOCK_Cam_AVMRight;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_IDLE_ERR_FLAG_Cam_Front30;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_IDLE_ERR_FLAG_Cam_Front120;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_IDLE_ERR_FLAG_Cam_Rear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_IDLE_ERR_FLAG_Cam_NA;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_IDLE_ERR_FLAG_Cam_SideRR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_IDLE_ERR_FLAG_Cam_SideRL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_IDLE_ERR_FLAG_Cam_SideFL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_IDLE_ERR_FLAG_Cam_SideFR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_IDLE_ERR_FLAG_Cam_AVMRear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_IDLE_ERR_FLAG_Cam_AVMLeft;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_IDLE_ERR_FLAG_Cam_AVMFront;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_IDLE_ERR_FLAG_Cam_AVMRight;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_MAX_RT_FLAG_0_Cam_Front30;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_MAX_RT_FLAG_0_Cam_Front120;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_MAX_RT_FLAG_0_Cam_Rear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_MAX_RT_FLAG_0_Cam_NA;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_MAX_RT_FLAG_0_Cam_SideRR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_MAX_RT_FLAG_0_Cam_SideRL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_MAX_RT_FLAG_0_Cam_SideFL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_MAX_RT_FLAG_0_Cam_SideFR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_MAX_RT_FLAG_0_Cam_AVMRear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_MAX_RT_FLAG_0_Cam_AVMLeft;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_MAX_RT_FLAG_0_Cam_AVMFront;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_MAX_RT_FLAG_0_Cam_AVMRight;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_RT_CNT_FLAG_0_Cam_Front30;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_RT_CNT_FLAG_0_Cam_Front120;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_RT_CNT_FLAG_0_Cam_Rear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_RT_CNT_FLAG_0_Cam_NA;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_RT_CNT_FLAG_0_Cam_SideRR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_RT_CNT_FLAG_0_Cam_SideRL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_RT_CNT_FLAG_0_Cam_SideFL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_RT_CNT_FLAG_0_Cam_SideFR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_RT_CNT_FLAG_0_Cam_AVMRear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_RT_CNT_FLAG_0_Cam_AVMLeft;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_RT_CNT_FLAG_0_Cam_AVMFront;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_RT_CNT_FLAG_0_Cam_AVMRight;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_DEC_ERR_FLAG_Cam_Front30;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_DEC_ERR_FLAG_Cam_Front120;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_DEC_ERR_FLAG_Cam_Rear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_DEC_ERR_FLAG_Cam_NA;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_DEC_ERR_FLAG_Cam_SideRR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_DEC_ERR_FLAG_Cam_SideRL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_DEC_ERR_FLAG_Cam_SideFL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_DEC_ERR_FLAG_Cam_SideFR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_DEC_ERR_FLAG_Cam_AVMRear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_DEC_ERR_FLAG_Cam_AVMLeft;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_DEC_ERR_FLAG_Cam_AVMFront;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_DEC_ERR_FLAG_Cam_AVMRight;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_VID_PXL_CRC_ERR_0_Cam_Front30;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_VID_PXL_CRC_ERR_0_Cam_Front120;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_VID_PXL_CRC_ERR_0_Cam_Rear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_VID_PXL_CRC_ERR_0_Cam_NA;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_VID_PXL_CRC_ERR_0_Cam_SideRR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_VID_PXL_CRC_ERR_0_Cam_SideRL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_VID_PXL_CRC_ERR_0_Cam_SideFL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_VID_PXL_CRC_ERR_0_Cam_SideFR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_VID_PXL_CRC_ERR_0_Cam_AVMRear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_VID_PXL_CRC_ERR_0_Cam_AVMLeft;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_VID_PXL_CRC_ERR_0_Cam_AVMFront;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_VID_PXL_CRC_ERR_0_Cam_AVMRight;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_LMO_ERR_Cam_Front30;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_LMO_ERR_Cam_Front120;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_LMO_ERR_Cam_Rear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_LMO_ERR_Cam_NA;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_LMO_ERR_Cam_SideRR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_LMO_ERR_Cam_SideRL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_LMO_ERR_Cam_SideFL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_LMO_ERR_Cam_SideFR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_LMO_ERR_Cam_AVMRear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_LMO_ERR_Cam_AVMLeft;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_LMO_ERR_Cam_AVMFront;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_LMO_ERR_Cam_AVMRight;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_LCRC_ERR_Cam_Front30;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_LCRC_ERR_Cam_Front120;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_LCRC_ERR_Cam_Rear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_LCRC_ERR_Cam_NA;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_LCRC_ERR_Cam_SideRR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_LCRC_ERR_Cam_SideRL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_LCRC_ERR_Cam_SideFL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_LCRC_ERR_Cam_SideFR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_LCRC_ERR_Cam_AVMRear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_LCRC_ERR_Cam_AVMLeft;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_LCRC_ERR_Cam_AVMFront;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_LCRC_ERR_Cam_AVMRight;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_MEM_ECC_ERR2_Cam_Front30;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_MEM_ECC_ERR2_Cam_Front120;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_MEM_ECC_ERR2_Cam_Rear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_MEM_ECC_ERR2_Cam_NA;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_MEM_ECC_ERR2_Cam_SideRR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_MEM_ECC_ERR2_Cam_SideRL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_MEM_ECC_ERR2_Cam_SideFL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_MEM_ECC_ERR2_Cam_SideFR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_MEM_ECC_ERR2_Cam_AVMRear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_MEM_ECC_ERR2_Cam_AVMLeft;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_MEM_ECC_ERR2_Cam_AVMFront;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_MEM_ECC_ERR2_Cam_AVMRight;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_ERRB_Cam_Front30;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_ERRB_Cam_Front120;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_ERRB_Cam_Rear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_ERRB_Cam_NA;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_ERRB_Cam_SideRR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_ERRB_Cam_SideRL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_ERRB_Cam_SideFL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_ERRB_Cam_SideFR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_ERRB_Cam_AVMRear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_ERRB_Cam_AVMLeft;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_ERRB_Cam_AVMFront;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DES_ERRB_Cam_AVMRight;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_CAMERA_INIT_ID_Cam_Front120;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_CAMERA_INIT_ID_Cam_Front30;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_CAMERA_INIT_ID_Cam_Rear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_CAMERA_INIT_ID_Cam_SideRR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_CAMERA_INIT_ID_Cam_SideRL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_CAMERA_INIT_ID_Cam_SideFL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_CAMERA_INIT_ID_Cam_SideFR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_CAMERA_INIT_ID_Cam_AVMRear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_CAMERA_INIT_ID_Cam_AVMLeft;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_CAMERA_INIT_ID_Cam_AVMFront;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_CAMERA_INIT_ID_Cam_AVMRight;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_STREAM_OFF_ID_Cam_Front120;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_STREAM_OFF_ID_Cam_Front30;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_STREAM_OFF_ID_Cam_Rear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_STREAM_OFF_ID_Cam_SideRR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_STREAM_OFF_ID_Cam_SideRL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_STREAM_OFF_ID_Cam_SideFL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_STREAM_OFF_ID_Cam_SideFR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_STREAM_OFF_ID_Cam_AVMRear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_STREAM_OFF_ID_Cam_AVMLeft;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_STREAM_OFF_ID_Cam_AVMFront;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_STREAM_OFF_ID_Cam_AVMRight;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_VOLTAGE_ID_Cam_Front120;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_VOLTAGE_ID_Cam_Front30;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_VOLTAGE_ID_Cam_Rear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_VOLTAGE_ID_Cam_SideRR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_VOLTAGE_ID_Cam_SideRL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_VOLTAGE_ID_Cam_SideFL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_VOLTAGE_ID_Cam_SideFR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_VOLTAGE_ID_Cam_AVMRear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_VOLTAGE_ID_Cam_AVMLeft;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_VOLTAGE_ID_Cam_AVMFront;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_VOLTAGE_ID_Cam_AVMRight;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_FCNT_ID_Cam_Front120;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_FCNT_ID_Cam_Front30;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_FCNT_ID_Cam_Rear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_FCNT_ID_Cam_SideRR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_FCNT_ID_Cam_SideRL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_FCNT_ID_Cam_SideFL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_FCNT_ID_Cam_SideFR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_FCNT_ID_Cam_AVMRear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_FCNT_ID_Cam_AVMLeft;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_FCNT_ID_Cam_AVMFront;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_FCNT_ID_Cam_AVMRight;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_TEMPER_ID_Cam_Front120;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_TEMPER_ID_Cam_Front30;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_TEMPER_ID_Cam_Rear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_TEMPER_ID_Cam_SideRR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_TEMPER_ID_Cam_SideRL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_TEMPER_ID_Cam_SideFL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_TEMPER_ID_Cam_SideFR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_TEMPER_ID_Cam_AVMRear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_TEMPER_ID_Cam_AVMLeft;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_TEMPER_ID_Cam_AVMFront;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_TEMPER_ID_Cam_AVMRight;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ROW_COLUMN_ID_Cam_Front120;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ROW_COLUMN_ID_Cam_Front30;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ROW_COLUMN_ID_Cam_Rear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ROW_COLUMN_ID_Cam_SideRR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ROW_COLUMN_ID_Cam_SideRL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ROW_COLUMN_ID_Cam_SideFL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ROW_COLUMN_ID_Cam_SideFR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ROW_COLUMN_ID_Cam_AVMRear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ROW_COLUMN_ID_Cam_AVMLeft;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ROW_COLUMN_ID_Cam_AVMFront;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ROW_COLUMN_ID_Cam_AVMRight;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_PLL_CLOCK_ID_Cam_Front120;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_PLL_CLOCK_ID_Cam_Front30;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_PLL_CLOCK_ID_Cam_Rear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_PLL_CLOCK_ID_Cam_SideRR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_PLL_CLOCK_ID_Cam_SideRL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_PLL_CLOCK_ID_Cam_SideFL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_PLL_CLOCK_ID_Cam_SideFR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_PLL_CLOCK_ID_Cam_AVMRear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_PLL_CLOCK_ID_Cam_AVMLeft;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_PLL_CLOCK_ID_Cam_AVMFront;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_PLL_CLOCK_ID_Cam_AVMRight;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_I2C_CRC_ID_Cam_Front120;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_I2C_CRC_ID_Cam_Front30;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_I2C_CRC_ID_Cam_Rear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_I2C_CRC_ID_Cam_SideRR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_I2C_CRC_ID_Cam_SideRL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_I2C_CRC_ID_Cam_SideFL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_I2C_CRC_ID_Cam_SideFR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_I2C_CRC_ID_Cam_AVMRear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_I2C_CRC_ID_Cam_AVMLeft;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_I2C_CRC_ID_Cam_AVMFront;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_I2C_CRC_ID_Cam_AVMRight;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_SCCB_ID_Cam_Front120;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_SCCB_ID_Cam_Front30;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_SCCB_ID_Cam_Rear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_SCCB_ID_Cam_SideRR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_SCCB_ID_Cam_SideRL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_SCCB_ID_Cam_SideFL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_SCCB_ID_Cam_SideFR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_SCCB_ID_Cam_AVMRear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_SCCB_ID_Cam_AVMLeft;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_SCCB_ID_Cam_AVMFront;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_SCCB_ID_Cam_AVMRight;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_RAM_CRC_ID_Cam_Front120;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_RAM_CRC_ID_Cam_Front30;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_RAM_CRC_ID_Cam_Rear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_RAM_CRC_ID_Cam_SideRR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_RAM_CRC_ID_Cam_SideRL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_RAM_CRC_ID_Cam_SideFL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_RAM_CRC_ID_Cam_SideFR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_RAM_CRC_ID_Cam_AVMRear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_RAM_CRC_ID_Cam_AVMLeft;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_RAM_CRC_ID_Cam_AVMFront;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_RAM_CRC_ID_Cam_AVMRight;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ROM_CRC_ID_Cam_Front120;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ROM_CRC_ID_Cam_Front30;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ROM_CRC_ID_Cam_Rear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ROM_CRC_ID_Cam_SideRR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ROM_CRC_ID_Cam_SideRL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ROM_CRC_ID_Cam_SideFL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ROM_CRC_ID_Cam_SideFR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ROM_CRC_ID_Cam_AVMRear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ROM_CRC_ID_Cam_AVMLeft;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ROM_CRC_ID_Cam_AVMFront;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ROM_CRC_ID_Cam_AVMRight;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ONLINE_PIXEL_ID_Cam_Front120;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ONLINE_PIXEL_ID_Cam_Front30;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ONLINE_PIXEL_ID_Cam_Rear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ONLINE_PIXEL_ID_Cam_SideRR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ONLINE_PIXEL_ID_Cam_SideRL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ONLINE_PIXEL_ID_Cam_SideFL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ONLINE_PIXEL_ID_Cam_SideFR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ONLINE_PIXEL_ID_Cam_AVMRear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ONLINE_PIXEL_ID_Cam_AVMLeft;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ONLINE_PIXEL_ID_Cam_AVMFront;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ONLINE_PIXEL_ID_Cam_AVMRight;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_TEST_PATTERN_ID_Cam_Front120;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_TEST_PATTERN_ID_Cam_Front30;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_TEST_PATTERN_ID_Cam_Rear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_TEST_PATTERN_ID_Cam_SideRR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_TEST_PATTERN_ID_Cam_SideRL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_TEST_PATTERN_ID_Cam_SideFL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_TEST_PATTERN_ID_Cam_SideFR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_TEST_PATTERN_ID_Cam_AVMRear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_TEST_PATTERN_ID_Cam_AVMLeft;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_TEST_PATTERN_ID_Cam_AVMFront;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_TEST_PATTERN_ID_Cam_AVMRight;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ERRB_ID_Cam_Front120;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ERRB_ID_Cam_Front30;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ERRB_ID_Cam_Rear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ERRB_ID_Cam_SideRR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ERRB_ID_Cam_SideRL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ERRB_ID_Cam_SideFL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ERRB_ID_Cam_SideFR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ERRB_ID_Cam_AVMRear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ERRB_ID_Cam_AVMLeft;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ERRB_ID_Cam_AVMFront;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_SENSOR_ERRB_ID_Cam_AVMRight;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_POC_OpenLoad_Cam_Front30;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_POC_OpenLoad_Cam_Front120;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_POC_OpenLoad_Cam_Rear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_POC_OpenLoad_Cam_SideRR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_POC_OpenLoad_Cam_SideRL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_POC_OpenLoad_Cam_SideFL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_POC_OpenLoad_Cam_SideFR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_POC_OpenLoad_Cam_AVMRear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_POC_OpenLoad_Cam_AVMLeft;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_POC_OpenLoad_Cam_AVMFront;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_POC_OpenLoad_Cam_AVMRight;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_POC_ShortCircuitToGround_Cam_Front30;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_POC_ShortCircuitToGround_Cam_Front120;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_POC_ShortCircuitToGround_Cam_Rear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_POC_ShortCircuitToGround_Cam_SideRR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_POC_ShortCircuitToGround_Cam_SideRL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_POC_ShortCircuitToGround_Cam_SideFL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_POC_ShortCircuitToGround_Cam_SideFR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_POC_ShortCircuitToGround_Cam_AVMRear;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_POC_ShortCircuitToGround_Cam_AVMLeft;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_POC_ShortCircuitToGround_Cam_AVMFront;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_POC_ShortCircuitToGround_Cam_AVMRight;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(cameracfg_Runnable_Events, m_FAULT_DemEvent_DES_UNLOCK_Cam_Front30, m_FAULT_DemEvent_DES_UNLOCK_Cam_Front120, m_FAULT_DemEvent_DES_UNLOCK_Cam_Rear, m_FAULT_DemEvent_DES_UNLOCK_Cam_NA, m_FAULT_DemEvent_DES_UNLOCK_Cam_SideRR, m_FAULT_DemEvent_DES_UNLOCK_Cam_SideRL, m_FAULT_DemEvent_DES_UNLOCK_Cam_SideFL, m_FAULT_DemEvent_DES_UNLOCK_Cam_SideFR, m_FAULT_DemEvent_DES_UNLOCK_Cam_AVMRear, m_FAULT_DemEvent_DES_UNLOCK_Cam_AVMLeft, m_FAULT_DemEvent_DES_UNLOCK_Cam_AVMFront, m_FAULT_DemEvent_DES_UNLOCK_Cam_AVMRight, m_FAULT_DemEvent_DES_VID_LOCK_Cam_Front30, m_FAULT_DemEvent_DES_VID_LOCK_Cam_Front120, m_FAULT_DemEvent_DES_VID_LOCK_Cam_Rear, m_FAULT_DemEvent_DES_VID_LOCK_Cam_NA, m_FAULT_DemEvent_DES_VID_LOCK_Cam_SideRR, m_FAULT_DemEvent_DES_VID_LOCK_Cam_SideRL, m_FAULT_DemEvent_DES_VID_LOCK_Cam_SideFL, m_FAULT_DemEvent_DES_VID_LOCK_Cam_SideFR, m_FAULT_DemEvent_DES_VID_LOCK_Cam_AVMRear, m_FAULT_DemEvent_DES_VID_LOCK_Cam_AVMLeft, m_FAULT_DemEvent_DES_VID_LOCK_Cam_AVMFront, m_FAULT_DemEvent_DES_VID_LOCK_Cam_AVMRight, m_FAULT_DemEvent_DES_IDLE_ERR_FLAG_Cam_Front30, m_FAULT_DemEvent_DES_IDLE_ERR_FLAG_Cam_Front120, m_FAULT_DemEvent_DES_IDLE_ERR_FLAG_Cam_Rear, m_FAULT_DemEvent_DES_IDLE_ERR_FLAG_Cam_NA, m_FAULT_DemEvent_DES_IDLE_ERR_FLAG_Cam_SideRR, m_FAULT_DemEvent_DES_IDLE_ERR_FLAG_Cam_SideRL, m_FAULT_DemEvent_DES_IDLE_ERR_FLAG_Cam_SideFL, m_FAULT_DemEvent_DES_IDLE_ERR_FLAG_Cam_SideFR, m_FAULT_DemEvent_DES_IDLE_ERR_FLAG_Cam_AVMRear, m_FAULT_DemEvent_DES_IDLE_ERR_FLAG_Cam_AVMLeft, m_FAULT_DemEvent_DES_IDLE_ERR_FLAG_Cam_AVMFront, m_FAULT_DemEvent_DES_IDLE_ERR_FLAG_Cam_AVMRight, m_FAULT_DemEvent_DES_MAX_RT_FLAG_0_Cam_Front30, m_FAULT_DemEvent_DES_MAX_RT_FLAG_0_Cam_Front120, m_FAULT_DemEvent_DES_MAX_RT_FLAG_0_Cam_Rear, m_FAULT_DemEvent_DES_MAX_RT_FLAG_0_Cam_NA, m_FAULT_DemEvent_DES_MAX_RT_FLAG_0_Cam_SideRR, m_FAULT_DemEvent_DES_MAX_RT_FLAG_0_Cam_SideRL, m_FAULT_DemEvent_DES_MAX_RT_FLAG_0_Cam_SideFL, m_FAULT_DemEvent_DES_MAX_RT_FLAG_0_Cam_SideFR, m_FAULT_DemEvent_DES_MAX_RT_FLAG_0_Cam_AVMRear, m_FAULT_DemEvent_DES_MAX_RT_FLAG_0_Cam_AVMLeft, m_FAULT_DemEvent_DES_MAX_RT_FLAG_0_Cam_AVMFront, m_FAULT_DemEvent_DES_MAX_RT_FLAG_0_Cam_AVMRight, m_FAULT_DemEvent_DES_RT_CNT_FLAG_0_Cam_Front30, m_FAULT_DemEvent_DES_RT_CNT_FLAG_0_Cam_Front120, m_FAULT_DemEvent_DES_RT_CNT_FLAG_0_Cam_Rear, m_FAULT_DemEvent_DES_RT_CNT_FLAG_0_Cam_NA, m_FAULT_DemEvent_DES_RT_CNT_FLAG_0_Cam_SideRR, m_FAULT_DemEvent_DES_RT_CNT_FLAG_0_Cam_SideRL, m_FAULT_DemEvent_DES_RT_CNT_FLAG_0_Cam_SideFL, m_FAULT_DemEvent_DES_RT_CNT_FLAG_0_Cam_SideFR, m_FAULT_DemEvent_DES_RT_CNT_FLAG_0_Cam_AVMRear, m_FAULT_DemEvent_DES_RT_CNT_FLAG_0_Cam_AVMLeft, m_FAULT_DemEvent_DES_RT_CNT_FLAG_0_Cam_AVMFront, m_FAULT_DemEvent_DES_RT_CNT_FLAG_0_Cam_AVMRight, m_FAULT_DemEvent_DES_DEC_ERR_FLAG_Cam_Front30, m_FAULT_DemEvent_DES_DEC_ERR_FLAG_Cam_Front120, m_FAULT_DemEvent_DES_DEC_ERR_FLAG_Cam_Rear, m_FAULT_DemEvent_DES_DEC_ERR_FLAG_Cam_NA, m_FAULT_DemEvent_DES_DEC_ERR_FLAG_Cam_SideRR, m_FAULT_DemEvent_DES_DEC_ERR_FLAG_Cam_SideRL, m_FAULT_DemEvent_DES_DEC_ERR_FLAG_Cam_SideFL, m_FAULT_DemEvent_DES_DEC_ERR_FLAG_Cam_SideFR, m_FAULT_DemEvent_DES_DEC_ERR_FLAG_Cam_AVMRear, m_FAULT_DemEvent_DES_DEC_ERR_FLAG_Cam_AVMLeft, m_FAULT_DemEvent_DES_DEC_ERR_FLAG_Cam_AVMFront, m_FAULT_DemEvent_DES_DEC_ERR_FLAG_Cam_AVMRight, m_FAULT_DemEvent_DES_VID_PXL_CRC_ERR_0_Cam_Front30, m_FAULT_DemEvent_DES_VID_PXL_CRC_ERR_0_Cam_Front120, m_FAULT_DemEvent_DES_VID_PXL_CRC_ERR_0_Cam_Rear, m_FAULT_DemEvent_DES_VID_PXL_CRC_ERR_0_Cam_NA, m_FAULT_DemEvent_DES_VID_PXL_CRC_ERR_0_Cam_SideRR, m_FAULT_DemEvent_DES_VID_PXL_CRC_ERR_0_Cam_SideRL, m_FAULT_DemEvent_DES_VID_PXL_CRC_ERR_0_Cam_SideFL, m_FAULT_DemEvent_DES_VID_PXL_CRC_ERR_0_Cam_SideFR, m_FAULT_DemEvent_DES_VID_PXL_CRC_ERR_0_Cam_AVMRear, m_FAULT_DemEvent_DES_VID_PXL_CRC_ERR_0_Cam_AVMLeft, m_FAULT_DemEvent_DES_VID_PXL_CRC_ERR_0_Cam_AVMFront, m_FAULT_DemEvent_DES_VID_PXL_CRC_ERR_0_Cam_AVMRight, m_FAULT_DemEvent_DES_LMO_ERR_Cam_Front30, m_FAULT_DemEvent_DES_LMO_ERR_Cam_Front120, m_FAULT_DemEvent_DES_LMO_ERR_Cam_Rear, m_FAULT_DemEvent_DES_LMO_ERR_Cam_NA, m_FAULT_DemEvent_DES_LMO_ERR_Cam_SideRR, m_FAULT_DemEvent_DES_LMO_ERR_Cam_SideRL, m_FAULT_DemEvent_DES_LMO_ERR_Cam_SideFL, m_FAULT_DemEvent_DES_LMO_ERR_Cam_SideFR, m_FAULT_DemEvent_DES_LMO_ERR_Cam_AVMRear, m_FAULT_DemEvent_DES_LMO_ERR_Cam_AVMLeft, m_FAULT_DemEvent_DES_LMO_ERR_Cam_AVMFront, m_FAULT_DemEvent_DES_LMO_ERR_Cam_AVMRight, m_FAULT_DemEvent_DES_LCRC_ERR_Cam_Front30, m_FAULT_DemEvent_DES_LCRC_ERR_Cam_Front120, m_FAULT_DemEvent_DES_LCRC_ERR_Cam_Rear, m_FAULT_DemEvent_DES_LCRC_ERR_Cam_NA, m_FAULT_DemEvent_DES_LCRC_ERR_Cam_SideRR, m_FAULT_DemEvent_DES_LCRC_ERR_Cam_SideRL, m_FAULT_DemEvent_DES_LCRC_ERR_Cam_SideFL, m_FAULT_DemEvent_DES_LCRC_ERR_Cam_SideFR, m_FAULT_DemEvent_DES_LCRC_ERR_Cam_AVMRear, m_FAULT_DemEvent_DES_LCRC_ERR_Cam_AVMLeft, m_FAULT_DemEvent_DES_LCRC_ERR_Cam_AVMFront, m_FAULT_DemEvent_DES_LCRC_ERR_Cam_AVMRight, m_FAULT_DemEvent_DES_MEM_ECC_ERR2_Cam_Front30, m_FAULT_DemEvent_DES_MEM_ECC_ERR2_Cam_Front120, m_FAULT_DemEvent_DES_MEM_ECC_ERR2_Cam_Rear, m_FAULT_DemEvent_DES_MEM_ECC_ERR2_Cam_NA, m_FAULT_DemEvent_DES_MEM_ECC_ERR2_Cam_SideRR, m_FAULT_DemEvent_DES_MEM_ECC_ERR2_Cam_SideRL, m_FAULT_DemEvent_DES_MEM_ECC_ERR2_Cam_SideFL, m_FAULT_DemEvent_DES_MEM_ECC_ERR2_Cam_SideFR, m_FAULT_DemEvent_DES_MEM_ECC_ERR2_Cam_AVMRear, m_FAULT_DemEvent_DES_MEM_ECC_ERR2_Cam_AVMLeft, m_FAULT_DemEvent_DES_MEM_ECC_ERR2_Cam_AVMFront, m_FAULT_DemEvent_DES_MEM_ECC_ERR2_Cam_AVMRight, m_FAULT_DemEvent_DES_ERRB_Cam_Front30, m_FAULT_DemEvent_DES_ERRB_Cam_Front120, m_FAULT_DemEvent_DES_ERRB_Cam_Rear, m_FAULT_DemEvent_DES_ERRB_Cam_NA, m_FAULT_DemEvent_DES_ERRB_Cam_SideRR, m_FAULT_DemEvent_DES_ERRB_Cam_SideRL, m_FAULT_DemEvent_DES_ERRB_Cam_SideFL, m_FAULT_DemEvent_DES_ERRB_Cam_SideFR, m_FAULT_DemEvent_DES_ERRB_Cam_AVMRear, m_FAULT_DemEvent_DES_ERRB_Cam_AVMLeft, m_FAULT_DemEvent_DES_ERRB_Cam_AVMFront, m_FAULT_DemEvent_DES_ERRB_Cam_AVMRight, m_FAULT_DemEvent_CAMERA_INIT_ID_Cam_Front120, m_FAULT_DemEvent_CAMERA_INIT_ID_Cam_Front30, m_FAULT_DemEvent_CAMERA_INIT_ID_Cam_Rear, m_FAULT_DemEvent_CAMERA_INIT_ID_Cam_SideRR, m_FAULT_DemEvent_CAMERA_INIT_ID_Cam_SideRL, m_FAULT_DemEvent_CAMERA_INIT_ID_Cam_SideFL, m_FAULT_DemEvent_CAMERA_INIT_ID_Cam_SideFR, m_FAULT_DemEvent_CAMERA_INIT_ID_Cam_AVMRear, m_FAULT_DemEvent_CAMERA_INIT_ID_Cam_AVMLeft, m_FAULT_DemEvent_CAMERA_INIT_ID_Cam_AVMFront, m_FAULT_DemEvent_CAMERA_INIT_ID_Cam_AVMRight, m_FAULT_DemEvent_SENSOR_STREAM_OFF_ID_Cam_Front120, m_FAULT_DemEvent_SENSOR_STREAM_OFF_ID_Cam_Front30, m_FAULT_DemEvent_SENSOR_STREAM_OFF_ID_Cam_Rear, m_FAULT_DemEvent_SENSOR_STREAM_OFF_ID_Cam_SideRR, m_FAULT_DemEvent_SENSOR_STREAM_OFF_ID_Cam_SideRL, m_FAULT_DemEvent_SENSOR_STREAM_OFF_ID_Cam_SideFL, m_FAULT_DemEvent_SENSOR_STREAM_OFF_ID_Cam_SideFR, m_FAULT_DemEvent_SENSOR_STREAM_OFF_ID_Cam_AVMRear, m_FAULT_DemEvent_SENSOR_STREAM_OFF_ID_Cam_AVMLeft, m_FAULT_DemEvent_SENSOR_STREAM_OFF_ID_Cam_AVMFront, m_FAULT_DemEvent_SENSOR_STREAM_OFF_ID_Cam_AVMRight, m_FAULT_DemEvent_SENSOR_VOLTAGE_ID_Cam_Front120, m_FAULT_DemEvent_SENSOR_VOLTAGE_ID_Cam_Front30, m_FAULT_DemEvent_SENSOR_VOLTAGE_ID_Cam_Rear, m_FAULT_DemEvent_SENSOR_VOLTAGE_ID_Cam_SideRR, m_FAULT_DemEvent_SENSOR_VOLTAGE_ID_Cam_SideRL, m_FAULT_DemEvent_SENSOR_VOLTAGE_ID_Cam_SideFL, m_FAULT_DemEvent_SENSOR_VOLTAGE_ID_Cam_SideFR, m_FAULT_DemEvent_SENSOR_VOLTAGE_ID_Cam_AVMRear, m_FAULT_DemEvent_SENSOR_VOLTAGE_ID_Cam_AVMLeft, m_FAULT_DemEvent_SENSOR_VOLTAGE_ID_Cam_AVMFront, m_FAULT_DemEvent_SENSOR_VOLTAGE_ID_Cam_AVMRight, m_FAULT_DemEvent_SENSOR_FCNT_ID_Cam_Front120, m_FAULT_DemEvent_SENSOR_FCNT_ID_Cam_Front30, m_FAULT_DemEvent_SENSOR_FCNT_ID_Cam_Rear, m_FAULT_DemEvent_SENSOR_FCNT_ID_Cam_SideRR, m_FAULT_DemEvent_SENSOR_FCNT_ID_Cam_SideRL, m_FAULT_DemEvent_SENSOR_FCNT_ID_Cam_SideFL, m_FAULT_DemEvent_SENSOR_FCNT_ID_Cam_SideFR, m_FAULT_DemEvent_SENSOR_FCNT_ID_Cam_AVMRear, m_FAULT_DemEvent_SENSOR_FCNT_ID_Cam_AVMLeft, m_FAULT_DemEvent_SENSOR_FCNT_ID_Cam_AVMFront, m_FAULT_DemEvent_SENSOR_FCNT_ID_Cam_AVMRight, m_FAULT_DemEvent_SENSOR_TEMPER_ID_Cam_Front120, m_FAULT_DemEvent_SENSOR_TEMPER_ID_Cam_Front30, m_FAULT_DemEvent_SENSOR_TEMPER_ID_Cam_Rear, m_FAULT_DemEvent_SENSOR_TEMPER_ID_Cam_SideRR, m_FAULT_DemEvent_SENSOR_TEMPER_ID_Cam_SideRL, m_FAULT_DemEvent_SENSOR_TEMPER_ID_Cam_SideFL, m_FAULT_DemEvent_SENSOR_TEMPER_ID_Cam_SideFR, m_FAULT_DemEvent_SENSOR_TEMPER_ID_Cam_AVMRear, m_FAULT_DemEvent_SENSOR_TEMPER_ID_Cam_AVMLeft, m_FAULT_DemEvent_SENSOR_TEMPER_ID_Cam_AVMFront, m_FAULT_DemEvent_SENSOR_TEMPER_ID_Cam_AVMRight, m_FAULT_DemEvent_SENSOR_ROW_COLUMN_ID_Cam_Front120, m_FAULT_DemEvent_SENSOR_ROW_COLUMN_ID_Cam_Front30, m_FAULT_DemEvent_SENSOR_ROW_COLUMN_ID_Cam_Rear, m_FAULT_DemEvent_SENSOR_ROW_COLUMN_ID_Cam_SideRR, m_FAULT_DemEvent_SENSOR_ROW_COLUMN_ID_Cam_SideRL, m_FAULT_DemEvent_SENSOR_ROW_COLUMN_ID_Cam_SideFL, m_FAULT_DemEvent_SENSOR_ROW_COLUMN_ID_Cam_SideFR, m_FAULT_DemEvent_SENSOR_ROW_COLUMN_ID_Cam_AVMRear, m_FAULT_DemEvent_SENSOR_ROW_COLUMN_ID_Cam_AVMLeft, m_FAULT_DemEvent_SENSOR_ROW_COLUMN_ID_Cam_AVMFront, m_FAULT_DemEvent_SENSOR_ROW_COLUMN_ID_Cam_AVMRight, m_FAULT_DemEvent_SENSOR_PLL_CLOCK_ID_Cam_Front120, m_FAULT_DemEvent_SENSOR_PLL_CLOCK_ID_Cam_Front30, m_FAULT_DemEvent_SENSOR_PLL_CLOCK_ID_Cam_Rear, m_FAULT_DemEvent_SENSOR_PLL_CLOCK_ID_Cam_SideRR, m_FAULT_DemEvent_SENSOR_PLL_CLOCK_ID_Cam_SideRL, m_FAULT_DemEvent_SENSOR_PLL_CLOCK_ID_Cam_SideFL, m_FAULT_DemEvent_SENSOR_PLL_CLOCK_ID_Cam_SideFR, m_FAULT_DemEvent_SENSOR_PLL_CLOCK_ID_Cam_AVMRear, m_FAULT_DemEvent_SENSOR_PLL_CLOCK_ID_Cam_AVMLeft, m_FAULT_DemEvent_SENSOR_PLL_CLOCK_ID_Cam_AVMFront, m_FAULT_DemEvent_SENSOR_PLL_CLOCK_ID_Cam_AVMRight, m_FAULT_DemEvent_SENSOR_I2C_CRC_ID_Cam_Front120, m_FAULT_DemEvent_SENSOR_I2C_CRC_ID_Cam_Front30, m_FAULT_DemEvent_SENSOR_I2C_CRC_ID_Cam_Rear, m_FAULT_DemEvent_SENSOR_I2C_CRC_ID_Cam_SideRR, m_FAULT_DemEvent_SENSOR_I2C_CRC_ID_Cam_SideRL, m_FAULT_DemEvent_SENSOR_I2C_CRC_ID_Cam_SideFL, m_FAULT_DemEvent_SENSOR_I2C_CRC_ID_Cam_SideFR, m_FAULT_DemEvent_SENSOR_I2C_CRC_ID_Cam_AVMRear, m_FAULT_DemEvent_SENSOR_I2C_CRC_ID_Cam_AVMLeft, m_FAULT_DemEvent_SENSOR_I2C_CRC_ID_Cam_AVMFront, m_FAULT_DemEvent_SENSOR_I2C_CRC_ID_Cam_AVMRight, m_FAULT_DemEvent_SENSOR_SCCB_ID_Cam_Front120, m_FAULT_DemEvent_SENSOR_SCCB_ID_Cam_Front30, m_FAULT_DemEvent_SENSOR_SCCB_ID_Cam_Rear, m_FAULT_DemEvent_SENSOR_SCCB_ID_Cam_SideRR, m_FAULT_DemEvent_SENSOR_SCCB_ID_Cam_SideRL, m_FAULT_DemEvent_SENSOR_SCCB_ID_Cam_SideFL, m_FAULT_DemEvent_SENSOR_SCCB_ID_Cam_SideFR, m_FAULT_DemEvent_SENSOR_SCCB_ID_Cam_AVMRear, m_FAULT_DemEvent_SENSOR_SCCB_ID_Cam_AVMLeft, m_FAULT_DemEvent_SENSOR_SCCB_ID_Cam_AVMFront, m_FAULT_DemEvent_SENSOR_SCCB_ID_Cam_AVMRight, m_FAULT_DemEvent_SENSOR_RAM_CRC_ID_Cam_Front120, m_FAULT_DemEvent_SENSOR_RAM_CRC_ID_Cam_Front30, m_FAULT_DemEvent_SENSOR_RAM_CRC_ID_Cam_Rear, m_FAULT_DemEvent_SENSOR_RAM_CRC_ID_Cam_SideRR, m_FAULT_DemEvent_SENSOR_RAM_CRC_ID_Cam_SideRL, m_FAULT_DemEvent_SENSOR_RAM_CRC_ID_Cam_SideFL, m_FAULT_DemEvent_SENSOR_RAM_CRC_ID_Cam_SideFR, m_FAULT_DemEvent_SENSOR_RAM_CRC_ID_Cam_AVMRear, m_FAULT_DemEvent_SENSOR_RAM_CRC_ID_Cam_AVMLeft, m_FAULT_DemEvent_SENSOR_RAM_CRC_ID_Cam_AVMFront, m_FAULT_DemEvent_SENSOR_RAM_CRC_ID_Cam_AVMRight, m_FAULT_DemEvent_SENSOR_ROM_CRC_ID_Cam_Front120, m_FAULT_DemEvent_SENSOR_ROM_CRC_ID_Cam_Front30, m_FAULT_DemEvent_SENSOR_ROM_CRC_ID_Cam_Rear, m_FAULT_DemEvent_SENSOR_ROM_CRC_ID_Cam_SideRR, m_FAULT_DemEvent_SENSOR_ROM_CRC_ID_Cam_SideRL, m_FAULT_DemEvent_SENSOR_ROM_CRC_ID_Cam_SideFL, m_FAULT_DemEvent_SENSOR_ROM_CRC_ID_Cam_SideFR, m_FAULT_DemEvent_SENSOR_ROM_CRC_ID_Cam_AVMRear, m_FAULT_DemEvent_SENSOR_ROM_CRC_ID_Cam_AVMLeft, m_FAULT_DemEvent_SENSOR_ROM_CRC_ID_Cam_AVMFront, m_FAULT_DemEvent_SENSOR_ROM_CRC_ID_Cam_AVMRight, m_FAULT_DemEvent_SENSOR_ONLINE_PIXEL_ID_Cam_Front120, m_FAULT_DemEvent_SENSOR_ONLINE_PIXEL_ID_Cam_Front30, m_FAULT_DemEvent_SENSOR_ONLINE_PIXEL_ID_Cam_Rear, m_FAULT_DemEvent_SENSOR_ONLINE_PIXEL_ID_Cam_SideRR, m_FAULT_DemEvent_SENSOR_ONLINE_PIXEL_ID_Cam_SideRL, m_FAULT_DemEvent_SENSOR_ONLINE_PIXEL_ID_Cam_SideFL, m_FAULT_DemEvent_SENSOR_ONLINE_PIXEL_ID_Cam_SideFR, m_FAULT_DemEvent_SENSOR_ONLINE_PIXEL_ID_Cam_AVMRear, m_FAULT_DemEvent_SENSOR_ONLINE_PIXEL_ID_Cam_AVMLeft, m_FAULT_DemEvent_SENSOR_ONLINE_PIXEL_ID_Cam_AVMFront, m_FAULT_DemEvent_SENSOR_ONLINE_PIXEL_ID_Cam_AVMRight, m_FAULT_DemEvent_SENSOR_TEST_PATTERN_ID_Cam_Front120, m_FAULT_DemEvent_SENSOR_TEST_PATTERN_ID_Cam_Front30, m_FAULT_DemEvent_SENSOR_TEST_PATTERN_ID_Cam_Rear, m_FAULT_DemEvent_SENSOR_TEST_PATTERN_ID_Cam_SideRR, m_FAULT_DemEvent_SENSOR_TEST_PATTERN_ID_Cam_SideRL, m_FAULT_DemEvent_SENSOR_TEST_PATTERN_ID_Cam_SideFL, m_FAULT_DemEvent_SENSOR_TEST_PATTERN_ID_Cam_SideFR, m_FAULT_DemEvent_SENSOR_TEST_PATTERN_ID_Cam_AVMRear, m_FAULT_DemEvent_SENSOR_TEST_PATTERN_ID_Cam_AVMLeft, m_FAULT_DemEvent_SENSOR_TEST_PATTERN_ID_Cam_AVMFront, m_FAULT_DemEvent_SENSOR_TEST_PATTERN_ID_Cam_AVMRight, m_FAULT_DemEvent_SENSOR_ERRB_ID_Cam_Front120, m_FAULT_DemEvent_SENSOR_ERRB_ID_Cam_Front30, m_FAULT_DemEvent_SENSOR_ERRB_ID_Cam_Rear, m_FAULT_DemEvent_SENSOR_ERRB_ID_Cam_SideRR, m_FAULT_DemEvent_SENSOR_ERRB_ID_Cam_SideRL, m_FAULT_DemEvent_SENSOR_ERRB_ID_Cam_SideFL, m_FAULT_DemEvent_SENSOR_ERRB_ID_Cam_SideFR, m_FAULT_DemEvent_SENSOR_ERRB_ID_Cam_AVMRear, m_FAULT_DemEvent_SENSOR_ERRB_ID_Cam_AVMLeft, m_FAULT_DemEvent_SENSOR_ERRB_ID_Cam_AVMFront, m_FAULT_DemEvent_SENSOR_ERRB_ID_Cam_AVMRight, m_FAULT_DemEvent_POC_OpenLoad_Cam_Front30, m_FAULT_DemEvent_POC_OpenLoad_Cam_Front120, m_FAULT_DemEvent_POC_OpenLoad_Cam_Rear, m_FAULT_DemEvent_POC_OpenLoad_Cam_SideRR, m_FAULT_DemEvent_POC_OpenLoad_Cam_SideRL, m_FAULT_DemEvent_POC_OpenLoad_Cam_SideFL, m_FAULT_DemEvent_POC_OpenLoad_Cam_SideFR, m_FAULT_DemEvent_POC_OpenLoad_Cam_AVMRear, m_FAULT_DemEvent_POC_OpenLoad_Cam_AVMLeft, m_FAULT_DemEvent_POC_OpenLoad_Cam_AVMFront, m_FAULT_DemEvent_POC_OpenLoad_Cam_AVMRight, m_FAULT_DemEvent_POC_ShortCircuitToGround_Cam_Front30, m_FAULT_DemEvent_POC_ShortCircuitToGround_Cam_Front120, m_FAULT_DemEvent_POC_ShortCircuitToGround_Cam_Rear, m_FAULT_DemEvent_POC_ShortCircuitToGround_Cam_SideRR, m_FAULT_DemEvent_POC_ShortCircuitToGround_Cam_SideRL, m_FAULT_DemEvent_POC_ShortCircuitToGround_Cam_SideFL, m_FAULT_DemEvent_POC_ShortCircuitToGround_Cam_SideFR, m_FAULT_DemEvent_POC_ShortCircuitToGround_Cam_AVMRear, m_FAULT_DemEvent_POC_ShortCircuitToGround_Cam_AVMLeft, m_FAULT_DemEvent_POC_ShortCircuitToGround_Cam_AVMFront, m_FAULT_DemEvent_POC_ShortCircuitToGround_Cam_AVMRight)

}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::um_cfg::cameracfg_Runnable_Events, StdLayoutTag>
{
    using ValueType = ::um_cfg::cameracfg_Runnable_Events;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::um_cfg::cameracfg_Runnable_Events, StdLayoutTruncTag>
{
    using ValueType = ::um_cfg::cameracfg_Runnable_Events;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
