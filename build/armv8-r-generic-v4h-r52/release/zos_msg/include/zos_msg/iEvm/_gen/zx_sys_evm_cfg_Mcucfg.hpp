#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/container/array.hpp>
#include <basic_types/types.hpp>

#include <zos_msg/common/common.hpp>
#include <zos_msg/iEvm/zx_sys_evm_interfaces.hpp>
namespace um_cfg
{
const zos::uint8_t Mcucfg_Runnable_EventSize_cu8 = 372;
using Mcu_IpcBuffer=zos::Array<zos::uint8_t, Mcucfg_Runnable_EventSize_cu8>;
struct Mcucfg_Runnable_Events
{
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_VOLTAGE_KL30_OVER_LEVEL1;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_VOLTAGE_KL30_OVER_LEVEL2;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_VOLTAGE_KL30_UNDER_LEVEL1;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_VOLTAGE_KL30_UNDER_LEVEL2;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_VOLTAGE_KL15_OVER_LEVEL1;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_VOLTAGE_TPS552892_VOUT_12V_OVER_LIMITATION;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_VOLTAGE_TPS552892_VOUT_12V_UNDER_LIMITATION;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_VOLTAGE_MPQ2918_5V_OVER_LIMITATION;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_VOLTAGE_MPQ2918_5V_UNDER_LIMITATION;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_VOLTAGE_MPQ2179_VOUT_3V3_OVER_LIMITATION;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_VOLTAGE_MPQ2179_VOUT_3V3_UNDER_LIMITATION;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_VOLTAGE_MPQ2179_VOUT_1V8_OVER_LIMITATION;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_VOLTAGE_MPQ2179_VOUT_1V8_UNDER_LIMITATION;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_VOLTAGE_MPQ2179_VOUT_1V2_OVER_LIMITATION;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_VOLTAGE_MPQ2179_VOUT_1V2_UNDER_LIMITATION;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_VOLTAGE_TPS74501_OUT_3V3_OVER_LIMITATION;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_VOLTAGE_TPS74501_OUT_3V3_UNDER_LIMITATION;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_VOLTAGE_TPS74501_OUT_0V9_OVER_LIMITATION;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_VOLTAGE_TPS74501_OUT_0V9_UNDER_LIMITATION;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_VOLTAGE_PCAN_5V_OVER_LIMITATION;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_VOLTAGE_PCAN_5V_UNDER_LIMITATION;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PROCESS_SOC_PCBA_VERSION_ERROR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_TEMPERATURE_SENSOR_TU1_TOO_HIGH;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_TEMPERATURE_SENSOR_TU1_TOO_LOW;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_TEMPERATURE_SENSOR_TU2_TOO_HIGH;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_TEMPERATURE_SENSOR_TU2_TOO_LOW;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_TEMPERATURE_SENSOR_TU3_TOO_HIGH;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_TEMPERATURE_SENSOR_TU3_TOO_LOW;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_TEMPERATURE_SENSOR_TU4_TOO_HIGH;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_TEMPERATURE_SENSOR_TU4_TOO_LOW;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_TEMPERATURE_SOC_INNER_TOO_HIGH;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_TEMPERATURE_SOC_INNER_TOO_LOW;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_TEMPERATURE_CAN_TRANSCEIVER_TOO_HIGH;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_TEMPERATURE_OverTemperWarning;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_TEMPERATURE_OverTemperAlarm;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_TEMPERATURE_OverTemperShutdown;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_TEMPERATURE_UnderTemperAlarm;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_TEMPERATURE_UnderTemperShutdown;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_OVP;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_RLOVP;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_UVP;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_RLUVP;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_OCP;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_CCL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_THERM0;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_THERM1;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_DRONSF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_PG;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_BISTF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_BISTC;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_ECCDED;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_ECCSEC;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_STRAP;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_NVMCMDC;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_PEC;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_RTCRC;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_VINSENUV;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_VIN_OV;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_VIN_UV;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_TEMPA_P0;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_TEMPB_P0;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_VIN_OV_P0;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_VIN_UVLO_P0;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_OSC2_P0;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_I2C_SELF_CHK_P0;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_MTP_CRC_P0;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_SYS_FSM_P0;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_TEMPA_P1;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_TEMPB_P1;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_VIN_OV_P1;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_VIN_UVLO_P1;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_OSC2_P1;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_I2C_SELF_CHK_P1;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_MTP_CRC_P1;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_SYS_FSM_P1;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_OT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_OTWARNING;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_OT_BIST;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_MNT_VINOV;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_MNT_VINUV;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_WD;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_EXTERNAL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_VCCOV;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_VCCUV;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_DUPLICATEBIT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_I2C_PEC;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_I2C_WRITE;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_RT_VM_CRC;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_I2C_ILLEGAL_WRITE;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_OTP_CRC;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_OTP_LOAD;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_LBIST;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_ABIST;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_BG_CLK;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMIC_MONEVENT_DAC;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_BUCK1_OV_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_BUCK1_UV_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_BUCK1_SC_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_BUCK2_OV_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_BUCK2_UV_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_BUCK2_SC_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_BUCK3_OV_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_BUCK3_UV_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_BUCK3_SC_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_BUCK4_OV_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_BUCK4_UV_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_BUCK4_SC_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_LDO1_OV_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_LDO1_UV_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_LDO1_SC_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_LDO2_OV_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_LDO2_UV_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_LDO2_SC_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_LDO3_OV_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_LDO3_UV_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_LDO3_SC_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_LDO4_OV_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_LDO4_UV_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_LDO4_SC_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_VCCA_UV_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_VCCA_OV_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_PFSM_ERR_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_VCCA_OVP_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_TSD_IMM_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_REG_CRC_ERR_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_BIST_FAIL_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DramPwr_PMIC_MONEVENT_BUCK1_UVOV;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DramPwr_PMIC_MONEVENT_BUCK2_UVOV;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DramPwr_PMIC_MONEVENT_BUCK3_UVOV;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DramPwr_PMIC_MONEVENT_BUCK4_UVOV;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DramPwr_PMIC_MONEVENT_LDO1_UVOV;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DramPwr_PMIC_MONEVENT_LDO2_UVOV;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DramPwr_PMIC_MONEVENT_LDO3_UVOV;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DramPwr_PMIC_MONEVENT_VCCA_UVOV;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DramPwr_PMIC_MONEVENT_VMON1_UVOV;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_F_UVHF4;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_F_UVHF3;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_F_UVHF1;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_F_OVHF4;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_F_OVHF3;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_F_OVHF1;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_FREQ_DEV_ERROR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_F_NIRQ;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_F_TSD;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_ESM_MCU_RST_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_ESM_MCU_FAIL_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_ESM_MCU_PIN_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_BUCK1_ILIM_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_BUCK2_ILIM_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_BUCK3_ILIM_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_BUCK4_ILIM_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_LDO1_ILIM_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_LDO2_ILIM_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_LDO3_ILIM_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_LDO4_ILIM_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_RTC;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_ENABLE_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_RTC_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_RTC_ALARM_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_RTC_TIMER_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_TWARN_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_BIST_PASS_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_NRSTOUT_READBACK_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_NINT_READBACK_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_RECOV_CNT_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_TSD_ORD_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_SOC_PWR_ERR_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_MCU_PWR_ERR_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_ORD_SHUTDOWN_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_IMM_SHUTDOWN_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_I2C2_ADR_ERR_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_I2C2_CRC_ERR_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_COMM_ADR_ERR_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_COMM_CRC_ERR_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_COMM_FRM_ERR_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_EN_DRV_READBACK_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_WD_RST_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_WD_FAIL_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_WD_ANSW_ERR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_WD_SEQ_ERR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_WD_ANSW_EARLY;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_WD_TRIG_EARLY;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_WD_TIMEOUT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_PMICA_WD_LONGWIN_TIMEOUT_INT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DramPwr_PMIC_MONEVENT_REG_UNLOCK;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DramPwr_PMIC_MONEVENT_BG_XMON;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DramPwr_PMIC_MONEVENT_I2C2_ERR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DramPwr_PMIC_MONEVENT_COMM_ERR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_F_CRC;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_F_PEC;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_ECC_SEC;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_ECC_DED;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_LDO_OV_ERROR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_NRST_MISMATCH;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_SHORT_DET;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_OPEN_DET;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_VCOREMON_OV;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_VCOREMON_UV;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_VDDIO_OV;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_VDDIO_UV;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_VMON4_OV;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_VMON4_UV;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_VMON3_OV;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_VMON3_UV;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_VMON2_OV;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_VMON2_UV;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_VMON1_OV;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_VMON1_UV;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_HVLDO_VMON_OV;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_HVLDO_VMON_UV;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_FS_DIG_REF_OV;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_FS_OSC_DRIFT;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_FS_FCCU12;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_FS_FCCU1;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_FS_FCCU2;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_LPCLK_FREQ2HIGH;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_LPCLK_FREQ2LOW;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_I2C_FS_CRC;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_I2C_FS_REQ;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_ALL;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_RESERVED;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Mipi_Rx0_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Mipi_Rx0_NCF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Mipi_Rx1_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Mipi_Rx1_NCF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Mipi_Rx4_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Mipi_Rx4_NCF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Mipi_Txcsi0_NCF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Mipi_Txcsi1_NCF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Mipi_Dsi_NCF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Idu_NCF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Cim_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Cim_NCF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Isp0_NCF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Isp1_NCF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Gdc_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Gdc_NCF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Pym_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Pym_NCF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Stitch_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Stitch_NCF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Ynr_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Ynr_NCF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Vdsp0_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Vdsp0_NCF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_A78_Core_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_A78_Core_NCF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Ddr_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Ddr_NCF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Gpio_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Lpwm_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_I2c0_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_I2c1_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_I2c2_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_I2c3_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_I2c4_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_I2c5_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Spi0_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Spi1_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Uart0_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Uart1_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Uart2_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Uart3_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Gpu0_NCF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Pcie_NCF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Eth0_NCF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Eth1_NCF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Mmc_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Mmc_NCF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Wwdt4_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Wwdt5_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Wwdt6_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Wwdt7_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Wwdt8_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Wwdt9_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Sysreg_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Spinlock_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Smmu_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Bus_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Int_Router_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Ipc_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Bdiso_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Gic_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Pdma_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Pdma_NCF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Clock_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Pmu_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Firewall_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Bpu_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Bpu_NCF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Fchm_Sw_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Bus_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Adc_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Adc_NCF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Firewall_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Bdiso_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Clock_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Stcu_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_I2c6_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_I2c7_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_I2c8_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_I2c9_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Spi2_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Spi3_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Spi4_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Spi5_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Spi6_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Spi7_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Uart4_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Uart5_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Uart6_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Gpio_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Pwm_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Canfd_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Fchm_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_R52_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_R52_NCF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Ipc_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Int_Router_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Mdma_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Mdma_NCF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Pdma_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Pdma_NCF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Sram_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Sram_NCF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Sysreg_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Timer_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Pmu_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Wwdt0_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Wwdt1_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Wwdt2_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Otfcrc_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Xspi_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Rtc_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Eth_NCF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Spinlock_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Selfcheck_NCF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Selfcheck_NCF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Periodcheck_NCF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Periodcheck_NCF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Acore_Heart_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Bpu_Heart_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Vdsp_Heart_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Temperature_Sw_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Temperature_Hw_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Temperature_Sw_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Voltage_Sw_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Voltage_Hw_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Voltage_Sw_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Wwdt4_Timeout_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Wwdt5_Timeout_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Wwdt6_Timeout_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Wwdt7_Timeout_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Wwdt8_Timeout_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Wwdt9_Timeout_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Wwdt_Vdsp_Timeout_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Wwdtspu_Bpu_Timeout_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMain_Mapped_Wwdtapm_Bpu_Timeout_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Pmic_Err0_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Pmic_Err0_NCF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Pmic_Err1_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Pmic_Err1_NCF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Pmic_Err2_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Pmic_Err2_NCF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Pmic_Err3_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Pmic_Err3_NCF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Pmic_Err4_CF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ModuleMcu_Mapped_Pmic_Err4_NCF;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Startup_CurrentSlotFailedAnotherSuccess;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_Startup_EnterRecoverySlot;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_ParkCam_NotCalibration;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_DriveCam_NotCalibration;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(Mcucfg_Runnable_Events, m_FAULT_DemEvent_VOLTAGE_KL30_OVER_LEVEL1, m_FAULT_DemEvent_VOLTAGE_KL30_OVER_LEVEL2, m_FAULT_DemEvent_VOLTAGE_KL30_UNDER_LEVEL1, m_FAULT_DemEvent_VOLTAGE_KL30_UNDER_LEVEL2, m_FAULT_DemEvent_VOLTAGE_KL15_OVER_LEVEL1, m_FAULT_DemEvent_VOLTAGE_TPS552892_VOUT_12V_OVER_LIMITATION, m_FAULT_DemEvent_VOLTAGE_TPS552892_VOUT_12V_UNDER_LIMITATION, m_FAULT_DemEvent_VOLTAGE_MPQ2918_5V_OVER_LIMITATION, m_FAULT_DemEvent_VOLTAGE_MPQ2918_5V_UNDER_LIMITATION, m_FAULT_DemEvent_VOLTAGE_MPQ2179_VOUT_3V3_OVER_LIMITATION, m_FAULT_DemEvent_VOLTAGE_MPQ2179_VOUT_3V3_UNDER_LIMITATION, m_FAULT_DemEvent_VOLTAGE_MPQ2179_VOUT_1V8_OVER_LIMITATION, m_FAULT_DemEvent_VOLTAGE_MPQ2179_VOUT_1V8_UNDER_LIMITATION, m_FAULT_DemEvent_VOLTAGE_MPQ2179_VOUT_1V2_OVER_LIMITATION, m_FAULT_DemEvent_VOLTAGE_MPQ2179_VOUT_1V2_UNDER_LIMITATION, m_FAULT_DemEvent_VOLTAGE_TPS74501_OUT_3V3_OVER_LIMITATION, m_FAULT_DemEvent_VOLTAGE_TPS74501_OUT_3V3_UNDER_LIMITATION, m_FAULT_DemEvent_VOLTAGE_TPS74501_OUT_0V9_OVER_LIMITATION, m_FAULT_DemEvent_VOLTAGE_TPS74501_OUT_0V9_UNDER_LIMITATION, m_FAULT_DemEvent_VOLTAGE_PCAN_5V_OVER_LIMITATION, m_FAULT_DemEvent_VOLTAGE_PCAN_5V_UNDER_LIMITATION, m_FAULT_DemEvent_PROCESS_SOC_PCBA_VERSION_ERROR, m_FAULT_DemEvent_TEMPERATURE_SENSOR_TU1_TOO_HIGH, m_FAULT_DemEvent_TEMPERATURE_SENSOR_TU1_TOO_LOW, m_FAULT_DemEvent_TEMPERATURE_SENSOR_TU2_TOO_HIGH, m_FAULT_DemEvent_TEMPERATURE_SENSOR_TU2_TOO_LOW, m_FAULT_DemEvent_TEMPERATURE_SENSOR_TU3_TOO_HIGH, m_FAULT_DemEvent_TEMPERATURE_SENSOR_TU3_TOO_LOW, m_FAULT_DemEvent_TEMPERATURE_SENSOR_TU4_TOO_HIGH, m_FAULT_DemEvent_TEMPERATURE_SENSOR_TU4_TOO_LOW, m_FAULT_DemEvent_TEMPERATURE_SOC_INNER_TOO_HIGH, m_FAULT_DemEvent_TEMPERATURE_SOC_INNER_TOO_LOW, m_FAULT_DemEvent_TEMPERATURE_CAN_TRANSCEIVER_TOO_HIGH, m_FAULT_DemEvent_TEMPERATURE_OverTemperWarning, m_FAULT_DemEvent_TEMPERATURE_OverTemperAlarm, m_FAULT_DemEvent_TEMPERATURE_OverTemperShutdown, m_FAULT_DemEvent_TEMPERATURE_UnderTemperAlarm, m_FAULT_DemEvent_TEMPERATURE_UnderTemperShutdown, m_FAULT_DemEvent_PMIC_MONEVENT_OVP, m_FAULT_DemEvent_PMIC_MONEVENT_RLOVP, m_FAULT_DemEvent_PMIC_MONEVENT_UVP, m_FAULT_DemEvent_PMIC_MONEVENT_RLUVP, m_FAULT_DemEvent_PMIC_MONEVENT_OCP, m_FAULT_DemEvent_PMIC_MONEVENT_CCL, m_FAULT_DemEvent_PMIC_MONEVENT_THERM0, m_FAULT_DemEvent_PMIC_MONEVENT_THERM1, m_FAULT_DemEvent_PMIC_MONEVENT_DRONSF, m_FAULT_DemEvent_PMIC_MONEVENT_PG, m_FAULT_DemEvent_PMIC_MONEVENT_BISTF, m_FAULT_DemEvent_PMIC_MONEVENT_BISTC, m_FAULT_DemEvent_PMIC_MONEVENT_ECCDED, m_FAULT_DemEvent_PMIC_MONEVENT_ECCSEC, m_FAULT_DemEvent_PMIC_MONEVENT_STRAP, m_FAULT_DemEvent_PMIC_MONEVENT_NVMCMDC, m_FAULT_DemEvent_PMIC_MONEVENT_PEC, m_FAULT_DemEvent_PMIC_MONEVENT_RTCRC, m_FAULT_DemEvent_PMIC_MONEVENT_VINSENUV, m_FAULT_DemEvent_PMIC_MONEVENT_VIN_OV, m_FAULT_DemEvent_PMIC_MONEVENT_VIN_UV, m_FAULT_DemEvent_PMIC_MONEVENT_TEMPA_P0, m_FAULT_DemEvent_PMIC_MONEVENT_TEMPB_P0, m_FAULT_DemEvent_PMIC_MONEVENT_VIN_OV_P0, m_FAULT_DemEvent_PMIC_MONEVENT_VIN_UVLO_P0, m_FAULT_DemEvent_PMIC_MONEVENT_OSC2_P0, m_FAULT_DemEvent_PMIC_MONEVENT_I2C_SELF_CHK_P0, m_FAULT_DemEvent_PMIC_MONEVENT_MTP_CRC_P0, m_FAULT_DemEvent_PMIC_MONEVENT_SYS_FSM_P0, m_FAULT_DemEvent_PMIC_MONEVENT_TEMPA_P1, m_FAULT_DemEvent_PMIC_MONEVENT_TEMPB_P1, m_FAULT_DemEvent_PMIC_MONEVENT_VIN_OV_P1, m_FAULT_DemEvent_PMIC_MONEVENT_VIN_UVLO_P1, m_FAULT_DemEvent_PMIC_MONEVENT_OSC2_P1, m_FAULT_DemEvent_PMIC_MONEVENT_I2C_SELF_CHK_P1, m_FAULT_DemEvent_PMIC_MONEVENT_MTP_CRC_P1, m_FAULT_DemEvent_PMIC_MONEVENT_SYS_FSM_P1, m_FAULT_DemEvent_PMIC_MONEVENT_OT, m_FAULT_DemEvent_PMIC_MONEVENT_OTWARNING, m_FAULT_DemEvent_PMIC_MONEVENT_OT_BIST, m_FAULT_DemEvent_PMIC_MONEVENT_MNT_VINOV, m_FAULT_DemEvent_PMIC_MONEVENT_MNT_VINUV, m_FAULT_DemEvent_PMIC_MONEVENT_WD, m_FAULT_DemEvent_PMIC_MONEVENT_EXTERNAL, m_FAULT_DemEvent_PMIC_MONEVENT_VCCOV, m_FAULT_DemEvent_PMIC_MONEVENT_VCCUV, m_FAULT_DemEvent_PMIC_MONEVENT_DUPLICATEBIT, m_FAULT_DemEvent_PMIC_MONEVENT_I2C_PEC, m_FAULT_DemEvent_PMIC_MONEVENT_I2C_WRITE, m_FAULT_DemEvent_PMIC_MONEVENT_RT_VM_CRC, m_FAULT_DemEvent_PMIC_MONEVENT_I2C_ILLEGAL_WRITE, m_FAULT_DemEvent_PMIC_MONEVENT_OTP_CRC, m_FAULT_DemEvent_PMIC_MONEVENT_OTP_LOAD, m_FAULT_DemEvent_PMIC_MONEVENT_LBIST, m_FAULT_DemEvent_PMIC_MONEVENT_ABIST, m_FAULT_DemEvent_PMIC_MONEVENT_BG_CLK, m_FAULT_DemEvent_PMIC_MONEVENT_DAC, m_FAULT_DemEvent_PMICA_BUCK1_OV_INT, m_FAULT_DemEvent_PMICA_BUCK1_UV_INT, m_FAULT_DemEvent_PMICA_BUCK1_SC_INT, m_FAULT_DemEvent_PMICA_BUCK2_OV_INT, m_FAULT_DemEvent_PMICA_BUCK2_UV_INT, m_FAULT_DemEvent_PMICA_BUCK2_SC_INT, m_FAULT_DemEvent_PMICA_BUCK3_OV_INT, m_FAULT_DemEvent_PMICA_BUCK3_UV_INT, m_FAULT_DemEvent_PMICA_BUCK3_SC_INT, m_FAULT_DemEvent_PMICA_BUCK4_OV_INT, m_FAULT_DemEvent_PMICA_BUCK4_UV_INT, m_FAULT_DemEvent_PMICA_BUCK4_SC_INT, m_FAULT_DemEvent_PMICA_LDO1_OV_INT, m_FAULT_DemEvent_PMICA_LDO1_UV_INT, m_FAULT_DemEvent_PMICA_LDO1_SC_INT, m_FAULT_DemEvent_PMICA_LDO2_OV_INT, m_FAULT_DemEvent_PMICA_LDO2_UV_INT, m_FAULT_DemEvent_PMICA_LDO2_SC_INT, m_FAULT_DemEvent_PMICA_LDO3_OV_INT, m_FAULT_DemEvent_PMICA_LDO3_UV_INT, m_FAULT_DemEvent_PMICA_LDO3_SC_INT, m_FAULT_DemEvent_PMICA_LDO4_OV_INT, m_FAULT_DemEvent_PMICA_LDO4_UV_INT, m_FAULT_DemEvent_PMICA_LDO4_SC_INT, m_FAULT_DemEvent_PMICA_VCCA_UV_INT, m_FAULT_DemEvent_PMICA_VCCA_OV_INT, m_FAULT_DemEvent_PMICA_PFSM_ERR_INT, m_FAULT_DemEvent_PMICA_VCCA_OVP_INT, m_FAULT_DemEvent_PMICA_TSD_IMM_INT, m_FAULT_DemEvent_PMICA_REG_CRC_ERR_INT, m_FAULT_DemEvent_PMICA_BIST_FAIL_INT, m_FAULT_DemEvent_DramPwr_PMIC_MONEVENT_BUCK1_UVOV, m_FAULT_DemEvent_DramPwr_PMIC_MONEVENT_BUCK2_UVOV, m_FAULT_DemEvent_DramPwr_PMIC_MONEVENT_BUCK3_UVOV, m_FAULT_DemEvent_DramPwr_PMIC_MONEVENT_BUCK4_UVOV, m_FAULT_DemEvent_DramPwr_PMIC_MONEVENT_LDO1_UVOV, m_FAULT_DemEvent_DramPwr_PMIC_MONEVENT_LDO2_UVOV, m_FAULT_DemEvent_DramPwr_PMIC_MONEVENT_LDO3_UVOV, m_FAULT_DemEvent_DramPwr_PMIC_MONEVENT_VCCA_UVOV, m_FAULT_DemEvent_DramPwr_PMIC_MONEVENT_VMON1_UVOV, m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_F_UVHF4, m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_F_UVHF3, m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_F_UVHF1, m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_F_OVHF4, m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_F_OVHF3, m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_F_OVHF1, m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_FREQ_DEV_ERROR, m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_F_NIRQ, m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_F_TSD, m_FAULT_DemEvent_PMICA_ESM_MCU_RST_INT, m_FAULT_DemEvent_PMICA_ESM_MCU_FAIL_INT, m_FAULT_DemEvent_PMICA_ESM_MCU_PIN_INT, m_FAULT_DemEvent_PMICA_BUCK1_ILIM_INT, m_FAULT_DemEvent_PMICA_BUCK2_ILIM_INT, m_FAULT_DemEvent_PMICA_BUCK3_ILIM_INT, m_FAULT_DemEvent_PMICA_BUCK4_ILIM_INT, m_FAULT_DemEvent_PMICA_LDO1_ILIM_INT, m_FAULT_DemEvent_PMICA_LDO2_ILIM_INT, m_FAULT_DemEvent_PMICA_LDO3_ILIM_INT, m_FAULT_DemEvent_PMICA_LDO4_ILIM_INT, m_FAULT_DemEvent_PMICA_RTC, m_FAULT_DemEvent_PMICA_ENABLE_INT, m_FAULT_DemEvent_PMICA_RTC_INT, m_FAULT_DemEvent_PMICA_RTC_ALARM_INT, m_FAULT_DemEvent_PMICA_RTC_TIMER_INT, m_FAULT_DemEvent_PMICA_TWARN_INT, m_FAULT_DemEvent_PMICA_BIST_PASS_INT, m_FAULT_DemEvent_PMICA_NRSTOUT_READBACK_INT, m_FAULT_DemEvent_PMICA_NINT_READBACK_INT, m_FAULT_DemEvent_PMICA_RECOV_CNT_INT, m_FAULT_DemEvent_PMICA_TSD_ORD_INT, m_FAULT_DemEvent_PMICA_SOC_PWR_ERR_INT, m_FAULT_DemEvent_PMICA_MCU_PWR_ERR_INT, m_FAULT_DemEvent_PMICA_ORD_SHUTDOWN_INT, m_FAULT_DemEvent_PMICA_IMM_SHUTDOWN_INT, m_FAULT_DemEvent_PMICA_I2C2_ADR_ERR_INT, m_FAULT_DemEvent_PMICA_I2C2_CRC_ERR_INT, m_FAULT_DemEvent_PMICA_COMM_ADR_ERR_INT, m_FAULT_DemEvent_PMICA_COMM_CRC_ERR_INT, m_FAULT_DemEvent_PMICA_COMM_FRM_ERR_INT, m_FAULT_DemEvent_PMICA_EN_DRV_READBACK_INT, m_FAULT_DemEvent_PMICA_WD_RST_INT, m_FAULT_DemEvent_PMICA_WD_FAIL_INT, m_FAULT_DemEvent_PMICA_WD_ANSW_ERR, m_FAULT_DemEvent_PMICA_WD_SEQ_ERR, m_FAULT_DemEvent_PMICA_WD_ANSW_EARLY, m_FAULT_DemEvent_PMICA_WD_TRIG_EARLY, m_FAULT_DemEvent_PMICA_WD_TIMEOUT, m_FAULT_DemEvent_PMICA_WD_LONGWIN_TIMEOUT_INT, m_FAULT_DemEvent_DramPwr_PMIC_MONEVENT_REG_UNLOCK, m_FAULT_DemEvent_DramPwr_PMIC_MONEVENT_BG_XMON, m_FAULT_DemEvent_DramPwr_PMIC_MONEVENT_I2C2_ERR, m_FAULT_DemEvent_DramPwr_PMIC_MONEVENT_COMM_ERR, m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_F_CRC, m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_F_PEC, m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_ECC_SEC, m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_ECC_DED, m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_LDO_OV_ERROR, m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_NRST_MISMATCH, m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_SHORT_DET, m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_OPEN_DET, m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_VCOREMON_OV, m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_VCOREMON_UV, m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_VDDIO_OV, m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_VDDIO_UV, m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_VMON4_OV, m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_VMON4_UV, m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_VMON3_OV, m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_VMON3_UV, m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_VMON2_OV, m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_VMON2_UV, m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_VMON1_OV, m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_VMON1_UV, m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_HVLDO_VMON_OV, m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_HVLDO_VMON_UV, m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_FS_DIG_REF_OV, m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_FS_OSC_DRIFT, m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_FS_FCCU12, m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_FS_FCCU1, m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_FS_FCCU2, m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_LPCLK_FREQ2HIGH, m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_LPCLK_FREQ2LOW, m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_I2C_FS_CRC, m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_I2C_FS_REQ, m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_ALL, m_FAULT_DemEvent_MainPwr_PMIC_MONEVENT_RESERVED, m_FAULT_DemEvent_ModuleMain_Mapped_Mipi_Rx0_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Mipi_Rx0_NCF, m_FAULT_DemEvent_ModuleMain_Mapped_Mipi_Rx1_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Mipi_Rx1_NCF, m_FAULT_DemEvent_ModuleMain_Mapped_Mipi_Rx4_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Mipi_Rx4_NCF, m_FAULT_DemEvent_ModuleMain_Mapped_Mipi_Txcsi0_NCF, m_FAULT_DemEvent_ModuleMain_Mapped_Mipi_Txcsi1_NCF, m_FAULT_DemEvent_ModuleMain_Mapped_Mipi_Dsi_NCF, m_FAULT_DemEvent_ModuleMain_Mapped_Idu_NCF, m_FAULT_DemEvent_ModuleMain_Mapped_Cim_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Cim_NCF, m_FAULT_DemEvent_ModuleMain_Mapped_Isp0_NCF, m_FAULT_DemEvent_ModuleMain_Mapped_Isp1_NCF, m_FAULT_DemEvent_ModuleMain_Mapped_Gdc_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Gdc_NCF, m_FAULT_DemEvent_ModuleMain_Mapped_Pym_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Pym_NCF, m_FAULT_DemEvent_ModuleMain_Mapped_Stitch_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Stitch_NCF, m_FAULT_DemEvent_ModuleMain_Mapped_Ynr_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Ynr_NCF, m_FAULT_DemEvent_ModuleMain_Mapped_Vdsp0_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Vdsp0_NCF, m_FAULT_DemEvent_ModuleMain_Mapped_A78_Core_CF, m_FAULT_DemEvent_ModuleMain_Mapped_A78_Core_NCF, m_FAULT_DemEvent_ModuleMain_Mapped_Ddr_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Ddr_NCF, m_FAULT_DemEvent_ModuleMain_Mapped_Gpio_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Lpwm_CF, m_FAULT_DemEvent_ModuleMain_Mapped_I2c0_CF, m_FAULT_DemEvent_ModuleMain_Mapped_I2c1_CF, m_FAULT_DemEvent_ModuleMain_Mapped_I2c2_CF, m_FAULT_DemEvent_ModuleMain_Mapped_I2c3_CF, m_FAULT_DemEvent_ModuleMain_Mapped_I2c4_CF, m_FAULT_DemEvent_ModuleMain_Mapped_I2c5_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Spi0_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Spi1_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Uart0_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Uart1_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Uart2_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Uart3_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Gpu0_NCF, m_FAULT_DemEvent_ModuleMain_Mapped_Pcie_NCF, m_FAULT_DemEvent_ModuleMain_Mapped_Eth0_NCF, m_FAULT_DemEvent_ModuleMain_Mapped_Eth1_NCF, m_FAULT_DemEvent_ModuleMain_Mapped_Mmc_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Mmc_NCF, m_FAULT_DemEvent_ModuleMain_Mapped_Wwdt4_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Wwdt5_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Wwdt6_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Wwdt7_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Wwdt8_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Wwdt9_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Sysreg_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Spinlock_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Smmu_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Bus_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Int_Router_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Ipc_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Bdiso_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Gic_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Pdma_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Pdma_NCF, m_FAULT_DemEvent_ModuleMain_Mapped_Clock_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Pmu_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Firewall_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Bpu_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Bpu_NCF, m_FAULT_DemEvent_ModuleMain_Mapped_Fchm_Sw_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_Bus_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_Adc_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_Adc_NCF, m_FAULT_DemEvent_ModuleMcu_Mapped_Firewall_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_Bdiso_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_Clock_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_Stcu_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_I2c6_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_I2c7_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_I2c8_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_I2c9_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_Spi2_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_Spi3_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_Spi4_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_Spi5_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_Spi6_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_Spi7_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_Uart4_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_Uart5_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_Uart6_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_Gpio_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_Pwm_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_Canfd_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_Fchm_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_R52_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_R52_NCF, m_FAULT_DemEvent_ModuleMcu_Mapped_Ipc_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_Int_Router_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_Mdma_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_Mdma_NCF, m_FAULT_DemEvent_ModuleMcu_Mapped_Pdma_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_Pdma_NCF, m_FAULT_DemEvent_ModuleMcu_Mapped_Sram_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_Sram_NCF, m_FAULT_DemEvent_ModuleMcu_Mapped_Sysreg_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_Timer_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_Pmu_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_Wwdt0_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_Wwdt1_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_Wwdt2_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_Otfcrc_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_Xspi_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_Rtc_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_Eth_NCF, m_FAULT_DemEvent_ModuleMcu_Mapped_Spinlock_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Selfcheck_NCF, m_FAULT_DemEvent_ModuleMcu_Mapped_Selfcheck_NCF, m_FAULT_DemEvent_ModuleMain_Mapped_Periodcheck_NCF, m_FAULT_DemEvent_ModuleMcu_Mapped_Periodcheck_NCF, m_FAULT_DemEvent_ModuleMain_Mapped_Acore_Heart_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Bpu_Heart_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Vdsp_Heart_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Temperature_Sw_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Temperature_Hw_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_Temperature_Sw_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Voltage_Sw_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Voltage_Hw_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_Voltage_Sw_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Wwdt4_Timeout_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Wwdt5_Timeout_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Wwdt6_Timeout_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Wwdt7_Timeout_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Wwdt8_Timeout_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Wwdt9_Timeout_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Wwdt_Vdsp_Timeout_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Wwdtspu_Bpu_Timeout_CF, m_FAULT_DemEvent_ModuleMain_Mapped_Wwdtapm_Bpu_Timeout_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_Pmic_Err0_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_Pmic_Err0_NCF, m_FAULT_DemEvent_ModuleMcu_Mapped_Pmic_Err1_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_Pmic_Err1_NCF, m_FAULT_DemEvent_ModuleMcu_Mapped_Pmic_Err2_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_Pmic_Err2_NCF, m_FAULT_DemEvent_ModuleMcu_Mapped_Pmic_Err3_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_Pmic_Err3_NCF, m_FAULT_DemEvent_ModuleMcu_Mapped_Pmic_Err4_CF, m_FAULT_DemEvent_ModuleMcu_Mapped_Pmic_Err4_NCF, m_FAULT_DemEvent_Startup_CurrentSlotFailedAnotherSuccess, m_FAULT_DemEvent_Startup_EnterRecoverySlot, m_FAULT_DemEvent_ParkCam_NotCalibration, m_FAULT_DemEvent_DriveCam_NotCalibration)

}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::um_cfg::Mcucfg_Runnable_Events, StdLayoutTag>
{
    using ValueType = ::um_cfg::Mcucfg_Runnable_Events;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::um_cfg::Mcucfg_Runnable_Events, StdLayoutTruncTag>
{
    using ValueType = ::um_cfg::Mcucfg_Runnable_Events;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
