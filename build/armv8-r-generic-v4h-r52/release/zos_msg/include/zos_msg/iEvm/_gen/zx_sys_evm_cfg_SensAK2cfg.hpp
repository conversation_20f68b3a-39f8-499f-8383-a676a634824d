#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/container/array.hpp>
#include <basic_types/types.hpp>

#include <zos_msg/common/common.hpp>
#include <zos_msg/iEvm/zx_sys_evm_interfaces.hpp>
namespace um_cfg
{
const zos::uint8_t SensAK2cfg_Runnable_EventSize_cu8 = 81;
using SensAK2_IpcBuffer=zos::Array<zos::uint8_t, SensAK2cfg_Runnable_EventSize_cu8>;
struct SensAK2cfg_Runnable_Events
{
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_POWER_AK2_USSOUT1_OVER_LIMITATION;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_POWER_AK2_USSOUT1_UNDER_LIMITATION;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_POWER_AK2_USSOUT2_OVER_LIMITATION;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_POWER_AK2_USSOUT2_UNDER_LIMITATION;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_POWER_AK2_USSOUT3_OVER_LIMITATION;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_POWER_AK2_USSOUT3_UNDER_LIMITATION;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_USS_Task1msTimeout;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_USS_Task10msTimeout;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_USS_TaskMpTimeout;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142A_SPI_CRCError;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142A_SPI_Timeout;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142A_SPI_DCRTimeout;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142A_SPI_Reserved;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142B_SPI_CRCError;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142B_SPI_Timeout;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142B_SPI_DCRTimeout;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142B_SPI_Reserved;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142C_SPI_CRCError;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142C_SPI_Timeout;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142C_SPI_DCRTimeout;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142C_SPI_Reserved;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142A_OverTemp;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142A_VCCUV_RFCB;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142A_CLKREF_ERROR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142A_CMD_INC;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142A_CMD_CRCError;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142A_UND_CMD;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142A_RFCErr;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142A_Reserved;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142B_OverTemp;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142B_VCCUV_RFCB;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142B_CLKREF_ERROR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142B_CMD_INC;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142B_CMD_CRCError;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142B_UND_CMD;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142B_RFCErr;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142B_Reserved;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142C_OverTemp;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142C_VCCUV_RFCB;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142C_CLKREF_ERROR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142C_CMD_INC;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142C_CMD_CRCError;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142C_UND_CMD;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142C_RFCErr;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142C_Reserved;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142A_DSI1_UV;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142A_DSI1_OV;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142A_DSI1_CMD_OVR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142A_DSI1_PDCMREC;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142A_DSI1_CRMREC;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142A_DSI1_TDMA_SCHEME_DEFINE;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142A_DSI2_UV;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142A_DSI2_OV;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142A_DSI2_CMD_OVR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142A_DSI2_PDCMREC;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142A_DSI2_CRMREC;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142A_DSI2_TDMA_SCHEME_DEFINE;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142B_DSI1_UV;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142B_DSI1_OV;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142B_DSI1_CMD_OVR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142B_DSI1_PDCMREC;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142B_DSI1_CRMREC;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142B_DSI1_TDMA_SCHEME_DEFINE;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142B_DSI2_UV;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142B_DSI2_OV;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142B_DSI2_CMD_OVR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142B_DSI2_PDCMREC;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142B_DSI2_CRMREC;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142B_DSI2_TDMA_SCHEME_DEFINE;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142C_DSI1_UV;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142C_DSI1_OV;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142C_DSI1_CMD_OVR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142C_DSI1_PDCMREC;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142C_DSI1_CRMREC;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142C_DSI1_TDMA_SCHEME_DEFINE;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142C_DSI2_UV;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142C_DSI2_OV;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142C_DSI2_CMD_OVR;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142C_DSI2_PDCMREC;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142C_DSI2_CRMREC;
    zxSysEvM::zxSysEvM_EventState m_FAULT_DemEvent_E52142C_DSI2_TDMA_SCHEME_DEFINE;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(SensAK2cfg_Runnable_Events, m_FAULT_DemEvent_POWER_AK2_USSOUT1_OVER_LIMITATION, m_FAULT_DemEvent_POWER_AK2_USSOUT1_UNDER_LIMITATION, m_FAULT_DemEvent_POWER_AK2_USSOUT2_OVER_LIMITATION, m_FAULT_DemEvent_POWER_AK2_USSOUT2_UNDER_LIMITATION, m_FAULT_DemEvent_POWER_AK2_USSOUT3_OVER_LIMITATION, m_FAULT_DemEvent_POWER_AK2_USSOUT3_UNDER_LIMITATION, m_FAULT_DemEvent_USS_Task1msTimeout, m_FAULT_DemEvent_USS_Task10msTimeout, m_FAULT_DemEvent_USS_TaskMpTimeout, m_FAULT_DemEvent_E52142A_SPI_CRCError, m_FAULT_DemEvent_E52142A_SPI_Timeout, m_FAULT_DemEvent_E52142A_SPI_DCRTimeout, m_FAULT_DemEvent_E52142A_SPI_Reserved, m_FAULT_DemEvent_E52142B_SPI_CRCError, m_FAULT_DemEvent_E52142B_SPI_Timeout, m_FAULT_DemEvent_E52142B_SPI_DCRTimeout, m_FAULT_DemEvent_E52142B_SPI_Reserved, m_FAULT_DemEvent_E52142C_SPI_CRCError, m_FAULT_DemEvent_E52142C_SPI_Timeout, m_FAULT_DemEvent_E52142C_SPI_DCRTimeout, m_FAULT_DemEvent_E52142C_SPI_Reserved, m_FAULT_DemEvent_E52142A_OverTemp, m_FAULT_DemEvent_E52142A_VCCUV_RFCB, m_FAULT_DemEvent_E52142A_CLKREF_ERROR, m_FAULT_DemEvent_E52142A_CMD_INC, m_FAULT_DemEvent_E52142A_CMD_CRCError, m_FAULT_DemEvent_E52142A_UND_CMD, m_FAULT_DemEvent_E52142A_RFCErr, m_FAULT_DemEvent_E52142A_Reserved, m_FAULT_DemEvent_E52142B_OverTemp, m_FAULT_DemEvent_E52142B_VCCUV_RFCB, m_FAULT_DemEvent_E52142B_CLKREF_ERROR, m_FAULT_DemEvent_E52142B_CMD_INC, m_FAULT_DemEvent_E52142B_CMD_CRCError, m_FAULT_DemEvent_E52142B_UND_CMD, m_FAULT_DemEvent_E52142B_RFCErr, m_FAULT_DemEvent_E52142B_Reserved, m_FAULT_DemEvent_E52142C_OverTemp, m_FAULT_DemEvent_E52142C_VCCUV_RFCB, m_FAULT_DemEvent_E52142C_CLKREF_ERROR, m_FAULT_DemEvent_E52142C_CMD_INC, m_FAULT_DemEvent_E52142C_CMD_CRCError, m_FAULT_DemEvent_E52142C_UND_CMD, m_FAULT_DemEvent_E52142C_RFCErr, m_FAULT_DemEvent_E52142C_Reserved, m_FAULT_DemEvent_E52142A_DSI1_UV, m_FAULT_DemEvent_E52142A_DSI1_OV, m_FAULT_DemEvent_E52142A_DSI1_CMD_OVR, m_FAULT_DemEvent_E52142A_DSI1_PDCMREC, m_FAULT_DemEvent_E52142A_DSI1_CRMREC, m_FAULT_DemEvent_E52142A_DSI1_TDMA_SCHEME_DEFINE, m_FAULT_DemEvent_E52142A_DSI2_UV, m_FAULT_DemEvent_E52142A_DSI2_OV, m_FAULT_DemEvent_E52142A_DSI2_CMD_OVR, m_FAULT_DemEvent_E52142A_DSI2_PDCMREC, m_FAULT_DemEvent_E52142A_DSI2_CRMREC, m_FAULT_DemEvent_E52142A_DSI2_TDMA_SCHEME_DEFINE, m_FAULT_DemEvent_E52142B_DSI1_UV, m_FAULT_DemEvent_E52142B_DSI1_OV, m_FAULT_DemEvent_E52142B_DSI1_CMD_OVR, m_FAULT_DemEvent_E52142B_DSI1_PDCMREC, m_FAULT_DemEvent_E52142B_DSI1_CRMREC, m_FAULT_DemEvent_E52142B_DSI1_TDMA_SCHEME_DEFINE, m_FAULT_DemEvent_E52142B_DSI2_UV, m_FAULT_DemEvent_E52142B_DSI2_OV, m_FAULT_DemEvent_E52142B_DSI2_CMD_OVR, m_FAULT_DemEvent_E52142B_DSI2_PDCMREC, m_FAULT_DemEvent_E52142B_DSI2_CRMREC, m_FAULT_DemEvent_E52142B_DSI2_TDMA_SCHEME_DEFINE, m_FAULT_DemEvent_E52142C_DSI1_UV, m_FAULT_DemEvent_E52142C_DSI1_OV, m_FAULT_DemEvent_E52142C_DSI1_CMD_OVR, m_FAULT_DemEvent_E52142C_DSI1_PDCMREC, m_FAULT_DemEvent_E52142C_DSI1_CRMREC, m_FAULT_DemEvent_E52142C_DSI1_TDMA_SCHEME_DEFINE, m_FAULT_DemEvent_E52142C_DSI2_UV, m_FAULT_DemEvent_E52142C_DSI2_OV, m_FAULT_DemEvent_E52142C_DSI2_CMD_OVR, m_FAULT_DemEvent_E52142C_DSI2_PDCMREC, m_FAULT_DemEvent_E52142C_DSI2_CRMREC, m_FAULT_DemEvent_E52142C_DSI2_TDMA_SCHEME_DEFINE)

}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::um_cfg::SensAK2cfg_Runnable_Events, StdLayoutTag>
{
    using ValueType = ::um_cfg::SensAK2cfg_Runnable_Events;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::um_cfg::SensAK2cfg_Runnable_Events, StdLayoutTruncTag>
{
    using ValueType = ::um_cfg::SensAK2cfg_Runnable_Events;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
