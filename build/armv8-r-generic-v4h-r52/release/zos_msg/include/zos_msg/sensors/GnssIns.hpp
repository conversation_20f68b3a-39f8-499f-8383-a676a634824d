#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>

#include <zos_msg/common/common.hpp>
#include <zos_msg/common/timestamp.hpp>
#include <zos_msg/geometry/point.hpp>
#include <zos_msg/geometry/pose.hpp>
namespace zos
{
namespace sensors
{
struct AccuryInfo
{
    zos::float32_t m_hdop;
    zos::float32_t m_vdop;
    zos::float32_t m_pdop;
    zos::float32_t m_gdop;
    zos::float32_t m_tdop;
    zos::float32_t m_longitude_error;
    zos::float32_t m_latitude_error;
    zos::float32_t m_height_error;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(AccuryInfo, m_hdop, m_vdop, m_pdop, m_gdop, m_tdop, m_longitude_error, m_latitude_error, m_height_error)

enum class GnssStatus
{
    INITIALIZED = 0,
    SINGALPOINT = 1,
    CODEDIFF = 2,
    RESERVED = 3,
    FIXEDSOLVE = 4,
    FLOATSOLVE = 5,
};
struct GnssPvt
{
    zos::common::Timestamp m_gnss_timestamp;
    zos::common::Coordinate m_gnss_coordinate;
    GnssStatus m_gnss_status;
    zos::geometry::Point3d m_gnss_position;
    zos::geometry::Point3f m_gnss_position_std;
    zos::geometry::Point3d m_gnss_velocity;
    zos::geometry::Point3f m_gnss_velocity_std;
    zos::uint16_t m_gnss_satelliteNum;
    AccuryInfo m_gnss_accuryinfo;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(GnssPvt, m_gnss_timestamp, m_gnss_coordinate, m_gnss_status, m_gnss_position, m_gnss_position_std, m_gnss_velocity, m_gnss_velocity_std, m_gnss_satelliteNum, m_gnss_accuryinfo)

enum class InsStatus
{
    INS_INACTIVE = 0,
    INS_ALIGNING = 1,
    INS_HIGH_VARIANCE = 2,
    INS_SOLUTION_GOOD = 3,
    RESERVED = 4,
    RESERVED1 = 5,
    INS_SOLUTION_FREE = 6,
    INS_ALIGNMENT_COMPLETE = 7,
    DETERMINING_ORIENTATION = 8,
    WAITING_INITIALPOS = 9,
    WAITING_AZIMUTH = 10,
    INITIALIZING_BIASES = 11,
    MOTION_DETECT = 12,
};
enum class PoseType
{
    NONE = 0,
    FIXEDPOS = 1,
    FIXEDHEIGHT = 2,
    Reserved_3 = 3,
    FLOATCONV = 4,
    WIDELANE = 5,
    NARROWLANE = 6,
    Reserved_7 = 7,
    DOPPLER_VELOCITY = 8,
    Reserved_9 = 9,
    SINGLE = 16,
    PSRDIFF = 17,
    WAAS = 18,
    PROPAGATED = 19,
    Reserved_20 = 20,
    L1_FLOAT = 32,
    IONOFREE_FLOAT = 33,
    NARROW_FLOAT = 34,
    Reserved_35 = 35,
    L1_INT = 48,
    WIDE_INT = 49,
    NARROW_INT = 50,
    RTK_DIRECT_INS = 51,
    INS_SBAS = 52,
    INS_PSRSP = 53,
    INS_PSRDIFF = 54,
    INS_RTKFLOAT = 55,
    INS_RTKFIXED = 56,
    PPP_CONVERGING = 68,
    PPP = 69,
    OPERATIONAL = 70,
    WARNING = 71,
    OUT_OF_BOUNDS = 72,
    INS_PPP_Converging = 73,
    INS_PPP = 74,
    PPP_BASIC_CONVERGING = 77,
    PPP_BASIC = 78,
    INS_PPPP_BASIC_Converging = 79,
    INS_PPPP_BASIC = 80,
};
struct InsInfo
{
    zos::common::Timestamp m_ins_timestamp;
    zos::common::Coordinate m_ins_coordinate;
    InsStatus m_ins_status;
    zos::geometry::Pose3d m_ins_pose;
    PoseType m_ins_pose_type;
    zos::geometry::Point3f m_ins_rot_std;
    zos::geometry::Point3f m_ins_position_std;
    zos::geometry::Point3d m_ins_velocity;
    zos::geometry::Point3f m_ins_velocity_std;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(InsInfo, m_ins_timestamp, m_ins_coordinate, m_ins_status, m_ins_pose, m_ins_pose_type, m_ins_rot_std, m_ins_position_std, m_ins_velocity, m_ins_velocity_std)

}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::sensors::AccuryInfo, StdLayoutTag>
{
    using ValueType = ::zos::sensors::AccuryInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::sensors::AccuryInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::sensors::AccuryInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::sensors::GnssPvt, StdLayoutTag>
{
    using ValueType = ::zos::sensors::GnssPvt;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::sensors::GnssPvt, StdLayoutTruncTag>
{
    using ValueType = ::zos::sensors::GnssPvt;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::sensors::InsInfo, StdLayoutTag>
{
    using ValueType = ::zos::sensors::InsInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::sensors::InsInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::sensors::InsInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
