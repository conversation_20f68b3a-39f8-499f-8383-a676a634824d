#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>
#include <basic_types/container/vector.hpp>

#include <zos_msg/common/timestamp.hpp>
#include <zos_msg/map/local_map_graph_common.hpp>
#include <zos_msg/map/local_map_graph_link.hpp>
namespace zos
{
namespace navigation
{
using FeatureIdType=zos::map::FeatureIdType;
using ScalarType=zos::map::ScalarType;
using CentermeterType=zos::map::CentermeterType;
enum class NavigationStatusType
{
    INACTIVE = 0,
    CRUISING = 1,
    NAVIGATING = 2,
    YAWING = 3,
};
struct PositionInLink
{
    FeatureIdType link_id;
    CentermeterType distance_from_link_start;
    zos::bool_t is_valid;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(PositionInLink, link_id, distance_from_link_start, is_valid)

struct NavigationLink
{
    FeatureIdType link_id;
    CentermeterType link_length;
    zos::map::link::LaneIdSequence lane_ids;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(NavigationLink, link_id, link_length, lane_ids)

const zos::uint8_t MAX_SPEED_LIMIT_CONNECTED_IDS_SIZE = 20;
using SpeedLimitConnectedIdSequence=zos::FixedVector<FeatureIdType, MAX_SPEED_LIMIT_CONNECTED_IDS_SIZE>;
struct SpeedLimitInfo
{
    ScalarType start_distance;
    ScalarType end_distance;
    FeatureIdType start_id;
    FeatureIdType end_id;
    ScalarType max_speed;
    ScalarType min_speed;
    SpeedLimitConnectedIdSequence connected_ids;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(SpeedLimitInfo, start_distance, end_distance, start_id, end_id, max_speed, min_speed, connected_ids)

enum class NavigationTurnType
{
    UNKNOWN = 0,
    LEFT_TURN = 1,
    RIGHT_TURN = 2,
    STRAIGHT_AHEAD = 3,
    LEFT_U_TURN = 4,
    RIGHT_U_TURN = 5,
    LEFT_SPLIT = 6,
    RIGHT_SPLIT = 7,
    LEFT_MERGE = 8,
    RIGHT_MERGE = 9,
};
struct TurnInforamtion
{
    FeatureIdType id;
    NavigationTurnType turn_type;
    CentermeterType distance_to_turn_action_occuring;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(TurnInforamtion, id, turn_type, distance_to_turn_action_occuring)

const zos::uint8_t MAX_LINK_SIZE_IN_NAVIGATION = 16;
const zos::uint8_t MAX_TURN_INFORMATION_SIZE_IN_NAVIGATION = MAX_LINK_SIZE_IN_NAVIGATION;
const zos::uint8_t MAX_LINK_LEVEL_SPEED_LIMIT_INFO_SIZE = MAX_LINK_SIZE_IN_NAVIGATION;
const zos::uint8_t MAX_LANE_LEVEL_SPEED_LIMIT_INFO_SIZE = (MAX_LINK_LEVEL_SPEED_LIMIT_INFO_SIZE * 2);
using NavigationLinksPath=zos::FixedVector<NavigationLink, MAX_LINK_SIZE_IN_NAVIGATION>;
using TurnInformationSequence=zos::FixedVector<TurnInforamtion, MAX_TURN_INFORMATION_SIZE_IN_NAVIGATION>;
using LinkSpeedLimitInfoSequence=zos::FixedVector<SpeedLimitInfo, MAX_LINK_LEVEL_SPEED_LIMIT_INFO_SIZE>;
using LaneSpeedLimitInfoSequence=zos::FixedVector<SpeedLimitInfo, MAX_LANE_LEVEL_SPEED_LIMIT_INFO_SIZE>;
struct Navigation
{
    zos::common::Timestamp timestamp;
    NavigationStatusType status;
    PositionInLink ego_position;
    PositionInLink destination_position;
    NavigationLinksPath links_path;
    TurnInformationSequence turn_informations;
    LaneSpeedLimitInfoSequence lane_speed_limit_infos;
    LinkSpeedLimitInfoSequence link_speed_limit_infos;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(Navigation, timestamp, status, ego_position, destination_position, links_path, turn_informations, lane_speed_limit_infos, link_speed_limit_infos)

}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::navigation::PositionInLink, StdLayoutTag>
{
    using ValueType = ::zos::navigation::PositionInLink;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::navigation::PositionInLink, StdLayoutTruncTag>
{
    using ValueType = ::zos::navigation::PositionInLink;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::navigation::NavigationLink, StdLayoutTag>
{
    using ValueType = ::zos::navigation::NavigationLink;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::navigation::NavigationLink, StdLayoutTruncTag>
{
    using ValueType = ::zos::navigation::NavigationLink;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, lane_ids) + sizeof(uint32_t);
        size += sample.lane_ids.size() * sizeof(decltype(sample.lane_ids)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,lane_ids));
        uint32_t des_size = offsetof(ValueType,lane_ids) + element_size * sizeof(decltype(sample.lane_ids)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::navigation::SpeedLimitInfo, StdLayoutTag>
{
    using ValueType = ::zos::navigation::SpeedLimitInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::navigation::SpeedLimitInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::navigation::SpeedLimitInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, connected_ids) + sizeof(uint32_t);
        size += sample.connected_ids.size() * sizeof(decltype(sample.connected_ids)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,connected_ids));
        uint32_t des_size = offsetof(ValueType,connected_ids) + element_size * sizeof(decltype(sample.connected_ids)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::navigation::TurnInforamtion, StdLayoutTag>
{
    using ValueType = ::zos::navigation::TurnInforamtion;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::navigation::TurnInforamtion, StdLayoutTruncTag>
{
    using ValueType = ::zos::navigation::TurnInforamtion;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::navigation::Navigation, StdLayoutTag>
{
    using ValueType = ::zos::navigation::Navigation;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::navigation::Navigation, StdLayoutTruncTag>
{
    using ValueType = ::zos::navigation::Navigation;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, link_speed_limit_infos) + sizeof(uint32_t);
        size += sample.link_speed_limit_infos.size() * sizeof(decltype(sample.link_speed_limit_infos)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,link_speed_limit_infos));
        uint32_t des_size = offsetof(ValueType,link_speed_limit_infos) + element_size * sizeof(decltype(sample.link_speed_limit_infos)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
