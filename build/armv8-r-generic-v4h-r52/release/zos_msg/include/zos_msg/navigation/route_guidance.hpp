#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>
#include <basic_types/container/vector.hpp>
#include <basic_types/container/string.hpp>

#include <zos_msg/common/timestamp.hpp>
#include <zos_msg/common/common.hpp>
#include <zos_msg/geometry/point.hpp>
#include <zos_msg/map/local_map_graph_objects.hpp>
#include <zos_msg/map/local_map_graph_link.hpp>
#include <zos_msg/map/local_map_graph_lane.hpp>
#include <zos_msg/navigation/navigation.hpp>
namespace zos
{
namespace navigation
{
namespace guidance
{
using FeatureIdType=zos::map::FeatureIdType;
using ScalarType=zos::map::ScalarType;
enum class Category
{
    UNKNOWN = 0,
    FULL_PATH = 1,
    DYNAMIC_GUIDANCE = 2,
};
enum class Status
{
    UNKNOWN = 0,
    CRUISING = 1,
    START_ROUTE_GUIDANCE = 2,
    EXIT_ROUTE_GUIDANCE = 3,
    ON_ROUTE_PATH = 4,
    OUT_OF_ROUTE = 5,
    RESET_ROUTE_GUIDANCE = 6,
};
enum class LinkDirection
{
    UNDEFINED = 0,
    POSITIVE_DIRECTION = 1,
    NEGATIVE_DIRECTION = 2,
    BI_DIRECTION = 3,
};
using LaneTurnType=zos::map::lane::LaneTurnType;
using LaneAttribute=zos::map::lane::LaneTransitionType;
struct LaneInfo
{
    FeatureIdType lane_id;
    LaneTurnType turn_type;
    zos::uint8_t sequences;
    LaneAttribute lane_attribute;
    LaneTurnType forbidden_turn_type;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(LaneInfo, lane_id, turn_type, sequences, lane_attribute, forbidden_turn_type)

struct SpeedLimit
{
    ScalarType start_position;
    ScalarType end_position;
    ScalarType max_speed;
    ScalarType min_speed;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(SpeedLimit, start_position, end_position, max_speed, min_speed)

struct SlopeInfo
{
    zos::geometry::Point2f point;
    ScalarType slope_percent;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(SlopeInfo, point, slope_percent)

const zos::uint8_t MAX_PRECEDING_OR_SUCCEEDING_LINK_SIZE = 8;
const zos::uint8_t MAX_SPEED_LIMIT_SIZE_IN_LINK = 4;
const zos::uint8_t MAX_SLOPE_INFO_SIZE_IN_LINK = 2;
const zos::uint8_t MAX_LANE_INFO_SIZE_IN_LINK = 10;
const zos::uint8_t MAX_POINT_SIZE_IN_LINK = 100;
using PrecedingSucceedingLinksIdSequence=zos::FixedVector<FeatureIdType, MAX_PRECEDING_OR_SUCCEEDING_LINK_SIZE>;
using SpeedLimitSequence=zos::FixedVector<SpeedLimit, MAX_SPEED_LIMIT_SIZE_IN_LINK>;
using SlopeInfoSequence=zos::FixedVector<SlopeInfo, MAX_SLOPE_INFO_SIZE_IN_LINK>;
using LaneInfoSequence=zos::FixedVector<LaneInfo, MAX_LANE_INFO_SIZE_IN_LINK>;
using PointInLinkSequence=zos::FixedVector<zos::geometry::Point2f, MAX_POINT_SIZE_IN_LINK>;
using LinkType=zos::map::link::LinkType;
using LinkClassType=zos::map::link::LinkClassType;
struct Link
{
    FeatureIdType id;
    LinkType link_type;
    LinkClassType link_class;
    zos::bool_t is_on_route;
    ScalarType length;
    LinkDirection direction;
    zos::uint16_t lane_count;
    LaneInfoSequence lane_infos;
    SpeedLimitSequence speed_limits;
    PrecedingSucceedingLinksIdSequence preceding_link_ids;
    PrecedingSucceedingLinksIdSequence succeeding_link_ids;
    PointInLinkSequence geometry_points;
    SlopeInfoSequence slope_infos;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(Link, id, link_type, link_class, is_on_route, length, direction, lane_count, lane_infos, speed_limits, preceding_link_ids, succeeding_link_ids, geometry_points, slope_infos)

struct Connection
{
    FeatureIdType incoming_id;
    FeatureIdType outcoming_id;
    FeatureIdType connected_ids;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(Connection, incoming_id, outcoming_id, connected_ids)

const zos::uint8_t MAX_CONNECTION_SIZE_IN_INTERSECTION = 8;
using ConnectionSequence=zos::FixedVector<Connection, MAX_CONNECTION_SIZE_IN_INTERSECTION>;
struct Intersection
{
    FeatureIdType id;
    ConnectionSequence connections;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(Intersection, id, connections)

enum class MainAction
{
    NONE = 0,
    TURN_LEFT = 1,
    TURN_RIGHT = 2,
    SLIGHT_LEFT = 3,
    SLIGHT_RIGHT = 4,
    TURN_HARD_LEFT = 5,
    TURN_HARD_RIGHT = 6,
    U_TURN = 7,
    GO_STRAIGHT = 8,
    MERGE_LEFT = 9,
    MERGE_RIGHT = 10,
    SPLIT_LEFT = 11,
    SPLIT_RIGHT = 12,
    ENTRY_RING = 13,
    LEAVE_RING = 14,
    DRIVE_SLOW = 15,
};
enum class AssistAction
{
    NONE = 0,
    ENTRY_MAIN = 1,
    ENTRY_SIDE_ROAD = 2,
    ENTRY_FREEWAY = 3,
    ENTRY_SLIP = 4,
    ENTRY_TUNNEL = 5,
    CENTER_BRANCH = 6,
    ENTRY_RIGHT_BRANCH = 7,
    ENTRY_LEFT_BRANCH = 8,
    ENTRY_RIGHT_ROAD = 9,
    ENTRY_LEFT_ROAD = 10,
    ENTRY_MERGE_CENTER = 11,
    ENTRY_MERGE_RIGHT = 12,
    ENTRY_MERGE_LEFT = 13,
    ENTRY_MERGE_RIGHT_SILD = 14,
    ENTRY_MERGE_LEFT_SILD = 15,
    ENTRY_MERGE_RIGHT_MAIN = 16,
    ENTRY_MERGE_LEFT_MAIN = 17,
    ENTRY_MERGE_RIGHT_RIGHT = 18,
    ALONG_ROAD = 19,
    ALONG_SILD = 20,
    ALONG_MAIN = 21,
    ARRIVE_EXIT = 22,
    ARRIVE_SERVICE_AREA = 23,
    ARRIVE_TOLLGATE = 24,
    ARRIVE_WAY = 25,
    ARRIVE_DESTINATION = 26,
    ENTRY_RING_LEFT = 27,
    ENTRY_RING_RIGHT = 28,
    ENTRY_RING_CONTINUE = 29,
    ENTRY_RING_UTURN = 30,
    SMALL_RING_NOT_COUNT = 31,
    RIGHT_BRANCH_1 = 32,
    RIGHT_BRANCH_2 = 33,
    RIGHT_BRANCH_3 = 34,
    RIGHT_BRANCH_4 = 35,
    RIGHT_BRANCH_5 = 36,
    LEFT_BRANCH_1 = 37,
    LEFT_BRANCH_2 = 38,
    LEFT_BRANCH_3 = 39,
    LEFT_BRANCH_4 = 40,
    LEFT_BRANCH_5 = 41,
};
const zos::uint8_t MAX_LINK_SIZE_IN_GUIDANCE_SEGMENT = 20;
const zos::uint8_t MAX_RECOMMEND_LANE_SIZE_IN_GUIDANCE_SEGMENT = 10;
const zos::uint8_t MAX_POINT_SIZE_GUIDANCE_SEGMENT = 50;
using LinkIdSequence=zos::FixedVector<FeatureIdType, MAX_LINK_SIZE_IN_GUIDANCE_SEGMENT>;
using RecommendedLaneIdsSequence=zos::FixedVector<zos::uint8_t, MAX_RECOMMEND_LANE_SIZE_IN_GUIDANCE_SEGMENT>;
using PointInGuidanceSegmentSequence=zos::FixedVector<zos::geometry::Point2f, MAX_POINT_SIZE_GUIDANCE_SEGMENT>;
struct GuidanceSegment
{
    FeatureIdType id;
    LinkIdSequence link_ids;
    ScalarType length;
    MainAction main_action;
    AssistAction assist_action;
    ScalarType remained_distance;
    ScalarType remained_time;
    PointInGuidanceSegmentSequence geometry_points;
    SpeedLimitSequence speed_limits;
    RecommendedLaneIdsSequence recommended_lanes;
    FeatureIdType connected_intersection_id;
    ScalarType distance_to_intersection;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(GuidanceSegment, id, link_ids, length, main_action, assist_action, remained_distance, remained_time, geometry_points, speed_limits, recommended_lanes, connected_intersection_id, distance_to_intersection)

const zos::uint8_t MAX_STRING_SIZE = 32;
using String=zos::FixedString<MAX_STRING_SIZE>;
struct PoiInfo
{
    zos::geometry::Point3d point;
    FeatureIdType id;
    String name;
    FeatureIdType link_id;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(PoiInfo, point, id, name, link_id)

const zos::uint8_t MAX_LINK_SIZE_IN_ROUTE = 60;
const zos::uint8_t MAX_INTERSECTION_SIZE = 8;
const zos::uint8_t MAX_GUIDANCE_SEGMENT_SIZE = 32;
using LinkSequence=zos::FixedVector<Link, MAX_LINK_SIZE_IN_ROUTE>;
using IntersectionSequence=zos::FixedVector<Intersection, MAX_INTERSECTION_SIZE>;
using GuidanceSegmentSequence=zos::FixedVector<GuidanceSegment, MAX_GUIDANCE_SEGMENT_SIZE>;
struct Route
{
    FeatureIdType route_id;
    zos::bool_t is_main_route;
    ScalarType total_distance;
    ScalarType total_time;
    ScalarType estimated_time_of_arrival;
    PoiInfo start_poi;
    PoiInfo end_poi;
    LinkSequence links;
    IntersectionSequence intersections;
    GuidanceSegmentSequence guidance_segments;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(Route, route_id, is_main_route, total_distance, total_time, estimated_time_of_arrival, start_poi, end_poi, links, intersections, guidance_segments)

using SignalDirection=zos::map::object::LightSignalStatusType;
using LightSignalColorType=zos::map::object::LightSignalColorType;
struct TrafficLightStatus
{
    FeatureIdType id;
    LightSignalColorType signal_color_type;
    zos::uint16_t count_down;
    SignalDirection signal_direction;
    FeatureIdType associated_guidance_segment_id;
    FeatureIdType associated_link_id;
    ScalarType remained_distance;
    zos::geometry::Point3f point;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(TrafficLightStatus, id, signal_color_type, count_down, signal_direction, associated_guidance_segment_id, associated_link_id, remained_distance, point)

enum class EventType
{
    UNKNOWN = 0,
    ACCIDENT = 1,
    CONGESTION = 2,
    LONG_SOLID_LINE = 3,
    ROADWORK = 4,
    FLOOD = 5,
    SNOW = 6,
    FOG = 7,
    TRAFFIC_LIGHT_FAILURE = 8,
    ROAD_CLOSED = 9,
    VEHICLE_BREAKDOWN = 10,
    EMERGENCY_VEHICLE_PASSAGE = 11,
};
const zos::uint8_t MAX_ASSOCIATED_LINK_ID_SIZE_IN_EVENT = 5;
using AssociatedIdSequence=zos::FixedVector<FeatureIdType, MAX_ASSOCIATED_LINK_ID_SIZE_IN_EVENT>;
struct TrafficEventInfo
{
    EventType event_type;
    ScalarType distance_to_start;
    ScalarType distance_to_end;
    ScalarType total_length;
    zos::geometry::Point2f start_position;
    zos::geometry::Point2f end_position;
    AssociatedIdSequence associated_guidance_segment_ids;
    AssociatedIdSequence associated_link_ids;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(TrafficEventInfo, event_type, distance_to_start, distance_to_end, total_length, start_position, end_position, associated_guidance_segment_ids, associated_link_ids)

struct EgoPositionInfo
{
    zos::geometry::Point3d ego_position;
    FeatureIdType current_route_id;
    FeatureIdType current_link_id;
    zos::uint32_t current_link_geometry_index;
    FeatureIdType current_guidance_segment_id;
    zos::uint32_t current_guidance_segment_points_id;
    zos::float64_t remained_distance_to_end;
    zos::float64_t remained_time_to_end;
    FeatureIdType id;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(EgoPositionInfo, ego_position, current_route_id, current_link_id, current_link_geometry_index, current_guidance_segment_id, current_guidance_segment_points_id, remained_distance_to_end, remained_time_to_end, id)

const zos::uint8_t MAX_TRAFFIC_LIGHT_STATUS_SIZE = 30;
const zos::uint8_t MAX_TRAFFIC_EVENT_INFO_SIZE = 4;
const zos::uint8_t MAX_ROUTE_SIZE = 4;
using TrafficLightStatusSequence=zos::FixedVector<TrafficLightStatus, MAX_TRAFFIC_LIGHT_STATUS_SIZE>;
using TrafficEventInfoSequence=zos::FixedVector<TrafficEventInfo, MAX_TRAFFIC_EVENT_INFO_SIZE>;
using RouteSequence=zos::FixedVector<Route, MAX_ROUTE_SIZE>;
struct RouteGuidance
{
    zos::common::Timestamp timestamp;
    zos::common::Coordinate coordinate;
    Category category;
    Status status;
    RouteSequence routes;
    EgoPositionInfo ego_position_info;
    TrafficLightStatusSequence traffic_light_status;
    TrafficEventInfoSequence traffic_event_infos;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(RouteGuidance, timestamp, coordinate, category, status, routes, ego_position_info, traffic_light_status, traffic_event_infos)

}
}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::navigation::guidance::LaneInfo, StdLayoutTag>
{
    using ValueType = ::zos::navigation::guidance::LaneInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::navigation::guidance::LaneInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::navigation::guidance::LaneInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::navigation::guidance::SpeedLimit, StdLayoutTag>
{
    using ValueType = ::zos::navigation::guidance::SpeedLimit;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::navigation::guidance::SpeedLimit, StdLayoutTruncTag>
{
    using ValueType = ::zos::navigation::guidance::SpeedLimit;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::navigation::guidance::SlopeInfo, StdLayoutTag>
{
    using ValueType = ::zos::navigation::guidance::SlopeInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::navigation::guidance::SlopeInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::navigation::guidance::SlopeInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::navigation::guidance::Link, StdLayoutTag>
{
    using ValueType = ::zos::navigation::guidance::Link;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::navigation::guidance::Link, StdLayoutTruncTag>
{
    using ValueType = ::zos::navigation::guidance::Link;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, slope_infos) + sizeof(uint32_t);
        size += sample.slope_infos.size() * sizeof(decltype(sample.slope_infos)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,slope_infos));
        uint32_t des_size = offsetof(ValueType,slope_infos) + element_size * sizeof(decltype(sample.slope_infos)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::navigation::guidance::Connection, StdLayoutTag>
{
    using ValueType = ::zos::navigation::guidance::Connection;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::navigation::guidance::Connection, StdLayoutTruncTag>
{
    using ValueType = ::zos::navigation::guidance::Connection;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::navigation::guidance::Intersection, StdLayoutTag>
{
    using ValueType = ::zos::navigation::guidance::Intersection;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::navigation::guidance::Intersection, StdLayoutTruncTag>
{
    using ValueType = ::zos::navigation::guidance::Intersection;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, connections) + sizeof(uint32_t);
        size += sample.connections.size() * sizeof(decltype(sample.connections)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,connections));
        uint32_t des_size = offsetof(ValueType,connections) + element_size * sizeof(decltype(sample.connections)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::navigation::guidance::GuidanceSegment, StdLayoutTag>
{
    using ValueType = ::zos::navigation::guidance::GuidanceSegment;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::navigation::guidance::GuidanceSegment, StdLayoutTruncTag>
{
    using ValueType = ::zos::navigation::guidance::GuidanceSegment;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::navigation::guidance::PoiInfo, StdLayoutTag>
{
    using ValueType = ::zos::navigation::guidance::PoiInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::navigation::guidance::PoiInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::navigation::guidance::PoiInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::navigation::guidance::Route, StdLayoutTag>
{
    using ValueType = ::zos::navigation::guidance::Route;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::navigation::guidance::Route, StdLayoutTruncTag>
{
    using ValueType = ::zos::navigation::guidance::Route;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, guidance_segments) + sizeof(uint32_t);
        size += sample.guidance_segments.size() * sizeof(decltype(sample.guidance_segments)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,guidance_segments));
        uint32_t des_size = offsetof(ValueType,guidance_segments) + element_size * sizeof(decltype(sample.guidance_segments)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::navigation::guidance::TrafficLightStatus, StdLayoutTag>
{
    using ValueType = ::zos::navigation::guidance::TrafficLightStatus;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::navigation::guidance::TrafficLightStatus, StdLayoutTruncTag>
{
    using ValueType = ::zos::navigation::guidance::TrafficLightStatus;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::navigation::guidance::TrafficEventInfo, StdLayoutTag>
{
    using ValueType = ::zos::navigation::guidance::TrafficEventInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::navigation::guidance::TrafficEventInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::navigation::guidance::TrafficEventInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, associated_link_ids) + sizeof(uint32_t);
        size += sample.associated_link_ids.size() * sizeof(decltype(sample.associated_link_ids)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,associated_link_ids));
        uint32_t des_size = offsetof(ValueType,associated_link_ids) + element_size * sizeof(decltype(sample.associated_link_ids)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::navigation::guidance::EgoPositionInfo, StdLayoutTag>
{
    using ValueType = ::zos::navigation::guidance::EgoPositionInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::navigation::guidance::EgoPositionInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::navigation::guidance::EgoPositionInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::navigation::guidance::RouteGuidance, StdLayoutTag>
{
    using ValueType = ::zos::navigation::guidance::RouteGuidance;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::navigation::guidance::RouteGuidance, StdLayoutTruncTag>
{
    using ValueType = ::zos::navigation::guidance::RouteGuidance;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, traffic_event_infos) + sizeof(uint32_t);
        size += sample.traffic_event_infos.size() * sizeof(decltype(sample.traffic_event_infos)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,traffic_event_infos));
        uint32_t des_size = offsetof(ValueType,traffic_event_infos) + element_size * sizeof(decltype(sample.traffic_event_infos)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
