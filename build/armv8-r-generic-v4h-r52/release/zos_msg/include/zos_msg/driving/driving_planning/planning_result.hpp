#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>
#include <basic_types/container/vector.hpp>

#include <zos_msg/common/common.hpp>
#include <zos_msg/common/timestamp.hpp>
#include <zos_msg/geometry/point.hpp>
namespace zos
{
namespace planning
{
struct PathPoint
{
    zos::float32_t x;
    zos::float32_t y;
    zos::float32_t theta;
    zos::float32_t kappa;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(PathPoint, x, y, theta, kappa)

using PathPoints=zos::FixedVector<PathPoint, 128>;
struct TrajectoryPoint
{
    zos::float32_t t;
    zos::float64_t x;
    zos::float64_t y;
    zos::float32_t theta;
    zos::float32_t kappa;
    zos::float32_t v;
    zos::float32_t a;
    zos::float32_t s;
    zos::float32_t l;
    zos::float32_t yaw_rate;
    zos::float32_t steering_angle;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(TrajectoryPoint, t, x, y, theta, kappa, v, a, s, l, yaw_rate, steering_angle)

using TrajectoryPoints=zos::FixedVector<TrajectoryPoint, 128>;
struct Trajectory
{
    zos::float64_t timestamp;
    zos::uint8_t coordinate_type;
    zos::bool_t is_lon_available;
    zos::bool_t is_lat_available;
    TrajectoryPoints points;
    zos::uint8_t action;
    zos::uint8_t gear;
    zos::float32_t length;
    zos::uint8_t trajectory_type;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(Trajectory, timestamp, coordinate_type, is_lon_available, is_lat_available, points, action, gear, length, trajectory_type)

enum class Activatestate
{
    INACTIVATE,
    ACC,
    ICA,
    NDA,
};
enum class Result
{
    RESULT_INACTIVE,
    RESULT_OK,
    RESULT_FAIL,
    RESULT_FAIL_GET_MAP,
    RESULT_FAIL_GET_TRAFFIC,
    RESULT_FAIL_GET_VEHICLE,
    RESULT_FAIL_GET_LOCALIZATION,
    RESULT_FAIL_UPDATE_MANAGER,
    RESULT_FAIL_MANAGER_STAMP,
    RESULT_FAIL_MANAGER_NO_MAP,
    RESULT_FAIL_MANAGER_TARGET_LANE,
    RESULT_FAIL_MANAGER_UPDATE_REFLINE,
    RESULT_FAIL_MANAGER_CURRENT_LANE,
    RESULT_FAIL_MANAGER_PLANNER,
};
enum class NudgeState
{
    NUDGE_NONE,
    NUDGE_LEFT,
    NUDGE_RIGHT,
};
enum class LaneBorrowReason
{
    LC_BORROW_REASON_NONE,
    LC_BORROW_REASON_FOR_VEHICLE,
    LC_REASON_FOR_STATIC_OBJECT,
    LC_REASON_FOR_ROADWORK,
    LC_REASON_FOR_GENERAL,
};
enum class LaneBorrowState
{
    BORROW_NONE,
    BORROW_PREPARE_LEFT,
    BORROW_PREPARE_RIGHT,
    BORROW_WAIT_LEFT,
    BORROW_WAIT_RIGHT,
    BORROW_LEFT,
    BORROW_RIGHT,
    BORROW_CANCEL,
};
enum class TurnLight
{
    TURN_LIGHT_NONE,
    TURN_LIGHT_LEFT,
    TURN_LIGHT_RIGHT,
};
enum class TurnLightReason
{
    TURN_LIGHT_REASON_NONE,
    TURN_LIGHT_REASON_TURN,
    TURN_LIGHT_REASON_UTURN,
    TURN_LIGHT_REASON_MERGE,
    TURN_LIGHT_REASON_SPLIT,
    TURN_LIGHT_REASON_ROUNDABOUT,
    TURN_LIGHT_REASON_BYPASS,
    TURN_LIGHT_REASON_PUSH,
};
struct LcGapObstacles
{
    zos::uint32_t gap_front;
    zos::uint32_t gap_back;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(LcGapObstacles, gap_front, gap_back)

enum class LaneChangeState
{
    LANE_KEEPING,
    LANE_PREPARE_LEFT,
    LANE_PREPARE_RIGHT,
    LANE_CHANGE_LEFT,
    LANE_CHANGE_RIGHT,
    LANE_CHANGE_FINISH_LEFT,
    LANE_CHANGE_FINISH_RIGHT,
    LANE_CHANGE_CANCEL,
    LANE_KEEPING_INIT,
    LANE_CHANGE_INVALID,
    LANE_CHANGE_CANCEL_LEFT,
    LANE_CHANGE_CANCEL_RIGHT,
};
enum class LcReason
{
    LC_REASON_NONE,
    LC_REASON_MANUAL,
    LC_REASON_EMERGENCY,
    LC_REASON_FOR_MERGE,
    LC_REASON_FOR_SPLIT,
    LC_REASON_FOR_NAVI,
    LC_REASON_OVERTAKE,
    LC_REASON_CENTER,
    LC_REASON_FOR_AVOID_VEHICLE,
    LC_REASON_FOR_AVOID_STATIC_OBJECT,
    LC_REASON_FOR_AVOID_ROADWORK,
    LC_REASON_FOR_AVOID_GENERAL,
    LC_REASON_FOR_AVOID_LANE_BUS,
    LC_REASON_FOR_AVOID_LANE_REVERSIBLE,
    LC_REASON_FOR_AVOID_VARIABLE_TURN,
    LC_REASON_FOR_AVOID_MULTI_DIR,
    LC_REASON_LANE_LOST,
    LC_REASON_BEST_TRUNK_LANE,
    LC_REASON_FASTER_TRUNK_LANE,
    LC_REASON_SLOWER_TRUNK_LANE,
    LC_REASON_BEST_MERGE_LANE,
    LC_REASON_EFFECTIVE,
    LC_REASON_NAVIGATOR_ADVICE,
};
enum class LcUnableReason
{
    LC_UNABLE_REASON_NONE,
    LC_UNABLE_REASON_NO_LANE,
    LC_UNABLE_REASON_OBSTACLE_FRONT,
    LC_UNABLE_REASON_OBSTACLE_REAR,
    LC_UNABLE_REASON_LINE_SOLID,
    LC_UNABLE_REASON_CURVATURE,
    LC_UNABLE_REASON_STATIC_OBSTACLE,
    LC_UNABLE_REASON_GENERAL_REASON,
    LC_UNABLE_REASON_NEAR_TO_INTERSECTION,
    LC_UNABLE_REASON_OVER_MAX_PREPARE_LANE_CHANGE_TIME,
    LC_UNABLE_REASON_LANE_PEDESTRIAN,
    LC_UNABLE_REASON_DRIVER_LATERAL_OVERRIDE,
    LC_UNABLE_REASON_DRIVER_LONGITUDINAL_OVERRIDE,
    LC_UNABLE_REASON_DRIVE_OVER_SPEED_LIMIT,
    LC_UNABLE_REASON_PASS_THROUGH_STATE_REGION_RISK_POINT,
    LC_UNABLE_REASON_REFERENCE_LINE_ID_NOT_SPECIFIED_ID,
    LC_UNABLE_REASON_ICA_ILC_SWITCH_OFF,
    LC_UNABLE_REASON_REVERSING_LEVER_CANCEL_LANE_CHANGE,
    LC_UNABLE_REASON_IMPASSABLE_OBS,
    LC_UNABLE_REASON_PASSED_TAKE_OVER_POINT,
    LC_UNABLE_REASON_LANE_CHANGE_DIRECTION_STRAIGHT,
    LC_UNABLE_REASON_NEAR_TO_TRAFFIC_LIGHT,
    LC_UNABLE_REASON_REFERENCE_LINE_SEQ,
    LC_UNABLE_REASON_CURRENT_LANE_STANDSTILL_OBS,
    LC_UNABLE_REASON_TARGET_LANE_STANDSTILL_OBS,
    LC_UNABLE_REASON_ADJACENT_LANE_OBS,
    LC_UNABLE_REASON_MRM_LON_STOP_REQUEST,
};
enum class LaneChangeSubState
{
    LANE_CHANGE_SUB_STATE_NONE,
    LANE_CHANGE_SUB_STATE_PUSH,
    LANE_CHANGE_SUB_STATE_HOLD_LEFT,
    LANE_CHANGE_SUB_STATE_HOLD_RIGHT,
};
enum class LaneChangeNotice
{
    LANE_CHANGE_NOTICE_NONE,
    LANE_CHANGE_NOTICE_LEFT_WAITING,
    LANE_CHANGE_NOTICE_RIGHT_WAITING,
    LANE_CHANGE_NOTICE_CONTINUOUS_LC,
    LANE_CHANGE_NOTICE_MISS_NAVI,
};
enum class IgnoreCrossLineCheck
{
    IGNORE_CROSS_LINE_CHECK_NONE,
    IGNORE_CROSS_LINE_CHECK_LEFT,
    IGNORE_CROSS_LINE_CHECK_RIGHT,
    IGNORE_CROSS_LINE_CHECK_BOTH,
};
enum class PlanningRequest
{
    NO_REQUEST,
    DRIVEOFF_REQUEST,
    LANE_CHANGE_LEFT_REQUEST,
    LANE_CHANGE_RIGHT_REQUEST,
    LANE_CHANGE_CANCEL_REQUEST,
    LANE_CHANGE_URBAN_NAVIGATION_LEFT_REQUEST,
    LANE_CHANGE_URBAN_NAVIGATION_RIGHT_REQUEST,
};
enum class RampState
{
    NOT_RAMP,
    RAMP_PREPARE_MERGE,
    RAMP_MERGE,
    RAMP_SUCCESS_MERGE,
    RAMP_CANCEL_MERGE,
    RAMP_PREPARE_SPLIT,
    RAMP_SPLIT,
    RAMP_SUCCESS_SPLIT,
    RAMP_CANCEL_SPLIT,
};
enum class TakeoverRequestType
{
    NO_TAKEOVER_REQUEST,
    STANDSTILL_OBSTACLE_AHEAD,
    LANE_LOST,
    PARALLEL_SINGLE_LANE_MERGE,
    DIRECT_SINGLE_LANE_MERGE,
    COMPOUND_DOUBLE_LANE_MERGE,
    INTELLIGNECE_AVOIDANCE,
    PLANNING_ERROR,
    REFERENCE_LINE_TOO_SHORT,
    PLANNING_ICA_ERROR,
    MERGE_EMERGENCY_RISK,
    LMS_DEL_BY,
    MODEL_REGION_LANE_CHANGE_LEFT,
    MODEL_REGION_LANE_CHANGE_RIGHT,
    MODEL_REGION_SPLIT_LEFT,
    MODEL_REGION_SPLIT_RIGHT,
    MODEL_REGION_MERGE_LEFT,
    MODEL_REGION_MERGE_RIGHT,
    OVER_SAFETY_SPEED_LIMIT,
    LON_PRIMAL_PROBLEM_ERROR,
    LANE_WIDTH_TOO_SMALL,
    MERGE_IS_RISK,
    LEAVING_HD_MAP_AREA,
    NO_GAP_TOO_LONG,
    STANDSTILL_OBS,
    TJP_TOR,
};
enum class ReasonDriverLCInvalid
{
    NONE,
    CURRENT_LANE_LOST,
    INFO_NOT_INITIALIZE,
    ODOMETRY_NOT_INITIALIZE,
    OVER_SPEED_LIMIT,
    REFERENCE_LINE_LENGTH_CHECK_FAILED,
    INTER_INTERSECTION,
    NEAR_TRAFFICLIGHT,
    CURRENT_LANE_WIDTH_CHECK_FAILED,
    LC_REQUEST_STATE_SEND_REQUEST,
    LC_REQUEST_STATE_AGREE_REQUEST,
    CONFIG_ACTIVE_LC_ENABLE_FALSE,
    FUNCTION_CONFIG_ICA_ILC_ENABLE_FALSE,
    FUNCTION_CONFIG_NDA_ILC_ENABLE_FALSE,
    ICA_STATE_ILC_SWITCH_OFF,
    UNDER_MIN_LC_SPEED,
    UNDER_SYSTEM_STARTUP_TIME,
    UNDER_LAST_LC_INTERVAL_TIME,
    DRIVER_LC_COMMAND_EMPTY,
    CURRENT_SEQUENCE_ARRAY_EMPTY,
    GET_CURRENT_LANE_SEQUENCE_FAILED,
    NO_LEFT_LANE,
    NO_RIGHT_LANE,
    LEFT_LANE_BOUNDARY_CHECK_FAILED,
    RIGHT_LANE_BOUNDARY_CHECK_FAILED,
    MRM_LON_REQUEST_STOP,
    DRIVER_LAT_OVERRIDE,
    TJP_UNABLE_LC,
};
struct AutomatonCheckStatus
{
    Activatestate activate_state;
    zos::bool_t is_crimping_boundary;
    zos::float32_t offset_to_center_line;
    zos::float32_t angle_to_center_line;
    zos::float32_t distance_to_boundary;
    zos::float32_t max_width;
    zos::float32_t min_width;
    zos::float32_t left_boundary_length;
    zos::float32_t right_boundary_length;
    zos::float32_t lane_min_radius;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(AutomatonCheckStatus, activate_state, is_crimping_boundary, offset_to_center_line, angle_to_center_line, distance_to_boundary, max_width, min_width, left_boundary_length, right_boundary_length, lane_min_radius)

using IDs=zos::FixedVector<zos::uint32_t, 8>;
struct PlanningState
{
    Result result;
    zos::uint8_t fail_reason;
    zos::uint32_t main_target_id;
    NudgeState nudge_state;
    IDs yield_obstacles;
    IDs overtake_obstacles;
    IDs nudge_obstacles;
    IDs obstacles_unmovable;
    IDs obstacles_in_queue;
    LaneBorrowReason lane_borrow_reason;
    LaneBorrowState lane_borrow_state;
    TurnLightReason turn_light_reason;
    TurnLight turn_light_request;
    LaneChangeState lc_state;
    LaneChangeSubState lc_sub_state;
    LcReason lc_reason;
    LcGapObstacles lc_gap_obstacles;
    LcUnableReason lc_unable_reason;
    LaneChangeNotice lc_notice;
    zos::float32_t stop_line;
    IgnoreCrossLineCheck ignore_cross_line_check;
    AutomatonCheckStatus automaton_check_status;
    PlanningRequest planning_request;
    RampState ramp_state;
    TakeoverRequestType takeover_request_type;
    ReasonDriverLCInvalid driver_lc_invalid_reason;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(PlanningState, result, fail_reason, main_target_id, nudge_state, yield_obstacles, overtake_obstacles, nudge_obstacles, obstacles_unmovable, obstacles_in_queue, lane_borrow_reason, lane_borrow_state, turn_light_reason, turn_light_request, lc_state, lc_sub_state, lc_reason, lc_gap_obstacles, lc_unable_reason, lc_notice, stop_line, ignore_cross_line_check, automaton_check_status, planning_request, ramp_state, takeover_request_type, driver_lc_invalid_reason)

struct MultiTrajDebug
{
    zos::uint8_t traj_id;
    zos::uint8_t lat_decision;
    zos::uint8_t lon_decision;
    zos::uint8_t traj_decision_reason;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(MultiTrajDebug, traj_id, lat_decision, lon_decision, traj_decision_reason)

using MultiTrajDebugs=zos::FixedVector<MultiTrajDebug, 4>;
struct ObstacleDebug
{
    zos::uint32_t id;
    zos::float32_t t;
    zos::float32_t ds;
    zos::float32_t dl;
    zos::float32_t v;
    zos::float32_t a;
    zos::float32_t v_l;
    zos::uint8_t lat_decision;
    zos::uint8_t lon_decision;
    zos::uint8_t intention;
    zos::float32_t relationship;
    zos::float32_t priority;
    zos::float32_t interaction_a;
    zos::float32_t posterior_a;
    zos::float32_t time_buffer;
    MultiTrajDebugs multi_traj_debug;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(ObstacleDebug, id, t, ds, dl, v, a, v_l, lat_decision, lon_decision, intention, relationship, priority, interaction_a, posterior_a, time_buffer, multi_traj_debug)

using ObstacleDebugs=zos::FixedVector<ObstacleDebug, 8>;
struct PlanningDebug
{
    TrajectoryPoint start_point;
    zos::bool_t reset_start_point;
    TrajectoryPoint vehicle_state;
    ObstacleDebug main_target;
    ObstacleDebugs obstacles_debug;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(PlanningDebug, start_point, reset_start_point, vehicle_state, main_target, obstacles_debug)

struct BoundaryLine
{
    zos::geometry::Point2dSequence left_boundary;
    zos::geometry::Point2dSequence right_boundary;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(BoundaryLine, left_boundary, right_boundary)

struct PlanningResult
{
    zos::common::Timestamp timestamp;
    zos::uint32_t seq;
    Trajectory trajectory;
    PlanningState state;
    PathPoints path_info;
    PathPoints reference_line;
    PathPoints backup_path_info;
    BoundaryLine driving_boundary;
    PlanningDebug debug;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(PlanningResult, timestamp, seq, trajectory, state, path_info, reference_line, backup_path_info, driving_boundary, debug)

}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::planning::PathPoint, StdLayoutTag>
{
    using ValueType = ::zos::planning::PathPoint;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::planning::PathPoint, StdLayoutTruncTag>
{
    using ValueType = ::zos::planning::PathPoint;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::planning::TrajectoryPoint, StdLayoutTag>
{
    using ValueType = ::zos::planning::TrajectoryPoint;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::planning::TrajectoryPoint, StdLayoutTruncTag>
{
    using ValueType = ::zos::planning::TrajectoryPoint;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::planning::Trajectory, StdLayoutTag>
{
    using ValueType = ::zos::planning::Trajectory;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::planning::Trajectory, StdLayoutTruncTag>
{
    using ValueType = ::zos::planning::Trajectory;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::planning::LcGapObstacles, StdLayoutTag>
{
    using ValueType = ::zos::planning::LcGapObstacles;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::planning::LcGapObstacles, StdLayoutTruncTag>
{
    using ValueType = ::zos::planning::LcGapObstacles;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::planning::AutomatonCheckStatus, StdLayoutTag>
{
    using ValueType = ::zos::planning::AutomatonCheckStatus;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::planning::AutomatonCheckStatus, StdLayoutTruncTag>
{
    using ValueType = ::zos::planning::AutomatonCheckStatus;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::planning::PlanningState, StdLayoutTag>
{
    using ValueType = ::zos::planning::PlanningState;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::planning::PlanningState, StdLayoutTruncTag>
{
    using ValueType = ::zos::planning::PlanningState;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::planning::MultiTrajDebug, StdLayoutTag>
{
    using ValueType = ::zos::planning::MultiTrajDebug;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::planning::MultiTrajDebug, StdLayoutTruncTag>
{
    using ValueType = ::zos::planning::MultiTrajDebug;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::planning::ObstacleDebug, StdLayoutTag>
{
    using ValueType = ::zos::planning::ObstacleDebug;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::planning::ObstacleDebug, StdLayoutTruncTag>
{
    using ValueType = ::zos::planning::ObstacleDebug;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, multi_traj_debug) + sizeof(uint32_t);
        size += sample.multi_traj_debug.size() * sizeof(decltype(sample.multi_traj_debug)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,multi_traj_debug));
        uint32_t des_size = offsetof(ValueType,multi_traj_debug) + element_size * sizeof(decltype(sample.multi_traj_debug)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::planning::PlanningDebug, StdLayoutTag>
{
    using ValueType = ::zos::planning::PlanningDebug;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::planning::PlanningDebug, StdLayoutTruncTag>
{
    using ValueType = ::zos::planning::PlanningDebug;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, obstacles_debug) + sizeof(uint32_t);
        size += sample.obstacles_debug.size() * sizeof(decltype(sample.obstacles_debug)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,obstacles_debug));
        uint32_t des_size = offsetof(ValueType,obstacles_debug) + element_size * sizeof(decltype(sample.obstacles_debug)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::planning::BoundaryLine, StdLayoutTag>
{
    using ValueType = ::zos::planning::BoundaryLine;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::planning::BoundaryLine, StdLayoutTruncTag>
{
    using ValueType = ::zos::planning::BoundaryLine;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, right_boundary) + sizeof(uint32_t);
        size += sample.right_boundary.size() * sizeof(decltype(sample.right_boundary)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,right_boundary));
        uint32_t des_size = offsetof(ValueType,right_boundary) + element_size * sizeof(decltype(sample.right_boundary)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::planning::PlanningResult, StdLayoutTag>
{
    using ValueType = ::zos::planning::PlanningResult;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::planning::PlanningResult, StdLayoutTruncTag>
{
    using ValueType = ::zos::planning::PlanningResult;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
