#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>
#include <basic_types/container/vector.hpp>

#include <zos_msg/common/common.hpp>
#include <zos_msg/common/timestamp.hpp>
namespace zos
{
namespace driving
{
enum class EWarningState
{
    STATE_NO_WARNING = 0,
    STATE_ACTIVE_LEVEL1 = 1,
    STATE_ACTIVE_LEVEL2 = 2,
};
enum class IF_REQUEST_STATUS
{
    IF_REQUEST_STATUS_STANDBY = 0,
    IF_REQUEST_STATUS_ACTIVE = 1,
    IF_REQUEST_STATUS_ERROR = 2,
};
struct CCRadarObjectInfo
{
    zos::uint16_t m_id;
    zos::float32_t m_dx;
    zos::float32_t m_dy;
    zos::float32_t m_vx;
    zos::float32_t m_vy;
    zos::float32_t m_ax;
    zos::float32_t m_ay;
    zos::float32_t m_vxRel;
    zos::float32_t m_vyRel;
    zos::float32_t m_length;
    zos::float32_t m_width;
    zos::float32_t m_heading;
    zos::float32_t m_existProb;
    zos::float32_t m_obstacleProb;
    zos::uint8_t m_radarObjType;
    zos::uint8_t m_radarObjMotionStatus;
    zos::float32_t m_ttcLon;
    zos::float32_t m_ttcLat;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CCRadarObjectInfo, m_id, m_dx, m_dy, m_vx, m_vy, m_ax, m_ay, m_vxRel, m_vyRel, m_length, m_width, m_heading, m_existProb, m_obstacleProb, m_radarObjType, m_radarObjMotionStatus, m_ttcLon, m_ttcLat)

struct CRcwOutput
{
    EWarningState m_rcwLeftState;
    EWarningState m_rcwRightState;
    EWarningState m_rcwState;
    CCRadarObjectInfo m_rcwLeftTargetObject;
    CCRadarObjectInfo m_rcwRightTargetObject;
    IF_REQUEST_STATUS m_rcwLeftFunctionState;
    IF_REQUEST_STATUS m_rcwRightFunctionState;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CRcwOutput, m_rcwLeftState, m_rcwRightState, m_rcwState, m_rcwLeftTargetObject, m_rcwRightTargetObject, m_rcwLeftFunctionState, m_rcwRightFunctionState)

struct CRctaOutput
{
    EWarningState m_rctaLeftState;
    EWarningState m_rctaRightState;
    CCRadarObjectInfo m_rctaLeftTargetObject;
    CCRadarObjectInfo m_rctaRightTargetObject;
    IF_REQUEST_STATUS m_rctaLeftFunctionState;
    IF_REQUEST_STATUS m_rctaRightFunctionState;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CRctaOutput, m_rctaLeftState, m_rctaRightState, m_rctaLeftTargetObject, m_rctaRightTargetObject, m_rctaLeftFunctionState, m_rctaRightFunctionState)

struct CFctaOutput
{
    EWarningState m_fctaLeftState;
    EWarningState m_fctaRightState;
    CCRadarObjectInfo m_fctaLeftTargetObject;
    CCRadarObjectInfo m_fctaRightTargetObject;
    IF_REQUEST_STATUS m_fctaLeftFunctionState;
    IF_REQUEST_STATUS m_fctaRightFunctionState;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CFctaOutput, m_fctaLeftState, m_fctaRightState, m_fctaLeftTargetObject, m_fctaRightTargetObject, m_fctaLeftFunctionState, m_fctaRightFunctionState)

enum class ELcaMainState
{
    CRF_LCA_MAINSTATE_OFF = 0,
    CRF_LCA_MAINSTATE_ERROR = 1,
    CRF_LCA_MAINSTATE_STANDBY = 2,
    CRF_LCA_MAINSTATE_ACTIVE = 3,
};
enum class ELcaSubSideState
{
    CRF_LCA_SubSTATE_STANDBY = 0,
    CRF_LCA_SubSTATE_ERROR = 1,
    CRF_LCA_SubSTATE_ACTIVE_LEVEL1 = 2,
    CRF_LCA_SubSTATE_ACTIVE_LEVEL2 = 3,
};
struct CLcaOutput
{
    ELcaMainState m_lcaMainState_e;
    ELcaSubSideState m_lcaSubStateLeft_e;
    ELcaSubSideState m_lcaSubStateRight_e;
    zos::uint16_t m_bsdLeZoneObjId_ub;
    zos::uint16_t m_bsdRiZoneObjId_ub;
    zos::float32_t m_bsdLeZoneObjDx_ub;
    zos::float32_t m_bsdRiZoneObjDx_ub;
    zos::float32_t m_bsdLeZoneObjVxRel_ub;
    zos::float32_t m_bsdRiZoneObjVxRel_ub;
    zos::uint16_t m_cvwLeZoneObjId_ub;
    zos::uint16_t m_cvwRiZoneObjId_ub;
    zos::float32_t m_cvwLeZoneObjDx_ub;
    zos::float32_t m_cvwRiZoneObjDx_ub;
    zos::float32_t m_cvwLeZoneObjVxRel_ub;
    zos::float32_t m_cvwRiZoneObjVxRel_ub;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CLcaOutput, m_lcaMainState_e, m_lcaSubStateLeft_e, m_lcaSubStateRight_e, m_bsdLeZoneObjId_ub, m_bsdRiZoneObjId_ub, m_bsdLeZoneObjDx_ub, m_bsdRiZoneObjDx_ub, m_bsdLeZoneObjVxRel_ub, m_bsdRiZoneObjVxRel_ub, m_cvwLeZoneObjId_ub, m_cvwRiZoneObjId_ub, m_cvwLeZoneObjDx_ub, m_cvwRiZoneObjDx_ub, m_cvwLeZoneObjVxRel_ub, m_cvwRiZoneObjVxRel_ub)

enum class EDowMainState
{
    CRF_DOW_MAINSTATE_OFF = 0,
    CRF_DOW_MAINSTATE_ERROR = 1,
    CRF_DOW_MAINSTATE_STANDBY = 2,
    CRF_DOW_MAINSTATE_ACTIVE = 3,
};
enum class EDowSubSideState
{
    CRF_DOW_SubSTATE_STANDBY = 0,
    CRF_DOW_SubSTATE_ERROR = 1,
    CRF_DOW_SubSTATE_ACTIVE_LEVEL1 = 2,
    CRF_DOW_SubSTATE_ACTIVE_LEVEL2_FRONT = 3,
    CRF_DOW_SubSTATE_ACTIVE_LEVEL2_REAR = 4,
    CRF_DOW_SubSTATE_ACTIVE_LEVEL2_FRONT_REAR = 5,
};
struct CDowOutput
{
    EDowMainState m_dowMainState_e;
    EDowSubSideState m_dowSubStateLeft_e;
    EDowSubSideState m_dowSubStateRight_e;
    zos::uint16_t m_dowLeZoneObjId_ub;
    zos::uint16_t m_dowRiZoneObjId_ub;
    zos::float32_t m_dowLeZoneObjDx_ub;
    zos::float32_t m_dowRiZoneObjDx_ub;
    zos::float32_t m_dowLeZoneObjVxRel_ub;
    zos::float32_t m_dowRiZoneObjVxRel_ub;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CDowOutput, m_dowMainState_e, m_dowSubStateLeft_e, m_dowSubStateRight_e, m_dowLeZoneObjId_ub, m_dowRiZoneObjId_ub, m_dowLeZoneObjDx_ub, m_dowRiZoneObjDx_ub, m_dowLeZoneObjVxRel_ub, m_dowRiZoneObjVxRel_ub)

using DowRejectReason_8=zos::FixedVector<zos::uint8_t, 8>;
using BsdRejectReason_9=zos::FixedVector<zos::uint8_t, 9>;
using CvwRejectReason_10=zos::FixedVector<zos::uint8_t, 10>;
struct DowLcaRadarObjInfo_st
{
    zos::bool_t m_isInRightSide_b;
    zos::uint16_t m_id_uw;
    zos::uint16_t m_idK1_uw;
    zos::float32_t m_dxFrontEdge_f;
    zos::float32_t m_dxFrontEdgeK1_f;
    zos::float32_t m_dxBackEdge_f;
    zos::float32_t m_dyInnerEdge_f;
    zos::float32_t m_dyOuterEdge_f;
    zos::float32_t m_ttc_f;
    zos::float32_t m_vx_f;
    zos::float32_t m_vxRel_f;
    zos::float32_t m_vyRel_f;
    zos::float32_t m_length_f;
    zos::float32_t m_width_f;
    zos::float32_t m_existProb_f;
    zos::float32_t m_obstacleProb_f;
    zos::uint8_t m_type_ub;
    zos::uint8_t m_motionStatus_ub;
    zos::float32_t m_changeToDowReleventTime_f;
    zos::float32_t m_changeToBsdReleventTime_f;
    zos::float32_t m_changeToCvwReleventTime_f;
    zos::float32_t m_objFarAwayFromEgoTime_f;
    zos::float32_t m_underTrackTime_f;
    zos::float32_t m_misTrackTime_f;
    zos::bool_t m_isNewUpdated_b;
    zos::bool_t m_isFarAwayEgo_b;
    zos::bool_t m_isOvertakenByEgo_b;
    zos::bool_t m_isInPredictionStatus_b;
    zos::bool_t m_isDowReleventObj_b;
    zos::bool_t m_isBsdReleventObj_b;
    zos::bool_t m_isCvwReleventObj_b;
    zos::bool_t m_isDowReleventObjCycle_b;
    zos::bool_t m_isBsdReleventObjCycle_b;
    zos::bool_t m_isCvwReleventObjCycle_b;
    zos::bool_t m_isDowReleventObjCycleK1_b;
    zos::bool_t m_isBsdReleventObjCycleK1_b;
    zos::bool_t m_isCvwReleventObjCycleK1_b;
    zos::uint8_t m_dowSuppress_ub;
    zos::uint8_t m_bsdSuppress_ub;
    zos::uint8_t m_cvwSuppress_ub;
    DowRejectReason_8 m_dowReleventRejectReason_ub;
    BsdRejectReason_9 m_bsdReleventRejectReason_ub;
    CvwRejectReason_10 m_cvwReleventRejectReason_ub;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(DowLcaRadarObjInfo_st, m_isInRightSide_b, m_id_uw, m_idK1_uw, m_dxFrontEdge_f, m_dxFrontEdgeK1_f, m_dxBackEdge_f, m_dyInnerEdge_f, m_dyOuterEdge_f, m_ttc_f, m_vx_f, m_vxRel_f, m_vyRel_f, m_length_f, m_width_f, m_existProb_f, m_obstacleProb_f, m_type_ub, m_motionStatus_ub, m_changeToDowReleventTime_f, m_changeToBsdReleventTime_f, m_changeToCvwReleventTime_f, m_objFarAwayFromEgoTime_f, m_underTrackTime_f, m_misTrackTime_f, m_isNewUpdated_b, m_isFarAwayEgo_b, m_isOvertakenByEgo_b, m_isInPredictionStatus_b, m_isDowReleventObj_b, m_isBsdReleventObj_b, m_isCvwReleventObj_b, m_isDowReleventObjCycle_b, m_isBsdReleventObjCycle_b, m_isCvwReleventObjCycle_b, m_isDowReleventObjCycleK1_b, m_isBsdReleventObjCycleK1_b, m_isCvwReleventObjCycleK1_b, m_dowSuppress_ub, m_bsdSuppress_ub, m_cvwSuppress_ub, m_dowReleventRejectReason_ub, m_bsdReleventRejectReason_ub, m_cvwReleventRejectReason_ub)

using DowLcaRadarObjInfo_32=zos::FixedVector<DowLcaRadarObjInfo_st, 32>;
struct CDowLcaIsmOutput
{
    DowLcaRadarObjInfo_32 m_rearObjInfo_ast;
    zos::uint8_t m_rearObjNumber_ub;
    zos::float32_t m_cycleDiffTime_f;
    zos::float32_t m_vxvRef_f;
    zos::float32_t m_vxDisp_f;
    zos::float32_t m_psiDtOpt_f;
    zos::float32_t m_egoTrajCurvature_f;
    zos::bool_t m_egoGearOnD_b;
    zos::bool_t m_doorsLeftFrontOpen_b;
    zos::bool_t m_doorsLeftRearOpen_b;
    zos::bool_t m_doorsRightFrontOpen_b;
    zos::bool_t m_doorsRightRearOpen_b;
    zos::bool_t m_turnLeverLeftOn_b;
    zos::bool_t m_turnLeverRightOn_b;
    zos::bool_t m_hazardLightOn_b;
    zos::bool_t m_dowConfigured_b;
    zos::bool_t m_dowError_b;
    zos::bool_t m_dowIhibit_b;
    zos::bool_t m_lcaConfigured_b;
    zos::bool_t m_lcaError_b;
    zos::bool_t m_lcaIhibit_b;
    zos::bool_t m_suppressDow_b;
    zos::bool_t m_suppressLca_b;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CDowLcaIsmOutput, m_rearObjInfo_ast, m_rearObjNumber_ub, m_cycleDiffTime_f, m_vxvRef_f, m_vxDisp_f, m_psiDtOpt_f, m_egoTrajCurvature_f, m_egoGearOnD_b, m_doorsLeftFrontOpen_b, m_doorsLeftRearOpen_b, m_doorsRightFrontOpen_b, m_doorsRightRearOpen_b, m_turnLeverLeftOn_b, m_turnLeverRightOn_b, m_hazardLightOn_b, m_dowConfigured_b, m_dowError_b, m_dowIhibit_b, m_lcaConfigured_b, m_lcaError_b, m_lcaIhibit_b, m_suppressDow_b, m_suppressLca_b)

enum class EConerRadarStatus
{
    NORMAL = 0,
    FAULT = 1,
    BLOCK = 2,
};
struct CCornerRadarOutput
{
    zos::uint32_t m_seq;
    zos::common::Timestamp m_Timestamp;
    CDowLcaIsmOutput m_dowLcaIsmOutput;
    CDowOutput m_dowOutput;
    CLcaOutput m_lcaOutput;
    CFctaOutput m_fctaOutput;
    CRctaOutput m_rctaOutput;
    CRcwOutput m_rcwOutput;
    EConerRadarStatus m_frontLeftRadarStatus;
    EConerRadarStatus m_frontRightRadarStatus;
    EConerRadarStatus m_rearLeftRadarStatus;
    EConerRadarStatus m_rearRightRadarStatus;
    EConerRadarStatus m_frontRadarStatus;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CCornerRadarOutput, m_seq, m_Timestamp, m_dowLcaIsmOutput, m_dowOutput, m_lcaOutput, m_fctaOutput, m_rctaOutput, m_rcwOutput, m_frontLeftRadarStatus, m_frontRightRadarStatus, m_rearLeftRadarStatus, m_rearRightRadarStatus, m_frontRadarStatus)

}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::driving::CCRadarObjectInfo, StdLayoutTag>
{
    using ValueType = ::zos::driving::CCRadarObjectInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CCRadarObjectInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::CCRadarObjectInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CRcwOutput, StdLayoutTag>
{
    using ValueType = ::zos::driving::CRcwOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CRcwOutput, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::CRcwOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CRctaOutput, StdLayoutTag>
{
    using ValueType = ::zos::driving::CRctaOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CRctaOutput, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::CRctaOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CFctaOutput, StdLayoutTag>
{
    using ValueType = ::zos::driving::CFctaOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CFctaOutput, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::CFctaOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CLcaOutput, StdLayoutTag>
{
    using ValueType = ::zos::driving::CLcaOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CLcaOutput, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::CLcaOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CDowOutput, StdLayoutTag>
{
    using ValueType = ::zos::driving::CDowOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CDowOutput, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::CDowOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::DowLcaRadarObjInfo_st, StdLayoutTag>
{
    using ValueType = ::zos::driving::DowLcaRadarObjInfo_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::DowLcaRadarObjInfo_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::DowLcaRadarObjInfo_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, m_cvwReleventRejectReason_ub) + sizeof(uint32_t);
        size += sample.m_cvwReleventRejectReason_ub.size() * sizeof(decltype(sample.m_cvwReleventRejectReason_ub)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,m_cvwReleventRejectReason_ub));
        uint32_t des_size = offsetof(ValueType,m_cvwReleventRejectReason_ub) + element_size * sizeof(decltype(sample.m_cvwReleventRejectReason_ub)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CDowLcaIsmOutput, StdLayoutTag>
{
    using ValueType = ::zos::driving::CDowLcaIsmOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CDowLcaIsmOutput, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::CDowLcaIsmOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CCornerRadarOutput, StdLayoutTag>
{
    using ValueType = ::zos::driving::CCornerRadarOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CCornerRadarOutput, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::CCornerRadarOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
