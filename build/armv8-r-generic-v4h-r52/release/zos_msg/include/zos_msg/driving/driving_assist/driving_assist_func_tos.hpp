#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>

#include <zos_msg/common/common.hpp>
#include <zos_msg/common/timestamp.hpp>
namespace zos
{
namespace driving
{
enum class EObjClassType
{
    TOS_OBJ_CLASS_INVALID = 0,
    TOS_OBJ_CLASS_CAR = 1,
    TOS_OBJ_CLASS_TRUCK = 2,
    TOS_OBJ_CLASS_BIKE = 3,
    TOS_OBJ_CLASS_BICYCLE = 4,
    TOS_OBJ_CLASS_PEDESTRIAN = 5,
    TOS_OBJ_CLASS_GENERAL_OBJ = 6,
    TOS_OBJ_CLASS_ANIMAL = 7,
    TOS_OBJ_CLASS_UNCERTAIN_VCL = 8,
    TOS_OBJ_CLASS_TWO_WHEELER = 9,
    TOS_OBJ_CLASS_BUS = 10,
};
enum class EObjMotionStatus
{
    TOS_OBJ_MOTION_STATUS_INVALID = 0,
    TOS_OBJ_MOTION_STATUS_UNKNOWN = 1,
    TOS_OBJ_MOTION_STATUS_MOVING_EGO_DIRECTION_DRIVING = 2,
    TOS_OBJ_MOTION_STATUS_MOVING_EGO_DIRECTION_REVERSING = 3,
    TOS_OBJ_MOTION_STATUS_MOVING_ONCOMING = 4,
    TOS_OBJ_MOTION_STATUS_MOVING_CROSSING = 5,
    TOS_OBJ_MOTION_STATUS_EGO_DIRECTION_STOPPED = 6,
    TOS_OBJ_MOTION_STATUS_STATIONARY = 7,
};
struct CCipvPropertyData
{
    zos::uint16_t m_cipvId;
    zos::float32_t m_cipvLongDistance;
    zos::float32_t m_cipvLatDistance;
    zos::float32_t m_objAbsLongVelocity;
    zos::float32_t m_objAbsLatVelocity;
    zos::float32_t m_objRelativeLongVelocity;
    zos::float32_t m_objRelativeLongAcc;
    zos::float32_t m_objAbsLongAcc;
    EObjMotionStatus m_cipvMotionStatus;
    EObjClassType m_cipvObjClass;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CCipvPropertyData, m_cipvId, m_cipvLongDistance, m_cipvLatDistance, m_objAbsLongVelocity, m_objAbsLatVelocity, m_objRelativeLongVelocity, m_objRelativeLongAcc, m_objAbsLongAcc, m_cipvMotionStatus, m_cipvObjClass)

struct CTosOutput
{
    CCipvPropertyData m_cipvProperty;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CTosOutput, m_cipvProperty)

}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::driving::CCipvPropertyData, StdLayoutTag>
{
    using ValueType = ::zos::driving::CCipvPropertyData;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CCipvPropertyData, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::CCipvPropertyData;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CTosOutput, StdLayoutTag>
{
    using ValueType = ::zos::driving::CTosOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CTosOutput, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::CTosOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
