#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>

#include <zos_msg/common/common.hpp>
#include <zos_msg/common/timestamp.hpp>
#include <zos_msg/driving/driving_assist/driving_assist_func_evi.hpp>
#include <zos_msg/driving/driving_assist/driving_assist_func_sem.hpp>
#include <zos_msg/driving/driving_assist/driving_assist_func_tos.hpp>
#include <zos_msg/driving/driving_assist/driving_assist_func_lda.hpp>
#include <zos_msg/driving/driving_assist/driving_assist_func_pla.hpp>
namespace zos
{
namespace driving
{
struct CAssistFuncOutput
{
    zos::uint32_t m_seq;
    zos::common::Timestamp m_Timestamp;
    CEviOutput m_eviOutput;
    CSemOutput m_semOutput;
    CTosOutput m_tosOutput;
    CLdaOutput m_ldaOutput;
    CPlaOutput m_plaOutput;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CAssistFuncOutput, m_seq, m_Timestamp, m_eviOutput, m_semOutput, m_tosOutput, m_ldaOutput, m_plaOutput)

}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::driving::CAssistFuncOutput, StdLayoutTag>
{
    using ValueType = ::zos::driving::CAssistFuncOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CAssistFuncOutput, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::CAssistFuncOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
