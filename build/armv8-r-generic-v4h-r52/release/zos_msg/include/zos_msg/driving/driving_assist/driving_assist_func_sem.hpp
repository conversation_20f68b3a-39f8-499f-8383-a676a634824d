#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/container/vector.hpp>
#include <basic_types/types.hpp>
#include <basic_types/container/array.hpp>

#include <zos_msg/common/common.hpp>
#include <zos_msg/common/timestamp.hpp>
namespace zos
{
namespace driving
{
using RoadGeometryFloatSeq_3=zos::FixedVector<zos::float32_t, 3>;
struct CRoadGeometry
{
    RoadGeometryFloatSeq_3 m_dY0_af;
    zos::float32_t m_alpPsi_f;
    RoadGeometryFloatSeq_3 m_kapC0_af;
    zos::float32_t m_kapDxC1_f;
    RoadGeometryFloatSeq_3 m_dWidth_af;
    zos::uint16_t m_LeftLeftLineId_uw;
    zos::uint16_t m_LeftLineId_uw;
    zos::uint16_t m_RightLineId_uw;
    zos::uint16_t m_RightRightLineId_uw;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CRoadGeometry, m_dY0_af, m_alpPsi_f, m_kapC0_af, m_kapDxC1_f, m_dWidth_af, m_LeftLeftLineId_uw, m_LeftLineId_uw, m_RightLineId_uw, m_RightRightLineId_uw)

struct CLaneGeometry
{
    zos::float32_t m_dY0;
    zos::float32_t m_alpPsi;
    zos::float64_t m_kapC0;
    zos::float64_t m_kapDxC1;
    zos::float32_t m_dWidth;
    zos::float32_t m_conf;
    zos::float32_t m_viewEnd;
    zos::bool_t m_available;
    zos::uint8_t m_LaneCenterLineSource_ub;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CLaneGeometry, m_dY0, m_alpPsi, m_kapC0, m_kapDxC1, m_dWidth, m_conf, m_viewEnd, m_available, m_LaneCenterLineSource_ub)

enum class ESemLaneType
{
    SEM_LANE_TYPE_LINE_UNDECIDED = 0,
    SEM_LANE_TYPE_SOLID_LINE = 1,
    SEM_LANE_TYPE_DASH_LINE = 2,
    SEM_LANE_TYPE_DML_LINE = 3,
    SEM_LANE_TYPE_BOTTS_DOTS = 4,
    SEM_LANE_TYPE_DECELERATION = 5,
    SEM_LANE_TYPE_HOV_LANE = 6,
    SEM_LANE_TYPE_ROADEDGE = 7,
    SEM_LANE_TYPE_LINE_RAMP_SOLID = 8,
    SEM_LANE_TYPE_LINE_RAMP_DASHED = 9,
    SEM_LANE_TYPE_SHADE_AREA = 10,
    SEM_LANE_TYPE_LANE_VIRTUAL_MARKING = 11,
};
enum class ESemHmiLaneType
{
    SEM_HMI_LINE_NO_TYPE = 0,
    SEM_HMI_LINE_SOLID = 1,
    SEM_HMI_LINE_DASHED = 2,
    SEM_HMI_LINE_DASHED_DASHED = 3,
    SEM_HMI_LINE_SOLID_SOLID = 4,
    SEM_HMI_LINE_DASHED_SOLID = 5,
    SEM_HMI_LINE_SOLID_DASHED = 6,
    SEM_HMI_LINE_ROAD_EDGE = 7,
    SEM_HMI_LINE_OTHERS = 8,
};
enum class ESemLaneLineColor
{
    SEM_LANE_LINE_COLOR_UNDECIDED = 0,
    SEM_LANE_LINE_COLOR_WHITE = 1,
    SEM_LANE_LINE_COLOR_YELLOW = 2,
    SEM_LANE_LINE_COLOR_BLUE = 3,
};
struct CLaneLinesInfo
{
    zos::bool_t m_isValid_b;
    ESemLaneType m_LineType_e;
    ESemHmiLaneType m_HmiLineType_e;
    ESemLaneLineColor m_lineColor_e;
    zos::float32_t m_dY0_f;
    zos::float32_t m_alpPsi_f;
    zos::float32_t m_kapC0_f;
    zos::float32_t m_kapDxC1_f;
    zos::float32_t m_LineConfidence_f;
    zos::float32_t m_ViewRangeEnd_f;
    zos::float32_t m_ViewRangeStart_f;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CLaneLinesInfo, m_isValid_b, m_LineType_e, m_HmiLineType_e, m_lineColor_e, m_dY0_f, m_alpPsi_f, m_kapC0_f, m_kapDxC1_f, m_LineConfidence_f, m_ViewRangeEnd_f, m_ViewRangeStart_f)

enum class ESemLaneSource
{
    SEM_LANE_SOURCE_NONE = 0,
    SEM_LANE_SOURCE_VIDEO = 1,
    SEM_LANE_SOURCE_MAP = 2,
};
struct LaneInfo_st
{
    zos::bool_t m_isUpdated;
    zos::bool_t m_isValid;
    zos::bool_t m_isInverseDir;
    zos::float32_t m_dViewRangEnd;
    CLaneGeometry m_LaneGeometry;
    CLaneGeometry m_LaneGeometryRaw;
    CLaneLinesInfo m_LeftLaneLinesInfo;
    CLaneLinesInfo m_RightLaneLinesInfo;
    ESemLaneSource m_LaneSource;
    zos::bool_t m_RoadEdgeReplaceLineFlag_b;
    zos::bool_t m_LeftLineIsValid_b;
    zos::bool_t m_RightLineIsValid_b;
    zos::uint8_t m_LaneValidSource_ub;
    zos::uint8_t m_DependTheLine_ub;
    zos::float32_t m_HistoryGeometryLineY0_f;
    zos::float32_t m_HistoryGeometryLinePsi_f;
    zos::float32_t m_HistoryGeometryLineC0_f;
    zos::float32_t m_HistoryGeometryLineC1_f;
    zos::bool_t m_HistoryGeometryLineIsValid_b;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(LaneInfo_st, m_isUpdated, m_isValid, m_isInverseDir, m_dViewRangEnd, m_LaneGeometry, m_LaneGeometryRaw, m_LeftLaneLinesInfo, m_RightLaneLinesInfo, m_LaneSource, m_RoadEdgeReplaceLineFlag_b, m_LeftLineIsValid_b, m_RightLineIsValid_b, m_LaneValidSource_ub, m_DependTheLine_ub, m_HistoryGeometryLineY0_f, m_HistoryGeometryLinePsi_f, m_HistoryGeometryLineC0_f, m_HistoryGeometryLineC1_f, m_HistoryGeometryLineIsValid_b)

enum class ELreType
{
    ROAD_EDGE_TYPE_UNDECIDED = 0,
    ROAD_EDGE_TYPE_FLAT = 1,
    ROAD_EDGE_TYPE_ELEVATED_STRCUTURE = 2,
    ROAD_EDGE_TYPE_CURB = 3,
    ROAD_EDGE_TYPE_CONES_POLES = 4,
    ROAD_EDGE_TYPE_PARKING_CARS = 5,
};
struct LaneRoadEdgeInfo_st
{
    zos::bool_t m_isValid;
    zos::float32_t m_lineConfidence;
    ELreType m_type;
    zos::float32_t m_dY0;
    zos::float32_t m_alpPsi;
    zos::float32_t m_kapC0;
    zos::float32_t m_kapDxC1;
    zos::float32_t m_viewRangeEnd;
    zos::float32_t m_viewRangeStart;
    zos::uint16_t m_RoadEdgeId_uw;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(LaneRoadEdgeInfo_st, m_isValid, m_lineConfidence, m_type, m_dY0, m_alpPsi, m_kapC0, m_kapDxC1, m_viewRangeEnd, m_viewRangeStart, m_RoadEdgeId_uw)

struct CHppInfo
{
    zos::bool_t m_hppAvailable;
    zos::float32_t m_hppConf;
    zos::float32_t m_dY0;
    zos::float32_t m_alpPsi;
    zos::float64_t m_kapC0;
    zos::float64_t m_kapDxC1;
    zos::float32_t m_viewRangeEnd;
    zos::float32_t m_halfWidth;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CHppInfo, m_hppAvailable, m_hppConf, m_dY0, m_alpPsi, m_kapC0, m_kapDxC1, m_viewRangeEnd, m_halfWidth)

using ElkLaneInfoBoolSeq_2=zos::FixedVector<zos::bool_t, 2>;
using ElkLaneInfoFloatSeq_2=zos::FixedVector<zos::float32_t, 2>;
using ElkLaneInfoSemLaneType_2=zos::FixedVector<ESemLaneType, 2>;
struct CElkLaneInfo
{
    zos::bool_t m_LaneValid;
    zos::bool_t m_LeftForbidLaneChange;
    zos::bool_t m_RightForbidLaneChange;
    ElkLaneInfoBoolSeq_2 m_isValid;
    zos::float32_t m_LaneWidth;
    ElkLaneInfoFloatSeq_2 m_dY0;
    ElkLaneInfoFloatSeq_2 m_alpPsi;
    ElkLaneInfoFloatSeq_2 m_kapC0;
    ElkLaneInfoFloatSeq_2 m_kapDxC1;
    ElkLaneInfoFloatSeq_2 m_LineWeight;
    ElkLaneInfoFloatSeq_2 m_RangeViewEnd;
    ElkLaneInfoSemLaneType_2 m_LineType;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CElkLaneInfo, m_LaneValid, m_LeftForbidLaneChange, m_RightForbidLaneChange, m_isValid, m_LaneWidth, m_dY0, m_alpPsi, m_kapC0, m_kapDxC1, m_LineWeight, m_RangeViewEnd, m_LineType)

enum class ESemObjClassType
{
    Sem_Obj_Type_None = 0,
    Sem_Obj_Type_ObstacleUnknown = 1,
    Sem_Obj_Type_VehicleUnknown = 2,
    Sem_Obj_Type_Car = 3,
    Sem_Obj_Type_Truck = 4,
    Sem_Obj_Type_Trailer = 5,
    Sem_Obj_Type_Bus = 6,
    Sem_Obj_Type_MotorTricycle = 7,
    Sem_Obj_Type_Wheeler2Unknown = 8,
    Sem_Obj_Type_Wheeler2Bicycle = 9,
    Sem_Obj_Type_Wheeler2Motorcycle = 10,
    Sem_Obj_Type_PedestrianUnknown = 11,
    Sem_Obj_Type_PedestrianChild = 12,
    Sem_Obj_Type_PedestrianAdult = 13,
    Sem_Obj_Type_Tricycle = 14,
    Sem_Obj_Type_AnimalUnknown = 15,
    Sem_Obj_Type_AnimalLarge = 16,
    Sem_Obj_Type_AnimalSmall = 17,
    Sem_Obj_Type_Emergency = 18,
    Sem_Obj_Type_TrafficCone = 19,
    Sem_Obj_Type_TriangleWarningPlate = 20,
    Sem_Obj_Type_Barriers = 21,
    Sem_Obj_Type_SpeedBump = 22,
    Sem_Obj_Type_ShoppingCart = 23,
    Sem_Obj_Type_GroundLock = 24,
    Sem_Obj_Type_LimitationBlock = 25,
    Sem_Obj_Type_Pillar = 26,
    Sem_Obj_Type_StonePier = 27,
    Sem_Obj_Type_FireHydrantCabinet = 28,
    Sem_Obj_Type_ChargingPile = 29,
    Sem_Obj_Type_ParkingProhbitationSign = 30,
    Sem_Obj_Type_TrashBin = 31,
    Sem_Obj_Type_Others = 32,
};
enum class ESemVisionMotionCategory
{
    MotionCategoryInvalid = 0,
    MotionCategoryUndefined = 1,
    MotionCategoryPassing = 2,
    MotionCategoryPassing_in = 3,
    MotionCategoryPassing_out = 4,
    MotionCategoryClose_cut_in = 5,
    MotionCategoryMoving_in = 6,
    MotionCategoryMoving_out = 7,
    MotionCategoryCrossing = 8,
    MotionCategoryLtap = 9,
    MotionCategoryRtap = 10,
    MotionCategoryMoving = 11,
    MotionCategoryPreceeding = 12,
    MotionCategoryOncoming = 13,
};
enum class EMotionType
{
    Unknown = 0,
    MovingEgoDirectionDriving = 1,
    MovingEgoDirectionReversing = 2,
    MovingOncoming = 3,
    MovingCrossing = 4,
    MovingEgoDirectionStopped = 5,
    Stationary = 6,
};
enum class ESemObjMotionOrientation
{
    MOT_OREINTATION_UNKNOWN = 0,
    MOT_OREINTATION_NORTH_EAST = 1,
    MOT_RESERVED0 = 2,
    MOT_OREINTATION_EAST = 3,
    MOT_RESERVED1 = 4,
    MOT_OREINTATION_SOUTH_EAST = 5,
    MOT_OREINTATION_SOUTH = 6,
    MOT_OREINTATION_SOUTH_WEST = 7,
    MOT_RESERVED2 = 8,
    MOT_OREINTATION_WEST = 9,
    MOT_RESERVED3 = 10,
    MOT_OREINTATION_NORTH_WEST = 11,
    MOT_OREINTATION_NORTH = 12,
    MOT_OREINTATION_INVALID = 13,
};
enum class ESensorType
{
    Unknown = 0,
    Vision = 1,
    FrontRadar = 2,
    FrontConerLeftRadar = 3,
    FrontConerRightRadar = 4,
    RearConerLeftRadar = 5,
    RearConerRightRadar = 6,
    Lidar = 7,
    CircularView = 8,
};
enum class ESemVisionLaneAssign
{
    LaneAssignUnknown = 0,
    LaneAssignLeftleft = 1,
    LaneAssignLeft = 2,
    LaneAssignHost = 3,
    LaneAssignRight = 4,
    LaneAssignRightright = 5,
};
enum class ESemObjVisibilitySide
{
    ObjVisibilitySideUnknown = 0,
    ObjVisibilitySideFront = 1,
    ObjVisibilitySideRear = 2,
};
enum class ESemLampStatus
{
    LampEnable = 0,
    LampLeftTurnSignalOn = 1,
    LampRightTurnSignalOn = 2,
    LampDoubleFlashLightOn = 3,
    LampBreakLightOn = 4,
    LampReversingLightOn = 5,
    LampHeadLampOn = 6,
};
enum class EPedSubType
{
    PED_SUB_TYPE_UNKNOWN = 0,
    PED_SUB_TYPE_ADULT = 1,
    PED_SUB_TYPE_CHILD = 2,
};
enum class EPedPos
{
    PED_POS_UNKNOWN = 0,
    PED_POS_BENDED = 1,
    PED_POS_CYCLIST = 2,
    PED_POS_LIER = 3,
    PED_POS_PEDESTRIAN = 4,
    PED_POS_SITTER = 5,
};
enum class EPedOrientation
{
    PED_ORIENTATION_UNKNOWN = 0,
    PED_ORIENTATION_BACK = 1,
    PED_ORIENTATION_FRONT = 2,
    PED_ORIENTATION_LEFT = 3,
    PED_ORIENTATION_LEFT_FRONT = 4,
    PED_ORIENTATION_LEFT_BACK = 5,
    PED_ORIENTATION_RIGHT = 6,
    PED_ORIENTATION_RIGHT_FRONT = 7,
    PED_ORIENTATION_RIGHT_BACK = 8,
};
using SensorIdSeq_6=zos::FixedVector<zos::uint16_t, 6>;
struct ObjInfo_st
{
    ESemObjClassType m_classification_ub;
    zos::float32_t m_classificationProb_f32;
    ESemVisionMotionCategory m_motionCategory_ub;
    EMotionType m_movingState_ub;
    ESemObjMotionOrientation m_motionOrientation_ub;
    zos::uint8_t m_behaviorType_ub;
    zos::uint8_t m_credibility_ub;
    zos::uint8_t m_lineID_ub;
    zos::uint8_t m_importantValue_ub;
    zos::uint16_t m_id_uw;
    zos::float32_t m_dx_f32;
    zos::float32_t m_dxStd_f32;
    zos::float32_t m_dy_f32;
    zos::float32_t m_vx_f32;
    zos::float32_t m_vy_f32;
    zos::float32_t m_ax_f32;
    zos::float32_t m_ay_f32;
    zos::float32_t m_width_f32;
    zos::float32_t m_length_f32;
    zos::float32_t m_relativeVx_f32;
    zos::float32_t m_relativeVy_f32;
    zos::float32_t m_relativeAx_f32;
    zos::float32_t m_relativeAy_f32;
    zos::float32_t m_obstacleProb_f32;
    zos::float32_t m_heading_f32;
    zos::bool_t m_headingValid_b;
    zos::float32_t m_tVRUExistTime_f32;
    zos::float32_t m_staVehExistTime_f32;
    zos::bool_t m_truckLenUpToStandard_b;
    zos::bool_t m_newVRUFlag_b;
    zos::bool_t m_newStaVehFlag_b;
    ESensorType m_sensorType_en;
    SensorIdSeq_6 m_sensorId_uw;
    zos::uint32_t m_fusionUpdateSensor_ul;
    zos::uint8_t m_objectsHasCutLane_ub;
    zos::uint8_t m_objectsHasCutPath_ub;
    ESemVisionLaneAssign m_objectsLaneAssignment_ub;
    zos::float32_t m_objectsAngleLeft_f32;
    zos::uint8_t m_objectsAngleLeftV_ub;
    zos::float32_t m_objectsAngleRight_f32;
    zos::uint8_t m_objectsAngleRightV_ub;
    ESemObjVisibilitySide m_objectsVisibilitySide_ub;
    zos::float32_t m_objectsExistenceProbability_f32;
    zos::float32_t m_objectRelativeVxStd_f32;
    zos::float32_t m_objectWidthStd_f32;
    ESemLampStatus m_lampStatus_en;
    EPedSubType m_fusionSensorPedSubType;
    EPedPos m_fusionSensorPedPos;
    EPedOrientation m_fusionSensorPedOrientation;
    zos::float32_t m_objectCutinSpeed_f32;
    zos::float32_t m_objectCutinLatDist_f32;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(ObjInfo_st, m_classification_ub, m_classificationProb_f32, m_motionCategory_ub, m_movingState_ub, m_motionOrientation_ub, m_behaviorType_ub, m_credibility_ub, m_lineID_ub, m_importantValue_ub, m_id_uw, m_dx_f32, m_dxStd_f32, m_dy_f32, m_vx_f32, m_vy_f32, m_ax_f32, m_ay_f32, m_width_f32, m_length_f32, m_relativeVx_f32, m_relativeVy_f32, m_relativeAx_f32, m_relativeAy_f32, m_obstacleProb_f32, m_heading_f32, m_headingValid_b, m_tVRUExistTime_f32, m_staVehExistTime_f32, m_truckLenUpToStandard_b, m_newVRUFlag_b, m_newStaVehFlag_b, m_sensorType_en, m_sensorId_uw, m_fusionUpdateSensor_ul, m_objectsHasCutLane_ub, m_objectsHasCutPath_ub, m_objectsLaneAssignment_ub, m_objectsAngleLeft_f32, m_objectsAngleLeftV_ub, m_objectsAngleRight_f32, m_objectsAngleRightV_ub, m_objectsVisibilitySide_ub, m_objectsExistenceProbability_f32, m_objectRelativeVxStd_f32, m_objectWidthStd_f32, m_lampStatus_en, m_fusionSensorPedSubType, m_fusionSensorPedPos, m_fusionSensorPedOrientation, m_objectCutinSpeed_f32, m_objectCutinLatDist_f32)

using LaneObjInfoUintSeq_3=zos::FixedVector<zos::uint16_t, 3>;
using LaneObjInfoSeq_20=zos::FixedVector<ObjInfo_st, 20>;
using LaneObjInfoMatrix=zos::Array<zos::Array<ObjInfo_st, 20>, 3>;
struct CLaneObjInfo
{
    ESemLaneSource m_laneSource;
    LaneObjInfoSeq_20 m_FrontObjInfo_st;
    LaneObjInfoUintSeq_3 m_FusionLaneFirstFrontObjIdx_ub;
    zos::uint16_t m_FirstFrontObjIdxInvalidValue;
    LaneObjInfoUintSeq_3 m_FusionLaneObjectNum_ub;
    LaneObjInfoMatrix m_FusionlaneObjData;
    zos::uint16_t m_CipvId_uw;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CLaneObjInfo, m_laneSource, m_FrontObjInfo_st, m_FusionLaneFirstFrontObjIdx_ub, m_FirstFrontObjIdxInvalidValue, m_FusionLaneObjectNum_ub, m_FusionlaneObjData, m_CipvId_uw)

using LaneRoadEdgeInfo_2=zos::FixedVector<LaneRoadEdgeInfo_st, 2>;
enum class SEM_PREDICTION_REASON_E
{
    SEM_PREDICTIONREASON_INVALID = 0,
    SEM_PREDICTIONREASON_LOSS = 1,
    SEM_PREDICTIONREASON_MERGE = 2,
    SEM_PREDICTIONREASON_SHADOW = 3,
    SEM_PREDICTIONREASON_DIVERGING = 4,
    SEM_PREDICTIONREASON_OCCLUDED = 5,
    SEM_PREDICTIONREASON_HEADWAY = 6,
};
struct LaneBoundaryInfo_st
{
    zos::float32_t Quality_f;
    zos::bool_t LaneCrossing_b;
    zos::bool_t IsValid_b;
    zos::uint8_t Type_ub;
    zos::uint8_t Color_ub;
    zos::uint8_t DLM_Type_ub;
    zos::uint8_t DECEL_Type_ub;
    zos::uint8_t Predict_ub;
    SEM_PREDICTION_REASON_E PredictionReason_e;
    zos::uint8_t AvailabilityState_ub;
    zos::float32_t dY0_f;
    zos::float32_t alpPsi_f;
    zos::float32_t kapC0_f;
    zos::float32_t kapDxC1_f;
    zos::float32_t dViewEnd_f;
    zos::float32_t dViewStart_f;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(LaneBoundaryInfo_st, Quality_f, LaneCrossing_b, IsValid_b, Type_ub, Color_ub, DLM_Type_ub, DECEL_Type_ub, Predict_ub, PredictionReason_e, AvailabilityState_ub, dY0_f, alpPsi_f, kapC0_f, kapDxC1_f, dViewEnd_f, dViewStart_f)

using LaneInfo_3=zos::FixedVector<LaneInfo_st, 3>;
struct CSemOutput
{
    zos::bool_t m_isLaneChange;
    zos::bool_t m_isLaneChangeLeft;
    zos::bool_t m_isLaneChangeRight;
    zos::bool_t m_isOvertakeLane_b;
    zos::bool_t m_isSignalLane_b;
    CRoadGeometry m_road;
    LaneInfo_3 m_lanes;
    LaneRoadEdgeInfo_2 m_roadEdges;
    CHppInfo m_hppInfo;
    CElkLaneInfo m_elkLane;
    CLaneObjInfo m_laneObjInfo;
    zos::bool_t m_isRadarFailure;
    zos::bool_t m_crossingFlag;
    zos::uint8_t m_videoStatus;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CSemOutput, m_isLaneChange, m_isLaneChangeLeft, m_isLaneChangeRight, m_isOvertakeLane_b, m_isSignalLane_b, m_road, m_lanes, m_roadEdges, m_hppInfo, m_elkLane, m_laneObjInfo, m_isRadarFailure, m_crossingFlag, m_videoStatus)

}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::driving::CRoadGeometry, StdLayoutTag>
{
    using ValueType = ::zos::driving::CRoadGeometry;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CRoadGeometry, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::CRoadGeometry;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CLaneGeometry, StdLayoutTag>
{
    using ValueType = ::zos::driving::CLaneGeometry;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CLaneGeometry, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::CLaneGeometry;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CLaneLinesInfo, StdLayoutTag>
{
    using ValueType = ::zos::driving::CLaneLinesInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CLaneLinesInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::CLaneLinesInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::LaneInfo_st, StdLayoutTag>
{
    using ValueType = ::zos::driving::LaneInfo_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::LaneInfo_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::LaneInfo_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::LaneRoadEdgeInfo_st, StdLayoutTag>
{
    using ValueType = ::zos::driving::LaneRoadEdgeInfo_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::LaneRoadEdgeInfo_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::LaneRoadEdgeInfo_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CHppInfo, StdLayoutTag>
{
    using ValueType = ::zos::driving::CHppInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CHppInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::CHppInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CElkLaneInfo, StdLayoutTag>
{
    using ValueType = ::zos::driving::CElkLaneInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CElkLaneInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::CElkLaneInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, m_LineType) + sizeof(uint32_t);
        size += sample.m_LineType.size() * sizeof(decltype(sample.m_LineType)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,m_LineType));
        uint32_t des_size = offsetof(ValueType,m_LineType) + element_size * sizeof(decltype(sample.m_LineType)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::ObjInfo_st, StdLayoutTag>
{
    using ValueType = ::zos::driving::ObjInfo_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::ObjInfo_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::ObjInfo_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CLaneObjInfo, StdLayoutTag>
{
    using ValueType = ::zos::driving::CLaneObjInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CLaneObjInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::CLaneObjInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::LaneBoundaryInfo_st, StdLayoutTag>
{
    using ValueType = ::zos::driving::LaneBoundaryInfo_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::LaneBoundaryInfo_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::LaneBoundaryInfo_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CSemOutput, StdLayoutTag>
{
    using ValueType = ::zos::driving::CSemOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CSemOutput, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::CSemOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
