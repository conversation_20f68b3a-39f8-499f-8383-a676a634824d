#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>

#include <zos_msg/common/common.hpp>
#include <zos_msg/common/timestamp.hpp>
namespace zos
{
namespace driving
{
enum class IF_DMK_LDA_LBS_STATE
{
    IF_DMK_LDA_LBS_START = 0,
    IF_DMK_LDA_LBS_RETURN = 1,
    IF_DMK_LDA_LBS_END = 2,
};
enum class IF_DMK_LDA_LBS_TRIGGER_TYPE
{
    IF_DMK_LDA_LBS_TRIGGER_NONE = 0,
    IF_DMK_LDA_LBS_TRIGGER_TRUCK = 1,
    IF_DMK_LDA_LBS_TRIGGER_CAR = 2,
};
enum class IF_DMK_LDA_LANE_MARKING
{
    IF_DMK_LDA_LANE_MARKING_NO = 0,
    IF_DMK_LDA_LANE_MARKING_LEFT = 1,
    IF_DMK_LDA_LANE_MARKING_RIGHT = 2,
    IF_DMK_LDA_LANE_MARKING_BOTH = 3,
};
struct CLdaOutput
{
    zos::bool_t m_isElkLeftInReactionZone;
    zos::bool_t m_isElkRightInReactionZone;
    zos::bool_t m_isElkRELeftTrigger;
    zos::bool_t m_isElkRERightTrigger;
    zos::bool_t m_isElkOCLeftTrigger;
    zos::bool_t m_isElkOCRightTrigger;
    zos::bool_t m_isElkOTLeftTrigger;
    zos::bool_t m_isElkOTRightTrigger;
    zos::bool_t m_ELKRoadedgeFg_b;
    zos::bool_t m_isLdpLeftInReactionZone;
    zos::bool_t m_isLdpRightInReactionZone;
    zos::bool_t m_isLdwLeftInReactionZone;
    zos::bool_t m_isLdwRightInReactionZone;
    zos::float32_t m_ldaLeftDistanceLaneCross;
    zos::float32_t m_ldaRightDistanceLaneCross;
    zos::float32_t m_elkActDLCLeft4DoubleRoadEge_f;
    zos::float32_t m_elkActDLCRight4DoubleRoadEge_f;
    zos::bool_t m_isLaneChangeLeft;
    zos::bool_t m_isLaneChangeRight;
    zos::bool_t m_isISMBSDLeftTrigger;
    zos::bool_t m_isISMBSDRightTrigger;
    zos::bool_t m_isISMCIPVID;
    zos::bool_t m_isISMLeftDLMLineType;
    zos::bool_t m_isISMRightDLMLineType;
    zos::bool_t m_isISMLeftDECELLineType;
    zos::bool_t m_isISMRightDECELLineType;
    zos::uint8_t m_lbsLeftState;
    zos::uint8_t m_lbsRightState;
    IF_DMK_LDA_LBS_TRIGGER_TYPE m_lbsLeftTriggerType;
    IF_DMK_LDA_LBS_TRIGGER_TYPE m_lbsRightTriggerType;
    zos::uint8_t m_lbsLeftTrigIndexInSEM;
    zos::uint8_t m_lbsRightTrigIndexInSEM;
    zos::uint8_t m_lbsLeftStateCar;
    zos::uint8_t m_lbsRightStateCar;
    zos::float32_t m_lbsLeftTruckDx;
    zos::float32_t m_lbsRightTruckDx;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CLdaOutput, m_isElkLeftInReactionZone, m_isElkRightInReactionZone, m_isElkRELeftTrigger, m_isElkRERightTrigger, m_isElkOCLeftTrigger, m_isElkOCRightTrigger, m_isElkOTLeftTrigger, m_isElkOTRightTrigger, m_ELKRoadedgeFg_b, m_isLdpLeftInReactionZone, m_isLdpRightInReactionZone, m_isLdwLeftInReactionZone, m_isLdwRightInReactionZone, m_ldaLeftDistanceLaneCross, m_ldaRightDistanceLaneCross, m_elkActDLCLeft4DoubleRoadEge_f, m_elkActDLCRight4DoubleRoadEge_f, m_isLaneChangeLeft, m_isLaneChangeRight, m_isISMBSDLeftTrigger, m_isISMBSDRightTrigger, m_isISMCIPVID, m_isISMLeftDLMLineType, m_isISMRightDLMLineType, m_isISMLeftDECELLineType, m_isISMRightDECELLineType, m_lbsLeftState, m_lbsRightState, m_lbsLeftTriggerType, m_lbsRightTriggerType, m_lbsLeftTrigIndexInSEM, m_lbsRightTrigIndexInSEM, m_lbsLeftStateCar, m_lbsRightStateCar, m_lbsLeftTruckDx, m_lbsRightTruckDx)

}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::driving::CLdaOutput, StdLayoutTag>
{
    using ValueType = ::zos::driving::CLdaOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CLdaOutput, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::CLdaOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
