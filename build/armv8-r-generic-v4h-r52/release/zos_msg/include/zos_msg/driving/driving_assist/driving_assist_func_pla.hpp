#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>

#include <zos_msg/common/common.hpp>
#include <zos_msg/common/timestamp.hpp>
namespace zos
{
namespace driving
{
enum class ETrajType
{
    PLA_TRAJTYPE_NO_TRAJECTORY = 0,
    PLA_TRAJTYPE_LANE_FOLLOW_TRAJECTORY = 1,
    PLA_TRAJTYPE_OBJECT_MOTION_TRAJECTORY = 2,
    PLA_TRAJTYPE_LANE_CHANGE_TRAJECTORY = 3,
    PLA_TRAJTYPE_ABORT_TRAJECTORY = 4,
    PLA_TRAJTYPE_LANE_TRANSITION_TRAJECTORY = 5,
};
enum class EPlaLineSource
{
    PLA_LINE_SOURCE_UNDEFINED = 0,
    PLA_LINE_SOURCE_HPP = 1,
    PLA_LINE_SOURCE_SEM = 2,
    PLA_LINE_SOURCE_HDMAP = 3,
    PLA_LINE_SOURCE_FUSED = 4,
};
enum class ALCREPLAN_REASON
{
    NO_REASON = 0,
    ALC_TRIGGER = 1,
    SPEED_CHANGE = 2,
    POSITION_JUMP = 3,
    ABORT_DELT_ENLARGE = 4,
    ABORT_PERIOD_CHANGE = 5,
    ALC_SLOWDOWN_TRIGGER = 6,
    AY_LIMIT = 7,
    DMK_RESET_ALC_TIME = 8,
    INVALID_RECOVERY = 9,
};
enum class ChooseHDMap_Reason
{
    PLA_CHOOSEHDMAP_NO = 0,
    PLA_CHOOSEHDMAP_MERGE_TO_RAMP = 1,
    PLA_CHOOSEHDMAP_VIRTUAL_LINE_IN_RAMP = 2,
    PLA_CHOOSEHDMAP_SPLIT_IN_RAMP = 3,
    PLA_CHOOSEHDMAP_DELAY = 4,
    PLA_CHOOSEHDMAP_LT_LANENUMCHG = 5,
};
enum class PlaNoaHDmapWarningInfo
{
    PLA_NOA_WARNING_NONE = 0,
    PLA_NOA_WARNING_MERGE_TO_RAMP = 1,
    PLA_NOA_WARNING_VIRTUAL_LINE_IN_RAMP = 2,
    PLA_NOA_WARNING_SPLIT_IN_RAMP = 3,
    PLA_NOA_WARNING_LINE_COLLISION = 4,
    PLA_NOA_WARNING_LANENUMCHG_IN_LT = 5,
};
struct CTrajectory
{
    zos::float32_t m_positionY0;
    zos::float32_t m_headingAngle;
    zos::float64_t m_kappaC0;
    zos::float64_t m_kappaDxC1;
    zos::float32_t m_viewRangeEnd;
    zos::float32_t m_viewRangeStart;
    zos::bool_t m_isValid;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CTrajectory, m_positionY0, m_headingAngle, m_kappaC0, m_kappaDxC1, m_viewRangeEnd, m_viewRangeStart, m_isValid)

struct QuinticPolyParameters_st
{
    zos::float32_t c0;
    zos::float32_t c1;
    zos::float64_t c2;
    zos::float64_t c3;
    zos::float64_t c4;
    zos::float64_t c5;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(QuinticPolyParameters_st, c0, c1, c2, c3, c4, c5)

struct QuinticPolyTrajectory_st
{
    QuinticPolyParameters_st m_QuinticPolyParams_st;
    zos::float32_t m_viewRangeStart_f;
    zos::float32_t m_viewRangeEnd_f;
    zos::bool_t m_isValid_b;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(QuinticPolyTrajectory_st, m_QuinticPolyParams_st, m_viewRangeStart_f, m_viewRangeEnd_f, m_isValid_b)

struct CPlaOutput
{
    CTrajectory m_targetTraj;
    CTrajectory m_egoTraj;
    CTrajectory m_centerLane;
    QuinticPolyTrajectory_st m_AlcQuinticPloyTraj;
    ETrajType m_plaTrajType;
    EPlaLineSource m_plaLineSource;
    zos::bool_t m_isTjaTrajValid;
    zos::bool_t m_isObjectFollowTrajValid;
    zos::bool_t m_isLaneChangeFinish;
    zos::bool_t m_isLaneChangeAbortFinish;
    zos::bool_t m_isLCLeftFlag_b;
    zos::bool_t m_isLCRightFlag_b;
    ALCREPLAN_REASON m_AlcReplanReason;
    zos::bool_t m_isBezierSmoothLine_b;
    PlaNoaHDmapWarningInfo m_PlaNoaHDmapWarning;
    ChooseHDMap_Reason m_PlaNoaChooseHdMapReason;
    zos::bool_t m_TjaTrajectoryValidOutput_b;
    zos::bool_t m_ObjectFollowTrajValidOutput_b;
    zos::bool_t m_pointNotEnough;
    zos::bool_t m_speedDistanceNotFit;
    zos::bool_t m_controlPointDyTooFar;
    zos::bool_t m_controlPointPsiTooLarge;
    zos::bool_t m_timerNotEnough;
    zos::bool_t m_controlPsiTooLarge;
    zos::bool_t m_controlTanTooLarge;
    zos::bool_t m_objDyTooLarge;
    zos::uint8_t m_insertPointNum;
    zos::uint8_t m_roationNum;
    zos::float32_t m_lastdyOutput;
    zos::float32_t m_lastpsiOutput;
    zos::float32_t m_lastc0Output;
    zos::float32_t m_lastc1Output;
    zos::bool_t m_alcAbortTimeChangeFlag_b;
    zos::bool_t m_alcAbortChangeLaneFlag_b;
    zos::bool_t m_alcTrajRePlanFlag_b;
    zos::bool_t m_alcTriggerWithLargePsi_b;
    zos::bool_t m_alcTrigger_b;
    zos::uint8_t m_alcAyNotSuitableFindTimes_ub;
    zos::uint8_t m_alcAbortTrajPositionEnlargeCounter_ub;
    zos::uint8_t m_alcAbortState_ub;
    zos::uint8_t m_alcMainStateK1_ub;
    zos::float32_t m_alcBasePositonFactor_f;
    zos::float32_t m_deltYawAngleAfterLineInvalid_f;
    zos::float32_t m_targetTrajInvalidTime_f;
    zos::float32_t m_alcBaseTime_f;
    zos::float32_t m_targetDx_f;
    zos::float32_t m_alcSetTime_f;
    zos::float32_t m_alcRemainTime_f;
    zos::float32_t m_alcSpendTime_f;
    zos::float32_t m_abortCompenPosition_f;
    zos::float32_t m_alcHeadingAngDiffClearTime_f;
    zos::float32_t m_alcSetDistance_f;
    zos::float32_t m_alcHasRunDistance_f;
    zos::bool_t m_ElkIsInTransitionTrajOutput_b;
    zos::bool_t m_LksIsInTransitionTrajOutput_b;
    zos::bool_t m_lbsUsingPlanTraj_b;
    zos::bool_t m_lbsRePlanTrigger_b;
    zos::bool_t m_lksTransUsingPlanTraj_b;
    zos::bool_t m_lksTransTrajReplanTrigger_b;
    zos::uint8_t m_lbsOffsetDir_ub;
    zos::uint8_t m_lksTransTrajType_ub;
    zos::uint8_t m_lksTransOffsetDir_ub;
    zos::uint8_t m_lbsLeftstatefg_ub;
    zos::uint8_t m_lbsRightstatefg_ub;
    zos::uint8_t m_avoidTosLeftLane_ub;
    zos::uint8_t m_avoidTosRightLane_ub;
    zos::uint8_t m_avoidLeftActive_ub;
    zos::uint8_t m_avoidRightActive_ub;
    zos::float32_t m_laneWidth_f;
    zos::float32_t m_roadEdgeFactor_f;
    zos::float32_t m_radiusFactor_f;
    zos::float32_t m_lbsDistance_f;
    zos::float32_t m_lbsTrajOfset_f;
    zos::float32_t m_lbsSpdFactor_f;
    zos::float32_t m_lbsTrajoffsetLine_f;
    zos::float32_t m_avoidDy_f;
    zos::float32_t m_avoidReturnTime_f;
    zos::float32_t m_avoidTrajOffset_f;
    zos::float32_t m_lbsActiveTime_f;
    zos::float32_t m_lksTransTrajActiveTime_f;
    zos::float32_t m_lksTransTrajPlanTime_f;
    zos::float32_t m_lksTransTrajPlanTimeMax_f;
    zos::float32_t m_leftAndRightLineY0DeltStableTime_f;
    zos::float32_t m_lksTransRecordLargeJumpY0_f;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CPlaOutput, m_targetTraj, m_egoTraj, m_centerLane, m_AlcQuinticPloyTraj, m_plaTrajType, m_plaLineSource, m_isTjaTrajValid, m_isObjectFollowTrajValid, m_isLaneChangeFinish, m_isLaneChangeAbortFinish, m_isLCLeftFlag_b, m_isLCRightFlag_b, m_AlcReplanReason, m_isBezierSmoothLine_b, m_PlaNoaHDmapWarning, m_PlaNoaChooseHdMapReason, m_TjaTrajectoryValidOutput_b, m_ObjectFollowTrajValidOutput_b, m_pointNotEnough, m_speedDistanceNotFit, m_controlPointDyTooFar, m_controlPointPsiTooLarge, m_timerNotEnough, m_controlPsiTooLarge, m_controlTanTooLarge, m_objDyTooLarge, m_insertPointNum, m_roationNum, m_lastdyOutput, m_lastpsiOutput, m_lastc0Output, m_lastc1Output, m_alcAbortTimeChangeFlag_b, m_alcAbortChangeLaneFlag_b, m_alcTrajRePlanFlag_b, m_alcTriggerWithLargePsi_b, m_alcTrigger_b, m_alcAyNotSuitableFindTimes_ub, m_alcAbortTrajPositionEnlargeCounter_ub, m_alcAbortState_ub, m_alcMainStateK1_ub, m_alcBasePositonFactor_f, m_deltYawAngleAfterLineInvalid_f, m_targetTrajInvalidTime_f, m_alcBaseTime_f, m_targetDx_f, m_alcSetTime_f, m_alcRemainTime_f, m_alcSpendTime_f, m_abortCompenPosition_f, m_alcHeadingAngDiffClearTime_f, m_alcSetDistance_f, m_alcHasRunDistance_f, m_ElkIsInTransitionTrajOutput_b, m_LksIsInTransitionTrajOutput_b, m_lbsUsingPlanTraj_b, m_lbsRePlanTrigger_b, m_lksTransUsingPlanTraj_b, m_lksTransTrajReplanTrigger_b, m_lbsOffsetDir_ub, m_lksTransTrajType_ub, m_lksTransOffsetDir_ub, m_lbsLeftstatefg_ub, m_lbsRightstatefg_ub, m_avoidTosLeftLane_ub, m_avoidTosRightLane_ub, m_avoidLeftActive_ub, m_avoidRightActive_ub, m_laneWidth_f, m_roadEdgeFactor_f, m_radiusFactor_f, m_lbsDistance_f, m_lbsTrajOfset_f, m_lbsSpdFactor_f, m_lbsTrajoffsetLine_f, m_avoidDy_f, m_avoidReturnTime_f, m_avoidTrajOffset_f, m_lbsActiveTime_f, m_lksTransTrajActiveTime_f, m_lksTransTrajPlanTime_f, m_lksTransTrajPlanTimeMax_f, m_leftAndRightLineY0DeltStableTime_f, m_lksTransRecordLargeJumpY0_f)

}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::driving::CTrajectory, StdLayoutTag>
{
    using ValueType = ::zos::driving::CTrajectory;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CTrajectory, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::CTrajectory;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::QuinticPolyParameters_st, StdLayoutTag>
{
    using ValueType = ::zos::driving::QuinticPolyParameters_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::QuinticPolyParameters_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::QuinticPolyParameters_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::QuinticPolyTrajectory_st, StdLayoutTag>
{
    using ValueType = ::zos::driving::QuinticPolyTrajectory_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::QuinticPolyTrajectory_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::QuinticPolyTrajectory_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CPlaOutput, StdLayoutTag>
{
    using ValueType = ::zos::driving::CPlaOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CPlaOutput, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::CPlaOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
