#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>

#include <zos_msg/common/common.hpp>
#include <zos_msg/common/timestamp.hpp>
namespace zos
{
namespace driving
{
enum class EWiperStatus
{
    WIPER_STATUS_OFF = 0,
    WIPER_STATUS_AUTO = 1,
    WIPER_STATUS_LOW = 2,
    WIPER_STATUS_MEDIUM = 3,
    WIPER_STATUS_HIGH = 4,
};
struct CEviOutput
{
    zos::bool_t m_axvRoadSlopeValid;
    zos::float32_t m_axvRoadSlope;
    zos::bool_t m_axvSensorMeanValid;
    zos::float32_t m_axvSensorMean;
    zos::float32_t m_acceleratorPedalSpeed;
    zos::float32_t m_egoTrajCurvature;
    zos::float32_t m_egoTrajCurvatureDt;
    zos::float32_t m_egoTrajCurvatureRaw;
    zos::float32_t m_alpSideSlipAngle;
    zos::float32_t m_alpWheelAngleKorr;
    zos::float32_t m_axvRoadSlopePeakF;
    zos::float32_t m_axvRef;
    zos::float32_t m_axvFeedbackVLC;
    zos::float32_t m_axvRefErr;
    zos::float32_t m_axvSensor;
    zos::float32_t m_ayvRef;
    zos::float32_t m_ayvOffset;
    zos::float32_t m_ayvRefErr;
    zos::bool_t m_isVehicleStandstill;
    zos::float32_t m_psiDtAck;
    zos::float32_t m_psiDtDtLP;
    zos::float32_t m_psiDtDt;
    zos::float32_t m_psiDtOffDriftInd;
    zos::float32_t m_psiDtOptErr;
    zos::float32_t m_psiDtOptRaw;
    zos::float32_t m_psiDtOpt;
    zos::bool_t m_isPsiDtOptValid;
    zos::float32_t m_steeringWheelAngle;
    zos::float32_t m_steeringWheelAngleSpeedFilter;
    zos::float32_t m_torqueHandFilter;
    zos::bool_t m_torqueHandisValid;
    zos::float32_t m_torqueHandRaw;
    zos::float32_t m_vxvRef;
    zos::float32_t m_vxvRefErr;
    zos::bool_t m_vxvrefValid;
    zos::bool_t m_isLowBeamLampOn;
    zos::bool_t m_isHighBeamLampOn;
    EWiperStatus m_wiperStatus;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CEviOutput, m_axvRoadSlopeValid, m_axvRoadSlope, m_axvSensorMeanValid, m_axvSensorMean, m_acceleratorPedalSpeed, m_egoTrajCurvature, m_egoTrajCurvatureDt, m_egoTrajCurvatureRaw, m_alpSideSlipAngle, m_alpWheelAngleKorr, m_axvRoadSlopePeakF, m_axvRef, m_axvFeedbackVLC, m_axvRefErr, m_axvSensor, m_ayvRef, m_ayvOffset, m_ayvRefErr, m_isVehicleStandstill, m_psiDtAck, m_psiDtDtLP, m_psiDtDt, m_psiDtOffDriftInd, m_psiDtOptErr, m_psiDtOptRaw, m_psiDtOpt, m_isPsiDtOptValid, m_steeringWheelAngle, m_steeringWheelAngleSpeedFilter, m_torqueHandFilter, m_torqueHandisValid, m_torqueHandRaw, m_vxvRef, m_vxvRefErr, m_vxvrefValid, m_isLowBeamLampOn, m_isHighBeamLampOn, m_wiperStatus)

}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::driving::CEviOutput, StdLayoutTag>
{
    using ValueType = ::zos::driving::CEviOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CEviOutput, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::CEviOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
