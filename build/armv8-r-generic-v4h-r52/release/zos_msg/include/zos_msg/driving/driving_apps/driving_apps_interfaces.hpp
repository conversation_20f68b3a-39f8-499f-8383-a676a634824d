#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/container/string.hpp>

namespace zos
{
namespace driving
{
using String=zos::FixedString<128>;
enum class FunctionState
{
    OFF = 0,
    PASSIVE = 1,
    FAILURE = 2,
    READY = 3,
    ACTIVE_NORMAL = 4,
    ACTIVE_STAND_ACTIVE = 5,
    ACTIVE_STAND_WAIT = 6,
    LNG_OVERRIDE = 7,
    LAT_OVERRIDE = 8,
    LNG_LAT_OVERRIDE = 9,
    MRM_ACTIVE_NORMAL = 10,
    MRM_ACTIVE_LNG_OVERRIDE = 11,
    MRC = 12,
    BOM = 13,
    COUNT = 14,
};
enum class TcpFunctionState
{
    OFF = 0,
    PASSIVE = 1,
    STANDBY = 2,
    MAPPING = 3,
    RELOCATING = 4,
    READY = 5,
    COUNT = 6,
};
enum class FunctionSuppressionReason
{
    NONE = 0,
    DRIVER_BRAKE_PEDAL = 1,
    DOORS_OR_HOOD_NOT_ALL_CLOSED = 2,
    DRIVER_SEATBELT_UNLOCK = 3,
    VEHICLE_NOT_IN_DRIVING_GEAR = 4,
    HAND_BRAKE_IS_SET = 5,
    HAZARD_FLASHER_ON = 6,
    TURN_LIGHT_ACTIVE = 7,
    DRIVER_HAND_TORQUE_ODC_NOT_SATISFY = 8,
    MRM_TIMES_OVER_THRESHOLD = 9,
    LAT_ACCEL_NOT_SATISFY = 10,
    YAW_RATE_NOT_SATISFY = 11,
    STEERING_WHEEL_ANGLE_SPD_NOT_SATISFY = 12,
    VEHICLE_CRASH = 13,
    LOW_SPEED_FOLLOW_NOT_SATISFY = 14,
    VEHICLE_SPD_NOT_SATISFY = 15,
    VEHICLE_BCM_KEY_OFF = 16,
    VEHICLE_POSE_NOT_SATISFY = 17,
    STEERING_WHEEL_ANGLE_NOT_SATISFY = 18,
    VEHICLE_CONDITION_NOT_SATISFY = 19,
    VEHICLE_ICA_DISABLE = 20,
    VEHICLE_WHEEL_DIRECTION_BACKWARD = 21,
    CID_FORBID_UPGRADE_NOP = 22,
    NDA_ODC_FLAG_BEFORE_ACTIVE_NOT_SATISFY = 23,
    BRAKE_PEDAL_AND_STANDSTILL_NOT_SATISFY = 24,
    NDA_VEHICLE_COND_NOT_SATISFY = 25,
    HIGH_PRECISION_NOT_SATISFY = 26,
    NAVI_STATUS_NOT_SATISFY = 27,
    NAVI_OPERA_MODE_NOT_SATISFY = 28,
    NAVI_ROUTE_MODE_NOT_SATISFY = 29,
    NAVI_MAP_MODE_NOT_SATISFY = 30,
    NAVI_LANE_PATH_NOT_SATISFY = 31,
    EXIT_DIRECTLY_NOT_TOR = 32,
    EXIT_DIRECTLY_TOR = 33,
    PLANNING_NDA_AVAILABLE_NOT_SATISFY = 34,
    CLOSE_NAVI_DESTINATION = 35,
    GEOFENCES_NOT_SATISFY = 36,
    ILLUMINATION_NOT_SATISFY = 37,
    WETHER_NOT_SATISFY = 38,
    IGNITION_KEY_NOT_SATISFY = 39,
    CHARGING_CAP_NOT_SATISFY = 40,
    TPMS_NOT_SATISFY = 41,
    REARVIEW_MIRROR_NOT_SATISFY = 42,
    ENERGY_SOURCE_NOT_SATISFY = 43,
    VEHICLE_MOVE_BACKWARD = 44,
    VEHICLE_RUN_MODE_NOT_SATISFY = 45,
    NOP_BASIC_ACTIVE_NOT_SATISFY = 46,
    HANDS_OFF_WARNING_NOT_SATISFY = 47,
    DPT_STATE_FAILURE = 48,
    DIG_NOP_FAILURE_ACTIVATE = 49,
    NDA_FUNCTION_FLAG_NOT_SATISFY = 50,
    NDA_ENABLE_SWITCH_CLOSED = 51,
    DPT_STATE_PARKING = 52,
    SPEED_LIMIT_OVERRUN_NOT_SATISFY = 53,
    SPECIAL_FAILURE_TO_PASSIVE = 54,
    CAMERA_FUZZY_OVER_THREE_TIMES = 55,
    PLANNING_PATH_CROSS_ROAD_EDGE_ERROR = 56,
    LANE_LINE_FUSION_ERROR = 57,
    PLANNING_ICA_ERROR = 58,
    HIGHWAY_NOT_SATISFY = 59,
    VEH_CONFIG_DISABLE = 60,
    USER_SETTING_DISABLE = 61,
    PLANNING_ICA_UNAVAILABLE = 100,
    OCCUR_FAULT = 101,
    OCCUR_LIGHT_FAULT = 102,
    OCCUR_CAN_NOT_ACTIVE_AND_NO_LIGHT_FAULT = 103,
    RAINFALL_NOT_SATISFY = 104,
    EPS_SHAKE_HANDS_NOT_SATISFY = 105,
    LANE_LINES_NOT_SATISFY = 106,
    LANE_WIDTH_NOT_SATISFY = 107,
    CURVE_RADIUS_NOT_SATISFY = 108,
    DR_ODO_UNKNOW = 109,
    AVH_NOT_SATISFY = 110,
    DRIVING_HIBERNATION = 111,
    PLANNING_ACC_UNAVAILABLE = 112,
    HIGH_PRECISION_LOST_AS_NETWORK_ERROR = 118,
    UBLOX_STATUS_ERROR = 119,
};
enum class FunctionUpgradeReason
{
    NONE = 0,
    DRIVER_HAND_TORQUE_ODC_NOT_SATISFY = 1,
    HANDSOFF_WARNING = 2,
    DMS_NOT_SATISFY = 3,
    HIGH_PRECISION_LOST = 4,
    NAVI_STATUS_NOT_SATISFY = 5,
    NAVI_OPERATIONMODE_NOT_SATISFY = 6,
    NAVI_ROUTE_MODE_NOT_SATISFY = 7,
    NAVI_MAP_MODE_NOT_SATISFY = 8,
    NAVI_LANE_PATH_EMPTY = 9,
    HIGH_PRECISION_OR_NAVI_NOT_SATISFY = 10,
    NDA_FUNCTION_FLAG_NOT_SATISFY = 11,
    NDA_ENABLE_SWITCH_CLOSED = 12,
    PLANNING_NDA_UNAVAILABLE = 13,
    TURN_LIGHT_IS_ON = 14,
    NDA_VEHICLE_CONDITION_NOT_SATISFY = 15,
    NDA_VEHICLE_SPD_NOT_SATISFY = 16,
    RAINFALL_NOT_SATISFY = 17,
    ILLUMINATION_NOT_SATISFY = 18,
    GEOFENCES_NOT_SATISFY = 19,
    NDA_ODD_BEFORE_ACTIVE_NOT_SATISFY = 20,
    VEHICLE_NOT_IN_SINGLE_LANE_CRUOSE = 21,
    FAULT_NDA_NOT_ACTIVATE = 22,
    ELKA_FAULT = 23,
    EXIT_MCU_ACTIVE_TOR_OR_MRM = 24,
    EXIT_MCU_REQUEST_SOC_EXIT = 25,
    EXIT_DIRECT_NO_TOR = 26,
    DIAG_NDA_EXIT_DIRECT_FAULT = 27,
    DIAG_CAN_NOT_EMERGENCY_STOP_MRM = 28,
    EXIT_DIRECT_TOR = 29,
    DRIVER_SET_DEGRADE_ICA = 30,
    STANDSTILL_NOT_SATISFY = 31,
    LNG_OVERRIDE_TIME_OUT = 32,
    ICA_FUNCTION_NOT_ACTIVE = 33,
    PLANNING_PATH_CROSS_ROAD_EDGE_ERROR = 34,
    PLANNING_ICA_ERROR = 35,
};
enum class FunctionDegradeReason
{
    NONE = 0,
    PLANNING_STATE_ERROR = 1,
    OCCUR_ICA_CAN_NOT_ACTIVE_FAULT = 2,
    OCCUR_ICA_FAILURE_LIGHT_FAULT = 3,
    OCCUR_ICA_EXIT_DIRECT_FAULT = 4,
    OCCUR_NDA_CAN_NOT_ACTIVE_FAULT = 5,
    OCCUR_NDA_FAILURE_LIGHT_FAULT = 6,
    OCCUR_NDA_FAILURE_NO_LIGHT_FAULT = 7,
    OCCUR_MRM_FAULT_AND_NO_MRM_ABILITY = 8,
    HANDS_OFF_TIMEOUT_AND_NO_MRM_ABILITY = 9,
    DIAG_NDA_EXIT_DIRECT_FAULT = 10,
    DIAG_CAN_NOT_EMERGENCY_STOP_MRM = 11,
    BOM_OCCUR_EXIT_DIRECT_FAULT = 12,
    PLANNING_NOT_RESPONSE_CMD = 13,
    PLANNING_REQUEST_TOR = 14,
    PLANNING_ERROR_EXIT = 15,
    VEHICLE_OCCUR_CRASH = 16,
    YAW_RATE_NOT_SATISFY = 17,
    LAT_ACCEL_NOT_SATISFY = 18,
    STEERING_WHEEL_ANGLE_SPD_NOT_SATISFY = 19,
    AVH_NOT_SATISFY = 20,
    DR_PRECISION_UNKNOW = 21,
    LANE_WIDTH_NOT_SATISFY = 22,
    BOTH_LANE_LOST = 23,
    SINGLE_LANE_LOST_TIME_OUT = 24,
    CURVE_RADIUS_NOT_SATISFY = 25,
    ILLUMINATION_NOT_SATISFY = 26,
    GEOFENCES_NOT_SATISFY = 27,
    EPS_SHAKE_HANDS_NOT_SATISFY = 28,
    VEHICLE_POSE_ERROR = 29,
    OCCUR_ICA_TOR_FAULT_WITH_HANDS_ON = 30,
    EXIT_MCU_ACTIVE_TOR_OR_MRM = 31,
    EXIT_MCU_REQUEST_SOC_EXIT = 32,
    LNG_OCCUR_MRM_FAULT_AND_CAN_NOT_CP = 33,
    LAT_OCCUR_MRM_FAULT = 34,
    LAT_HANDSOFF_TIMEOUT = 35,
    LAT_HIGH_PRECISION_LOST = 36,
    LAT_NAVI_STATUS_NOT_SATISFY = 37,
    LAT_NAVI_OPERATIONMODE_NOT_SATISFY = 38,
    LAT_NAVI_ROUTE_MODE_NOT_SATISFY = 39,
    LAT_NAVI_MAP_MODE_NOT_SATISFY = 40,
    LAT_NAVI_LANE_PATH_EMPTY = 41,
    LAT_PASSIVE_VD_NOT_SATISFY = 42,
    LAT_ODD_NOT_SATISFY = 43,
    TOR_OCCUR_MRM_FAULT_AND_CAN_NOT_CP = 44,
    MRM_AND_CAN_NOT_CP = 45,
    OCCUR_NDA_TOR_FAULT = 46,
    NDA_NEED_PHASE_IN = 47,
    NDA_FUNC_UNAVAILABLE = 48,
    PILOT_HIBERNATION = 49,
    PLAN_TOR_108_CAN_TAKE_OVER = 50,
    LAT_EXIT_SLOPE_OVERRUN_WARNING = 51,
    LAT_EXIT_CURVATURE_OVERRUN_WARNING = 52,
    LAT_EXIT_TOLLGATE_WARNING = 53,
    TOR_SLOPE_OVERRUN_EXIT = 54,
    TOR_CURVATURE_OVERRUN_EXIT = 55,
    TOR_TOLLGATE_EXIT = 56,
    TOR_EXIST_TOR_TIMER_LAT_OVERRIDE = 57,
    TOR_TIMER_OVER_EXIT_THRESHOLD = 58,
    FAULT_TOR_TIMER_OVER_AND_CAN_NOT_MRM = 59,
    ICA_FUNC_UNAVAILABLE = 60,
    ACC_FUNC_UNAVAILABLE = 61,
    DPT_AUTOMATON_STATE_FAULT = 62,
    VEHICLE_MOVE_BACKWARD_EXIT = 63,
    VEHICLE_RUN_MODE_NOT_SATISFY_EXIT = 64,
    REARVIEW_MIRROR_NOT_SATISFY_EXIT = 65,
    EXIT_DIRECT_NO_TOR = 66,
    EXIT_DIRECT_TOR = 67,
    GEOFENCES_REACH_TOLLGATE = 68,
    GEOFENCES_REACH_SLOPE_OVERRUN = 69,
    WEATHER_WARNING_TIMEOUT = 70,
    ILLUMINATION_WARNING_TIMEOUT = 71,
    OTHER_FUNC_REQUEST_NOP_EXIT = 72,
    LANE_LINES_NOT_SATISFY_AFTER_ACTIVE = 73,
    VEHICLE_SPD_NOT_SATISFY_AFTER_ACTIVE = 74,
    NAVI_STATUS_NOT_SATISFY = 75,
    NAVI_OPERATIONMODE_NOT_SATISFY = 76,
    NAVI_ROUTE_MODE_NOT_SATISFY = 77,
    NAVI_MAP_MODE_NOT_SATISFY = 78,
    NAVI_LANE_PATH_EMPTY = 79,
    LNG_TARGET_CLOSE_TIME_OUT = 80,
    HIGH_PRECISION_LOST = 81,
    HANDS_OFF_WARNING_3_EXIT = 82,
    EYES_OFF_WARNING_3_EXIT = 83,
    WARNING_2_TIMES_OVER_EXIT = 84,
    TIMING_TO_EXIT_WARNING_TIMEOUT = 85,
    PLANNING_NDA_AVAILABLE_NOT_SATISFY = 86,
    TPMS_NOT_SATISFY_EXIT = 87,
    CURVATURE_HIGH_PRECISION_LOST = 88,
    GEOFENCES_REACH_CURVATURE_OVERRUN = 89,
    SPECIAL_FAILURE_TO_PASSIVE = 90,
    IPB_NOT_SATISF = 91,
    VCU_NOT_SATISF = 92,
    EPS_NOT_SATISF = 93,
    PLANNING_PATH_CROSS_ROAD_EDGE_ERROR = 94,
    LANE_LINE_FUSION_ERROR = 95,
    PLANNING_ICA_ERROR = 96,
    MAX_EYE_ACTIVE_ELKA = 100,
    MAX_EYE_ACTIVE_ESA = 101,
    MAX_EYE_ACTIVE_AES = 102,
    MAX_EYE_ACTIVE_AEB = 103,
    MAX_EYE_AEB_ACTIVE = 104,
    CHASSIS_NOT_SATISFY_HDC = 110,
    CHASSIS_NOT_SATISFY_ESP = 111,
    CHASSIS_NOT_SATISFY_ABS = 112,
    CHASSIS_NOT_SATISFY_TCS = 113,
    CHASSIS_NOT_SATISFY_EBD = 114,
    CHASSIS_NOT_SATISFY_CDD = 115,
    CHASSIS_SAFETY_NOT_SATISFY = 116,
    HIGH_PRECISION_LOST_AS_NETWORK_ERROR = 118,
    DRIVER_BRAKE_PEDAL = 140,
    WHEN_STAND_TRIGGER_LAT_OVERRIDE = 141,
    ICA_VEHICLE_CONDITION_NOT_SATISFY = 142,
    NDA_VEHICLE_CONDITION_NOT_SATISFY = 143,
    STANDSTILL_TIME_OUT = 144,
    VEHICLE_SPD_NOT_SATISFY = 145,
    RAINFALL_NOT_SATISFY = 146,
    DRIVER_SET_HAND_BRAKE = 147,
    DOOR_OR_HOOD_OPEN = 148,
    NOT_IN_DRIVING_GEAR = 149,
    DRIVER_SEATBELT_UNLOCK = 150,
    VEHICLE_BCM_KEY_OFF = 151,
    DRIVER_OPEN_HAZARD_FLASHER = 152,
    DRIVER_SET_EXIT_BUTTON = 153,
    LAT_OVERRIDE_TIME_OUT = 154,
    LNG_OVERRIDE_TIME_OUT = 155,
    EPB_LAT_OVERRIDE = 156,
    EPB_LNG_OVERRIDE = 157,
    TOR_OCCUR_LAT_OVERRIDE = 158,
    TOR_LNG_OVERRIDE_TIME_OU = 159,
    TOR_TIME_OUT = 160,
    TOR_LNG_OVERRIDE_CAN_TAKE_OVERU = 161,
    MRM_LNG_OVERRIDE_CAN_TAKE_OVER = 162,
    MRM_OCCUR_LAT_OVERRIDE = 163,
    MRM_LNG_OVERRIDE_TIME_OUT = 164,
    DRIVER_FATIGUE = 165,
    LAT_NDA_DISABLE = 166,
    ES_MRM_OCCUR_LNG_OVERRIDE = 167,
    MRC_OCCUR_LNG_OVERRIDE = 168,
    VCU_HANDSHAKE_NOT_SATISFY = 169,
    DOWN_GRADE_TO_ACC_SATISFY = 170,
    DOWN_GRADE_TO_ICC_SATISFY = 171,
    DOWN_GRADE_TO_ICA_SATISFY = 172,
    STEERING_WHEEL_ANGLE_NOT_SATISFY = 173,
    DRIVER_DEEP_ACC_PEDAL = 174,
    DRIVER_HAND_TORQUE_OVERRIDE = 175,
    CAMERA_FUZZY_OVER_THREE_TIMES = 176,
    FRONT_CAMERA_FUZZY = 177,
    ACC_EXIT = 200,
    ACC_EXIT_VEHFAILURE = 201,
    ACC_EXIT_WIPER_FAST = 202,
    ACC_EXIT_DOORS_OPEN = 203,
    ACC_EXIT_SEABELT_OPEN = 204,
    ACC_EXIT_GEAR_NOTD = 205,
    ACC_EXIT_WHLDIRECTON_NOT_STATISFY = 206,
    ACC_EXIT_EPB_NOT_STATISFY = 207,
    ACC_EXIT_HDC_ACTIVE = 208,
    ACC_EXIT_VDC_ACTIVE = 209,
    ACC_EXIT_ABS_ACTIVE = 210,
    ACC_EXIT_TCS_ACTIVE = 211,
    ACC_EXIT_EBD_ACTIVE = 212,
    ACC_EXIT_CDD_NOT_AVAILABLE = 213,
    ACC_EXIT_TARGET_LOST = 214,
    ACC_EXIT_CANCEL_BUTTON = 215,
    ACC_EXIT_SAFE_ACTIVE = 216,
    ACC_EXIT_DRIVING_HIBERNATION = 217,
    CHASSIS_STATE_LOST = 230,
    VEHICLE_STATE_LOST = 231,
    PLANNING_STATE_LOST = 232,
    CID_CONFIG_LOST = 233,
    DIAGNOSIS_INFO_LOST = 234,
    ACC_EXIT_ERROR = 250,
    NO_QUIT = 255,
};
enum class FunctionQuitReason
{
    NONE = 0,
    PLANNING_STATE_ERROR = 1,
    OCCUR_ICA_CAN_NOT_ACTIVE_FAULT = 2,
    OCCUR_ICA_FAILURE_LIGHT_FAULT = 3,
    OCCUR_ICA_EXIT_DIRECT_FAULT = 4,
    OCCUR_NDA_CAN_NOT_ACTIVE_FAULT = 5,
    OCCUR_NDA_FAILURE_LIGHT_FAULT = 6,
    OCCUR_NDA_FAILURE_NO_LIGHT_FAULT = 7,
    OCCUR_MRM_FAULT_AND_NO_MRM_ABILITY = 8,
    HANDS_OFF_TIMEOUT_AND_NO_MRM_ABILITY = 9,
    DIAG_NDA_EXIT_DIRECT_FAULT = 10,
    DIAG_CAN_NOT_EMERGENCY_STOP_MRM = 11,
    BOM_OCCUR_EXIT_DIRECT_FAULT = 12,
    PLANNING_NOT_RESPONSE_CMD = 13,
    PLANNING_REQUEST_TOR = 14,
    PLANNING_ERROR_EXIT = 15,
    VEHICLE_OCCUR_CRASH = 16,
    YAW_RATE_NOT_SATISFY = 17,
    LAT_ACCEL_NOT_SATISFY = 18,
    STEERING_WHEEL_ANGLE_SPD_NOT_SATISFY = 19,
    AVH_NOT_SATISFY = 20,
    DR_PRECISION_UNKNOW = 21,
    LANE_WIDTH_NOT_SATISFY = 22,
    BOTH_LANE_LOST = 23,
    SINGLE_LANE_LOST_TIME_OUT = 24,
    CURVE_RADIUS_NOT_SATISFY = 25,
    ILLUMINATION_NOT_SATISFY = 26,
    GEOFENCES_NOT_SATISFY = 27,
    EPS_SHAKE_HANDS_NOT_SATISFY = 28,
    VEHICLE_POSE_ERROR = 29,
    OCCUR_ICA_TOR_FAULT_WITH_HANDS_ON = 30,
    EXIT_MCU_ACTIVE_TOR_OR_MRM = 31,
    EXIT_MCU_REQUEST_SOC_EXIT = 32,
    LNG_OCCUR_MRM_FAULT_AND_CAN_NOT_CP = 33,
    LAT_OCCUR_MRM_FAULT = 34,
    LAT_HANDSOFF_TIMEOUT = 35,
    LAT_HIGH_PRECISION_LOST = 36,
    LAT_NAVI_STATUS_NOT_SATISFY = 37,
    LAT_NAVI_OPERATIONMODE_NOT_SATISFY = 38,
    LAT_NAVI_ROUTE_MODE_NOT_SATISFY = 39,
    LAT_NAVI_MAP_MODE_NOT_SATISFY = 40,
    LAT_NAVI_LANE_PATH_EMPTY = 41,
    LAT_PASSIVE_VD_NOT_SATISFY = 42,
    LAT_ODD_NOT_SATISFY = 43,
    TOR_OCCUR_MRM_FAULT_AND_CAN_NOT_CP = 44,
    MRM_AND_CAN_NOT_CP = 45,
    OCCUR_NDA_TOR_FAULT = 46,
    NDA_NEED_PHASE_IN = 47,
    NDA_FUNC_UNAVAILABLE = 48,
    PILOT_HIBERNATION = 49,
    PLAN_TOR_108_CAN_TAKE_OVER = 50,
    LAT_EXIT_SLOPE_OVERRUN_WARNING = 51,
    LAT_EXIT_CURVATURE_OVERRUN_WARNING = 52,
    LAT_EXIT_TOLLGATE_WARNING = 53,
    TOR_SLOPE_OVERRUN_EXIT = 54,
    TOR_CURVATURE_OVERRUN_EXIT = 55,
    TOR_TOLLGATE_EXIT = 56,
    TOR_EXIST_TOR_TIMER_LAT_OVERRIDE = 57,
    TOR_TIMER_OVER_EXIT_THRESHOLD = 58,
    FAULT_TOR_TIMER_OVER_AND_CAN_NOT_MRM = 59,
    ICA_FUNC_UNAVAILABLE = 60,
    ACC_FUNC_UNAVAILABLE = 61,
    DPT_AUTOMATON_STATE_FAULT = 62,
    VEHICLE_MOVE_BACKWARD_EXIT = 63,
    VEHICLE_RUN_MODE_NOT_SATISFY_EXIT = 64,
    REARVIEW_MIRROR_NOT_SATISFY_EXIT = 65,
    EXIT_DIRECT_NO_TOR = 66,
    EXIT_DIRECT_TOR = 67,
    GEOFENCES_REACH_TOLLGATE = 68,
    GEOFENCES_REACH_SLOPE_OVERRUN = 69,
    WEATHER_WARNING_TIMEOUT = 70,
    ILLUMINATION_WARNING_TIMEOUT = 71,
    OTHER_FUNC_REQUEST_NOP_EXIT = 72,
    LANE_LINES_NOT_SATISFY_AFTER_ACTIVE = 73,
    VEHICLE_SPD_NOT_SATISFY_AFTER_ACTIVE = 74,
    NAVI_STATUS_NOT_SATISFY = 75,
    NAVI_OPERATIONMODE_NOT_SATISFY = 76,
    NAVI_ROUTE_MODE_NOT_SATISFY = 77,
    NAVI_MAP_MODE_NOT_SATISFY = 78,
    NAVI_LANE_PATH_EMPTY = 79,
    LNG_TARGET_CLOSE_TIME_OUT = 80,
    HIGH_PRECISION_LOST = 81,
    HANDS_OFF_WARNING_3_EXIT = 82,
    EYES_OFF_WARNING_3_EXIT = 83,
    WARNING_2_TIMES_OVER_EXIT = 84,
    TIMING_TO_EXIT_WARNING_TIMEOUT = 85,
    PLANNING_NDA_AVAILABLE_NOT_SATISFY = 86,
    TPMS_NOT_SATISFY_EXIT = 87,
    CURVATURE_HIGH_PRECISION_LOST = 88,
    GEOFENCES_REACH_CURVATURE_OVERRUN = 89,
    SPECIAL_FAILURE_TO_PASSIVE = 90,
    IPB_NOT_SATISF = 91,
    VCU_NOT_SATISF = 92,
    EPS_NOT_SATISF = 93,
    PLANNING_PATH_CROSS_ROAD_EDGE_ERROR = 94,
    LANE_LINE_FUSION_ERROR = 95,
    PLANNING_ICA_ERROR = 96,
    FOG_LAMP_ON = 97,
    MAX_EYE_ACTIVE_ELKA = 100,
    MAX_EYE_ACTIVE_ESA = 101,
    MAX_EYE_ACTIVE_AES = 102,
    MAX_EYE_ACTIVE_AEB = 103,
    MAX_EYE_AEB_ACTIVE = 104,
    CHASSIS_NOT_SATISFY_HDC = 110,
    CHASSIS_NOT_SATISFY_ESP = 111,
    CHASSIS_NOT_SATISFY_ABS = 112,
    CHASSIS_NOT_SATISFY_TCS = 113,
    CHASSIS_NOT_SATISFY_EBD = 114,
    CHASSIS_NOT_SATISFY_CDD = 115,
    CHASSIS_SAFETY_NOT_SATISFY = 116,
    HIGH_PRECISION_LOST_AS_NETWORK_ERROR = 118,
    DRIVER_BRAKE_PEDAL = 140,
    WHEN_STAND_TRIGGER_LAT_OVERRIDE = 141,
    ICA_VEHICLE_CONDITION_NOT_SATISFY = 142,
    NDA_VEHICLE_CONDITION_NOT_SATISFY = 143,
    STANDSTILL_TIME_OUT = 144,
    VEHICLE_SPD_NOT_SATISFY = 145,
    RAINFALL_NOT_SATISFY = 146,
    DRIVER_SET_HAND_BRAKE = 147,
    DOOR_OR_HOOD_OPEN = 148,
    NOT_IN_DRIVING_GEAR = 149,
    DRIVER_SEATBELT_UNLOCK = 150,
    VEHICLE_BCM_KEY_OFF = 151,
    DRIVER_OPEN_HAZARD_FLASHER52U = 152,
    DRIVER_SET_EXIT_BUTTON = 153,
    LAT_OVERRIDE_TIME_OUT = 154,
    LNG_OVERRIDE_TIME_OUT = 155,
    EPB_LAT_OVERRIDE = 156,
    EPB_LNG_OVERRIDE = 157,
    TOR_OCCUR_LAT_OVERRIDE = 158,
    TOR_LNG_OVERRIDE_TIME_OU = 159,
    TOR_TIME_OUT = 160,
    TOR_LNG_OVERRIDE_CAN_TAKE_OVERU = 161,
    MRM_LNG_OVERRIDE_CAN_TAKE_OVER = 162,
    MRM_OCCUR_LAT_OVERRIDE = 163,
    MRM_LNG_OVERRIDE_TIME_OUT = 164,
    DRIVER_FATIGUE = 165,
    LAT_NDA_DISABLE = 166,
    ES_MRM_OCCUR_LNG_OVERRIDE = 167,
    MRC_OCCUR_LNG_OVERRIDE = 168,
    VCU_HANDSHAKE_NOT_SATISFY = 169,
    DOWN_GRADE_TO_ACC_SATISFY = 170,
    DOWN_GRADE_TO_ICC_SATISFY = 171,
    DOWN_GRADE_TO_ICA_SATISFY = 172,
    STEERING_WHEEL_ANGLE_NOT_SATISFY = 173,
    DRIVER_DEEP_ACC_PEDAL = 174,
    DRIVER_HAND_TORQUE_OVERRIDE = 175,
    CAMERA_FUZZY_OVER_THREE_TIMES = 176,
    FRONT_CAMERA_FUZZY = 177,
    ACC_EXIT = 200,
    ACC_EXIT_VEHFAILURE = 201,
    ACC_EXIT_WIPER_FAST = 202,
    ACC_EXIT_DOORS_OPEN = 203,
    ACC_EXIT_SEABELT_OPEN = 204,
    ACC_EXIT_GEAR_NOTD = 205,
    ACC_EXIT_WHLDIRECTON_NOT_STATISFY = 206,
    ACC_EXIT_EPB_NOT_STATISFY = 207,
    ACC_EXIT_HDC_ACTIVE = 208,
    ACC_EXIT_VDC_ACTIVE = 209,
    ACC_EXIT_ABS_ACTIVE = 210,
    ACC_EXIT_TCS_ACTIVE = 211,
    ACC_EXIT_EBD_ACTIVE = 212,
    ACC_EXIT_CDD_NOT_AVAILABLE = 213,
    ACC_EXIT_TARGET_LOST = 214,
    ACC_EXIT_CANCEL_BUTTON = 215,
    ACC_EXIT_SAFE_ACTIVE = 216,
    ACC_EXIT_DRIVING_HIBERNATION = 217,
    CHASSIS_STATE_LOST = 230,
    VEHICLE_STATE_LOST = 231,
    PLANNING_STATE_LOST = 232,
    CID_CONFIG_LOST = 233,
    DIAGNOSIS_INFO_LOST = 234,
    ACC_EXIT_ERROR = 250,
    NO_QUIT = 255,
};
enum class HandsOnLevel
{
    NONE = 0,
    HANDSON_LEVEL1 = 1,
    HANDSON_LEVEL2 = 2,
    HANDSON_LEVEL3 = 3,
};
enum class RequestLight
{
    NONE = 0,
    TURN_ON = 1,
    TURN_OFF = 2,
    DOUBLE_FLASH = 3,
    COUNT = 4,
};
enum class CommandType
{
    NONE_CMD = 0,
    EXIT_PLANNING = 1,
    ACTIVE_ACC = 2,
    ACTIVE_ICA = 3,
    ACTIVE_NDA = 4,
    ACTIVE_HOLD = 5,
    ACTIVE_DRIVEOFF = 6,
    ACTIVE_SAFE_STOP = 7,
    ACTIVE_STOP = 8,
    LANE_KEEP = 9,
    LANE_CHANGE_LEFT = 10,
    LANE_CHANGE_RIGHT = 11,
    ACTIVE_TOR = 12,
    ACTIVE_MRM_PULLOVER_STOP = 13,
    ACTIVE_MRM_COMFORT_STOP = 14,
    ACTIVE_MRM_EMERGENCY_STOP = 15,
    LAT_OVERRIDE = 16,
    LON_OVERRIDE = 17,
    FAULT_OR_ARTIFICIAL_DEGRADE_ICA = 18,
    ACTIVE_TJP = 19,
    ACTIVE_BOM = 20,
    CANCEL_SPLIT = 21,
    TCP_ACTIVE_MAPPING = 22,
    TCP_MAPPING_SAVE = 23,
    TCP_RELOCATING = 24,
    COUNT = 25,
};
enum class TimeGapGear
{
    Gear_None = 0,
    Gear_One = 1,
    Gear_Two = 2,
    Gear_Three = 3,
    Gear_Four = 4,
    Gear_Five = 5,
    Gear_Six = 6,
};
enum class LonState
{
    INACTIVE = 0,
    ACTIVE = 1,
};
enum class LatState
{
    INACTIVE = 0,
    ACTIVE = 1,
};
enum class PilotModeType
{
    None = 0,
    HNop_Mode = 1,
    Tcp_Mode = 2,
};
enum class NOPLaneChangeMode
{
    None = 0,
    Automatic = 1,
    Driver_Confirmed = 2,
};
enum class NOPLaneChangeStyle
{
    None = 0,
    Basic = 1,
    Normal = 2,
    Radical = 3,
};
enum class EnableBtnSt
{
    None = 0,
    Off = 1,
    On = 2,
};
enum class FCWWarnDistance
{
    None = 0,
    Near = 1,
    Normal = 2,
    Far = 3,
};
enum class LKSWarnType
{
    None = 0,
    Off = 1,
    Warning_only = 2,
    Warning_and_vibration = 3,
};
enum class CruiseSpdMode
{
    None = 0,
    Current_speed_mode = 1,
    ISLI_mode = 2,
};
enum class CruiseSpdOffsetMode
{
    None = 0,
    Fixed = 1,
    Percent = 2,
};
enum class ISLIWarnMode
{
    None = 0,
    Off = 1,
    Display_warning = 2,
    Sound_display_warning = 3,
};
enum class NOPLaneChangeWarnType
{
    None = 0,
    Off = 1,
    Warning_only = 2,
    Warning_vibration = 3,
};
enum class VoiceRemindSettingMode
{
    None = 0,
    Simple = 1,
    Detailed = 2,
};
enum class CrusingMainswitch
{
    CrusingMainswitch_OFF = 0,
    CrusingMainswitch_ICA_Upgrade_NOP = 1,
    CrusingMainswitch_NOP_Degrade_ICA = 2,
};
enum class CrusingSetspeedSwitch
{
    CrusingSetspeedSwitch_None = 0,
    CrusingSetspeedSwitch_Spd_Increase = 1,
    CrusingSetspeedSwitch_Spd_Decrease = 2,
};
enum class CrusingTimegapSwitch
{
    CrusingTimegapSwitch_None = 0,
    CrusingTimegapSwitch_Timegap_Increase = 1,
    CrusingTimegapSwitch_Timegap_Decrease = 2,
};
enum class TCPFunctionSwitch
{
    TCPFunctionSwitch_NONE = 0,
    TCPFunctionSwitch_OFF = 1,
    TCPFunctionSwitch_Active_TCP = 2,
};
enum class TCPMappingSwitch
{
    TCPMappingSwitch_TCP_Mapping_NONE = 0,
    TCPMappingSwitch_TCP_Mapping_OFF_Cancel_Save = 1,
    TCPMappingSwitch_TCP_Mapping_OFF_Confirm_Save = 2,
    TCPMappingSwitch_TCP_Mapping_Active = 3,
};
enum class TCPRelocatingSwitch
{
    TCPRelocatingSwitch_TCP_Relocating_NONE = 0,
    TCPRelocatingSwitch_TCP_Relocating_OFF = 1,
    TCPRelocatingSwitch_TCP_Relocating_Active = 2,
};
enum class TCPRouteManagement
{
    TCPRouteManagement_None = 0,
    TCPRouteManagement_Storage = 1,
    TCPRouteManagement_Delete = 2,
};
}
}
namespace zos
{
namespace serialization
{
}
}
