#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>

#include <zos_msg/common/common.hpp>
#include <zos_msg/common/timestamp.hpp>
#include <zos_msg/driving/driving_apps/driving_apps_interfaces.hpp>
namespace zos
{
namespace driving
{
struct CHmiAdccSetting
{
    EnableBtnSt nopSettingSt;
    NOPLaneChangeMode nopLaneChangeMode;
    NOPLaneChangeWarnType nopLaneChangeWarnType;
    NOPLaneChangeStyle nopLaneChangeStyle;
    EnableBtnSt icaSettingSt;
    CruiseSpdMode cruiseSpdMode;
    CruiseSpdOffsetMode cruiseSpdOffsetMode;
    zos::int32_t cruiseSpdOffsetSpd;
    zos::int32_t cruiseSpdOffsetPercent;
    ISLIWarnMode isliWarnMode;
    EnableBtnSt fcwSettingSt;
    FCWWarnDistance fcwWarnDistance;
    EnableBtnSt aebSettingSt;
    EnableBtnSt lksSettingSt;
    LKSWarnType lksWarnType;
    EnableBtnSt elkSettingSt;
    EnableBtnSt hodWarnSettingSt;
    EnableBtnSt tcpSettingSt;
    EnableBtnSt hpaSettingSt;
    EnableBtnSt vpaSettingSt;
    EnableBtnSt apaSettingSt;
    VoiceRemindSettingMode voiceRemindSettingMode;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CHmiAdccSetting, nopSettingSt, nopLaneChangeMode, nopLaneChangeWarnType, nopLaneChangeStyle, icaSettingSt, cruiseSpdMode, cruiseSpdOffsetMode, cruiseSpdOffsetSpd, cruiseSpdOffsetPercent, isliWarnMode, fcwSettingSt, fcwWarnDistance, aebSettingSt, lksSettingSt, lksWarnType, elkSettingSt, hodWarnSettingSt, tcpSettingSt, hpaSettingSt, vpaSettingSt, apaSettingSt, voiceRemindSettingMode)

struct CHmiCrusingSetting
{
    CrusingMainswitch crusing_main_switch;
    CrusingSetspeedSwitch crusing_set_speed_switch;
    CrusingTimegapSwitch crusing_timegap_switch;
    TCPFunctionSwitch TCP_function_switch;
    TCPMappingSwitch TCP_mapping_switch;
    TCPRelocatingSwitch TCP_relocating_switch;
    TCPRouteManagement TCP_route_management;
    String TCP_routeID;
    String TCP_routeName;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CHmiCrusingSetting, crusing_main_switch, crusing_set_speed_switch, crusing_timegap_switch, TCP_function_switch, TCP_mapping_switch, TCP_relocating_switch, TCP_route_management, TCP_routeID, TCP_routeName)

struct CHmiCommand
{
    CHmiAdccSetting m_adcc_setting;
    CHmiCrusingSetting m_crusing_setting;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CHmiCommand, m_adcc_setting, m_crusing_setting)

}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::driving::CHmiAdccSetting, StdLayoutTag>
{
    using ValueType = ::zos::driving::CHmiAdccSetting;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CHmiAdccSetting, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::CHmiAdccSetting;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CHmiCrusingSetting, StdLayoutTag>
{
    using ValueType = ::zos::driving::CHmiCrusingSetting;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CHmiCrusingSetting, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::CHmiCrusingSetting;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, TCP_routeName) + sizeof(uint32_t);
        size += sample.TCP_routeName.size() * sizeof(decltype(sample.TCP_routeName)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,TCP_routeName));
        uint32_t des_size = offsetof(ValueType,TCP_routeName) + element_size * sizeof(decltype(sample.TCP_routeName)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CHmiCommand, StdLayoutTag>
{
    using ValueType = ::zos::driving::CHmiCommand;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CHmiCommand, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::CHmiCommand;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
