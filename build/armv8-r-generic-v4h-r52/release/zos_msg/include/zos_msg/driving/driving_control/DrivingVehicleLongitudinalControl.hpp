#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>

#include <zos_msg/common/common.hpp>
#include <zos_msg/common/timestamp.hpp>
namespace zos
{
namespace driving
{
struct CDrivingVehicleLongitudinalControl
{
    zos::common::Timestamp m_timestamp;
    zos::common::Timestamp m_timeStampVehicleUtcSec;
    zos::bool_t m_vlcAxTarEna;
    zos::float32_t m_vlcAxTar;
    zos::bool_t m_vlcTorqueEna;
    zos::float32_t m_vlcTorque;
    zos::bool_t m_vlcDriveOffReq;
    zos::bool_t m_vlcStandstillReq;
    zos::float32_t m_vlcAxFeedback;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CDrivingVehicleLongitudinalControl, m_timestamp, m_timeStampVehicleUtcSec, m_vlcAxTarEna, m_vlcAxTar, m_vlcTorqueEna, m_vlcTorque, m_vlcDriveOffReq, m_vlcStandstillReq, m_vlcAxFeedback)

struct CDrivingVehicleLongitudinalControlDebugInfo
{
    zos::float32_t m_AccAxTarAim_f;
    zos::float32_t m_AccdAxTarUpperLimit_f;
    zos::float32_t m_AccdAxTarLowerLimit_f;
    zos::float32_t m_AccAxTarLowerComfBand_f;
    zos::float32_t m_AccAxTarUpperComfBand_f;
    zos::uint8_t m_AccUseUpperComfBandReq_ub;
    zos::uint8_t m_AccBrakePreferred_ub;
    zos::uint8_t m_AccStandstillReq_ub;
    zos::uint8_t m_AccDriveOffReq_ub;
    zos::uint8_t m_DriverOverride_ub;
    zos::float32_t m_MMotActWhl_f;
    zos::float32_t m_MMotMaxWhl_f;
    zos::float32_t m_MMotMinWhl_f;
    zos::float32_t m_MMotDrvReq_f;
    zos::uint16_t m_MotRpm_uw;
    zos::float32_t m_VxAct_f;
    zos::float32_t m_AxAct_f;
    zos::float32_t m_AxSensor_f;
    zos::uint8_t m_AxSensorValid_ub;
    zos::uint8_t m_VehicleStandstill_ub;
    zos::uint8_t m_GearStep_ub;
    zos::uint8_t m_GearStepValid_ub;
    zos::uint8_t m_ShiftInProgress_ub;
    zos::float32_t m_GearRatio_f;
    zos::uint8_t m_Bls_ub;
    zos::float32_t m_McPressure_f;
    zos::uint8_t m_BrakeForcePresentFromCan_ub;
    zos::uint8_t m_VlcVxAxAvailable_ub;
    zos::uint8_t m_TargetGearStep_ub;
    zos::uint8_t m_TargetGearStepValid_ub;
    zos::float32_t m_axvRoadSlope_f;
    zos::uint8_t m_axvRoadSlopeValid_b;
    zos::float32_t m_VlcAxTarFeedback_sw;
    zos::uint8_t m_VlcDcEna_ub;
    zos::uint8_t m_VlcEcEna_ub;
    zos::uint8_t m_VlcDriveOffReq_ub;
    zos::uint8_t m_VlcStandstillReq_ub;
    zos::uint8_t m_VlcActive_ub;
    zos::float32_t m_VlcAxTar_f;
    zos::float32_t m_VlcAxTarAim_f;
    zos::float32_t m_VlcAxFeedback_f;
    zos::float32_t m_VlcFxTar_f;
    zos::float32_t m_VlcFxTarFilter_f;
    zos::float32_t m_VlcFxTarPT_f;
    zos::float32_t m_FxMin_f;
    zos::uint8_t m_DriveOffCtrlActive_ub;
    zos::uint8_t m_DriveOffDcMode_ub;
    zos::uint8_t m_VlcMode_N;
    zos::uint8_t m_VlcIsActive_b;
    zos::uint8_t m_VlcDcEnaK1_ub;
    zos::uint8_t m_VlcSiP_ub;
    zos::float32_t m_TargetGearRatio_f;
    zos::float32_t m_TargetGearRatioFilter_f;
    zos::float32_t m_TargetGearRatioFilterK1_f;
    zos::float32_t m_AxSensorPT_f;
    zos::uint8_t m_BrakeForcePresent_ub;
    zos::float32_t m_VlcMMotTar_f;
    zos::float32_t m_AxSlope_f;
    zos::float32_t m_AxSlopeUphillTimer_f;
    zos::uint8_t m_AxSlopeUphillFlag_ub;
    zos::float32_t m_AxSlopeDownhillTimer_f;
    zos::uint8_t m_AxSlopeDownhillFlag_ub;
    zos::uint8_t m_DriverIsBraking_ub;
    zos::uint8_t m_BrakeForcePresentByInternalLogic_ub;
    zos::uint8_t m_BrakeForcePresentByMsPressure_ub;
    zos::uint8_t m_BrakeForcePresentByActTorque_ub;
    zos::uint8_t m_VlcAxTarBaseBranchID_ub;
    zos::float32_t m_VlcAxTarBase_f;
    zos::float32_t m_FxTarPFilter_f;
    zos::float32_t m_FxTarIFilter_f;
    zos::float32_t m_FxTarSFilter_f;
    zos::float32_t m_VlcAxCtrlDevAim_f;
    zos::float32_t m_VlcAxCtrlDev_f;
    zos::float32_t m_VlcFxTarSlopeComp_f;
    zos::uint8_t m_BypassCtrlActive_ub;
    zos::float32_t m_FxTarBypass_f;
    zos::float32_t m_LowPrioMaxForce_f;
    zos::float32_t m_LowPrioMinForce_f;
    zos::float32_t m_HighPrioMaxForce_f;
    zos::float32_t m_HighPrioMinForce_f;
    zos::uint8_t m_VlcActuatorPreDecision_ub;
    zos::uint8_t m_MMotMaxReached_ub;
    zos::uint8_t m_MMotMinReached_ub;
    zos::uint8_t m_BrakeAct_ub;
    zos::float32_t m_AxDiffLowerBandToAct_f;
    zos::float32_t m_AxDiffUpperBandToAct_f;
    zos::float32_t m_VlcAxDevEcToDcThres_f;
    zos::float32_t m_VlcAxDevDcToEcThres_f;
    zos::float32_t m_VlcAxLowerBandDevDcToEcThres_f;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CDrivingVehicleLongitudinalControlDebugInfo, m_AccAxTarAim_f, m_AccdAxTarUpperLimit_f, m_AccdAxTarLowerLimit_f, m_AccAxTarLowerComfBand_f, m_AccAxTarUpperComfBand_f, m_AccUseUpperComfBandReq_ub, m_AccBrakePreferred_ub, m_AccStandstillReq_ub, m_AccDriveOffReq_ub, m_DriverOverride_ub, m_MMotActWhl_f, m_MMotMaxWhl_f, m_MMotMinWhl_f, m_MMotDrvReq_f, m_MotRpm_uw, m_VxAct_f, m_AxAct_f, m_AxSensor_f, m_AxSensorValid_ub, m_VehicleStandstill_ub, m_GearStep_ub, m_GearStepValid_ub, m_ShiftInProgress_ub, m_GearRatio_f, m_Bls_ub, m_McPressure_f, m_BrakeForcePresentFromCan_ub, m_VlcVxAxAvailable_ub, m_TargetGearStep_ub, m_TargetGearStepValid_ub, m_axvRoadSlope_f, m_axvRoadSlopeValid_b, m_VlcAxTarFeedback_sw, m_VlcDcEna_ub, m_VlcEcEna_ub, m_VlcDriveOffReq_ub, m_VlcStandstillReq_ub, m_VlcActive_ub, m_VlcAxTar_f, m_VlcAxTarAim_f, m_VlcAxFeedback_f, m_VlcFxTar_f, m_VlcFxTarFilter_f, m_VlcFxTarPT_f, m_FxMin_f, m_DriveOffCtrlActive_ub, m_DriveOffDcMode_ub, m_VlcMode_N, m_VlcIsActive_b, m_VlcDcEnaK1_ub, m_VlcSiP_ub, m_TargetGearRatio_f, m_TargetGearRatioFilter_f, m_TargetGearRatioFilterK1_f, m_AxSensorPT_f, m_BrakeForcePresent_ub, m_VlcMMotTar_f, m_AxSlope_f, m_AxSlopeUphillTimer_f, m_AxSlopeUphillFlag_ub, m_AxSlopeDownhillTimer_f, m_AxSlopeDownhillFlag_ub, m_DriverIsBraking_ub, m_BrakeForcePresentByInternalLogic_ub, m_BrakeForcePresentByMsPressure_ub, m_BrakeForcePresentByActTorque_ub, m_VlcAxTarBaseBranchID_ub, m_VlcAxTarBase_f, m_FxTarPFilter_f, m_FxTarIFilter_f, m_FxTarSFilter_f, m_VlcAxCtrlDevAim_f, m_VlcAxCtrlDev_f, m_VlcFxTarSlopeComp_f, m_BypassCtrlActive_ub, m_FxTarBypass_f, m_LowPrioMaxForce_f, m_LowPrioMinForce_f, m_HighPrioMaxForce_f, m_HighPrioMinForce_f, m_VlcActuatorPreDecision_ub, m_MMotMaxReached_ub, m_MMotMinReached_ub, m_BrakeAct_ub, m_AxDiffLowerBandToAct_f, m_AxDiffUpperBandToAct_f, m_VlcAxDevEcToDcThres_f, m_VlcAxDevDcToEcThres_f, m_VlcAxLowerBandDevDcToEcThres_f)

}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::driving::CDrivingVehicleLongitudinalControl, StdLayoutTag>
{
    using ValueType = ::zos::driving::CDrivingVehicleLongitudinalControl;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CDrivingVehicleLongitudinalControl, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::CDrivingVehicleLongitudinalControl;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CDrivingVehicleLongitudinalControlDebugInfo, StdLayoutTag>
{
    using ValueType = ::zos::driving::CDrivingVehicleLongitudinalControlDebugInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CDrivingVehicleLongitudinalControlDebugInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::CDrivingVehicleLongitudinalControlDebugInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
