#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>

#include <zos_msg/common/common.hpp>
#include <zos_msg/common/timestamp.hpp>
namespace zos
{
namespace driving
{
struct LocTscDebugInfo
{
    zos::float32_t axvCvAim_f;
    zos::float32_t axvCvAimDynamic_f;
    zos::float32_t axvCvComfortBandUpperValue_f;
    zos::float32_t axvCvComfortBandLowerValue_f;
    zos::float32_t axvCvComfortBandUpperValueDyn_f;
    zos::float32_t axvCvComfortBandLowerValueDyn_f;
    zos::float32_t aDtUpperLimitAxvCv_f;
    zos::float32_t aDtLowerLimitAxvCv_f;
    zos::bool_t ConsiderSSCData_b;
    zos::bool_t RaiseAxvCvCloseToANoDrvAction_b;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(LocTscDebugInfo, axvCvAim_f, axvCvAimDynamic_f, axvCvComfortBandUpperValue_f, axvCvComfortBandLowerValue_f, axvCvComfortBandUpperValueDyn_f, axvCvComfortBandLowerValueDyn_f, aDtUpperLimitAxvCv_f, aDtLowerLimitAxvCv_f, ConsiderSSCData_b, RaiseAxvCvCloseToANoDrvAction_b)

struct LocCscDebugInfo
{
    zos::float32_t axvCv_f;
    zos::float32_t aDtUpperLimitAxvCv_f;
    zos::float32_t aDtLowerLimitAxvCv_f;
    zos::float32_t axvCvComfortBandUpperValue_f;
    zos::float32_t axvCvComfortBandLowerValue_f;
    zos::bool_t LowSpeedControl_b;
    zos::bool_t TOIsDisappear_b;
    zos::float32_t TODisappearTime_f;
    zos::bool_t ConsiderCSCData_b;
    zos::float32_t kapCSC_f;
    zos::float32_t kapCSCLane_f;
    zos::float32_t kapCSCTraj_f;
    zos::float32_t kapCurveTrajFilt_f;
    zos::float32_t tauPropCtrlVDiff_f;
    zos::float32_t tauPredict_f;
    zos::float32_t vxvCSCDesiredMaxPO_f;
    zos::float32_t vxvCSCDesired_f;
    zos::float32_t ayCSCMaxDesired_f;
    zos::float32_t CSCfactor4curvaturDt_f;
    zos::float32_t LeftLBQualityK1_f;
    zos::float32_t RightLBQualityK1_f;
    zos::float32_t kapCurveTrajFiltK1_f;
    zos::float32_t HppC1Filter_f;
    zos::float32_t HppC0Filter_f;
    zos::float32_t HppPsiFilter_f;
    zos::float32_t CSCfactor4curvaturDtOriginal_f;
    zos::float32_t kapCSCIntegral_f;
    zos::float32_t kapCSCI_f;
    zos::float32_t ICSCGain_f;
    zos::float32_t ICSCGain1_f;
    zos::float32_t ICSCGain2_f;
    zos::float32_t ICSCGain3_f;
    zos::float32_t CSCfactorGainDecrease_f;
    zos::float32_t CSCfactorGainDecreaseC0_f;
    zos::float32_t CSCaxvCvMinValue_f;
    zos::float32_t CSCaxvCvCCtrl_f;
    zos::float32_t tauPGainByspeed_f;
    zos::float32_t tauPgainByC0_f;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(LocCscDebugInfo, axvCv_f, aDtUpperLimitAxvCv_f, aDtLowerLimitAxvCv_f, axvCvComfortBandUpperValue_f, axvCvComfortBandLowerValue_f, LowSpeedControl_b, TOIsDisappear_b, TODisappearTime_f, ConsiderCSCData_b, kapCSC_f, kapCSCLane_f, kapCSCTraj_f, kapCurveTrajFilt_f, tauPropCtrlVDiff_f, tauPredict_f, vxvCSCDesiredMaxPO_f, vxvCSCDesired_f, ayCSCMaxDesired_f, CSCfactor4curvaturDt_f, LeftLBQualityK1_f, RightLBQualityK1_f, kapCurveTrajFiltK1_f, HppC1Filter_f, HppC0Filter_f, HppPsiFilter_f, CSCfactor4curvaturDtOriginal_f, kapCSCIntegral_f, kapCSCI_f, ICSCGain_f, ICSCGain1_f, ICSCGain2_f, ICSCGain3_f, CSCfactorGainDecrease_f, CSCfactorGainDecreaseC0_f, CSCaxvCvMinValue_f, CSCaxvCvCCtrl_f, tauPGainByspeed_f, tauPgainByC0_f)

struct LocFocDebugInfo
{
    zos::float32_t axvCvComfortBandUpperValue_f;
    zos::float32_t axvCvComfortBandLowerValue_f;
    zos::float32_t axvCv_f;
    zos::float32_t axvCvMix_f;
    zos::float32_t axvCvMixRaw_f;
    zos::float32_t aDtUpperLimitAxvCv_f;
    zos::float32_t aDtLowerLimitAxvCv_f;
    zos::float32_t axvFiltFollowControlPO_f;
    zos::uint8_t ObjectControlType_ub;
    zos::float32_t dDesired_f;
    zos::bool_t torReq_b;
    zos::bool_t DriveOffContinue_b;
    zos::float32_t aApproach1_f;
    zos::float32_t aApproach2_f;
    zos::uint8_t DINisActive_ub;
    zos::bool_t DINLeftControl_b;
    zos::float32_t tAxvPOConstant_f;
    zos::float32_t vLimiter_f;
    zos::float32_t vrDeltaPos_f;
    zos::uint16_t ObjectIDK1Array_ub;
    zos::bool_t vrelLimitActive_b;
    zos::uint8_t distanceLimit_ub;
    zos::uint8_t axvFiltSensObjThres4CutInBrking_ub;
    zos::float32_t axvFiltSensObjThres4CutInBrking_f;
    zos::float32_t dnecerrary_f;
    zos::float32_t RatioDnecDact_f;
    zos::float32_t Pdslowdown_f;
    zos::float32_t PdslowdownFilter_f;
    zos::float32_t PdslowdownBase_f;
    zos::float32_t dActDslowdownLimitMin_f;
    zos::float32_t dActDslowdownLimitMax_f;
    zos::float32_t ToCutIntimercycle_f;
    zos::float32_t aDeltaCutin_f;
    zos::float32_t ttc_f;
    zos::float32_t aFOCCutIn_f;
    zos::float32_t aFOCCutinK1_f;
    zos::float32_t aRampCutin_f;
    zos::uint16_t objIDK1Approach;
    zos::float32_t dImmerseUnlimit_f;
    zos::float32_t fak2uDact2Dset_f;
    zos::float32_t dDesiredPO_f;
    zos::float32_t dSlowDownPO_f;
    zos::float32_t axvFollowPO_f;
    zos::float32_t axvApproachPO_f;
    zos::float32_t vDeltaRawPO_f;
    zos::float32_t vrFilPO_f;
    zos::float32_t dCalcMinPO_f;
    zos::float32_t tFil_f;
    zos::float32_t axvIntegralPO_f;
    zos::float32_t dImmerseComfortPOK1_f;
    zos::float32_t axvFiltFollowControlPOK1_f;
    zos::float32_t tauVPO_f;
    zos::float32_t tReducedCutInReact_f;
    zos::float32_t FOCtauImmerseCritChar_f;
    zos::float32_t vrSensObjFollowFiltered_f;
    zos::float32_t vrSensObjApproachFiltered_f;
    zos::float32_t vDeltaTORConst_f;
    zos::float32_t vDeltaTORAllkow_f;
    zos::float32_t dImmerse_f;
    zos::float32_t distanceLimit_f;
    zos::uint8_t ObjectIDDisLimit_ub;
    zos::uint8_t disLimitEnable_ub;
    zos::float32_t fak2uDact2DsetDistanceLimit_f;
    zos::float32_t dSetNoBrake_f;
    zos::float32_t FOCfak2uDistanceLimitMin_f;
    zos::float32_t FOCfak2uDistanceLimitMax_f;
    zos::float32_t taugapDistanceLimit_f;
    zos::float32_t aapproachOffset_f;
    zos::uint16_t TargetLostTimer_uw;
    zos::float32_t atargetLost_f;
    zos::float32_t bdc_axvCv_f;
    zos::float32_t bdc_aDtUpperLimitAxvCv_f;
    zos::float32_t bdc_aDtLowerLimitAxvCv_f;
    zos::float32_t bdc_dExpansion_f;
    zos::float32_t bdc_dStandstillCv_f;
    zos::bool_t bdc_BrakePreferred_b;
    zos::bool_t bdc_DecToStopReq_b;
    zos::bool_t bdc_tor_b;
    zos::bool_t bdc_active_b;
    zos::bool_t bdc_SafetyStop_b;
    zos::bool_t bdc_StartStopEnable_b;
    zos::float32_t bdc_tBDCactive_f;
    zos::float32_t bdc_tBDCactivePredicted_f;
    zos::float32_t bdc_tFinalStop_f;
    zos::float32_t bdc_dZO_f;
    zos::float32_t bdc_vRef_f;
    zos::float32_t bdc_vSoll_f;
    zos::float32_t bdc_vsoll_gain_f;
    zos::float32_t bdc_vSollStopFilter_f;
    zos::float32_t bdc_vRollStop_f;
    zos::bool_t bdc_vRollStopPhase_b;
    zos::float32_t bdc_vRollStopGain_f;
    zos::bool_t bdc_SrartToUsevSollStopFilter_b;
    zos::float32_t bdc_vSollOrig_f;
    zos::float32_t bdc_aRef_f;
    zos::float32_t bdc_axvRef_f;
    zos::float32_t bdc_xSoll_f;
    zos::float32_t bdc_xSollOrig_f;
    zos::float32_t bdc_aReg_f;
    zos::float32_t bdc_xZOSoll_f;
    zos::float32_t bdc_d0_ZO_f;
    zos::float32_t bdc_d_BDCDesire_f;
    zos::float32_t bdc_ddiff_f;
    zos::float32_t bdc_dres_f;
    zos::float32_t bdc_dresstop_f;
    zos::float32_t bdc_a_max_f;
    zos::float32_t bdc_aSoll_f;
    zos::float32_t bdc_aSollPredicted_f;
    zos::float32_t bdc_aSollMIX_f;
    zos::float32_t bdc_aMax_f;
    zos::float32_t bdc_dSoll_f;
    zos::float32_t bdc_a0_f;
    zos::float32_t bdc_a0Freeze_f;
    zos::float32_t bdc_ada0_f;
    zos::float32_t bdc_ada0Freeze_f;
    zos::float32_t bdc_da_f;
    zos::float32_t bdc_tStop_f;
    zos::float32_t bdc_tRemain_f;
    zos::float32_t bdc_dstop_f;
    zos::float32_t bdc_v0_f;
    zos::float32_t bdc_dZOSoll_f;
    zos::float32_t bdc_vxvZO_f;
    zos::float32_t bdc_axvZO_f;
    zos::float32_t bdc_axvZOpre_f;
    zos::float32_t bdc_TZO_f;
    zos::float32_t bdc_tStopZO_f;
    zos::float32_t bdc_tStopZOpre_f;
    zos::float32_t bdc_tSafetyStop_f;
    zos::float32_t bdc_aStop_f;
    zos::float32_t bdc_vRollCv_f;
    zos::float32_t bdc_aDtStop_f;
    zos::float32_t bdc_axvProfSlope_f;
    zos::int16_t bdc_CALCPROFILEtimer_ub;
    zos::float32_t bdc_tBDCDelayForCutin_f;
    zos::float32_t bdc_aZOFilter_f;
    zos::bool_t bdc_CALCPROFILE_b;
    zos::float32_t bdc_BDCadiffTimerForRecalc;
    zos::float32_t bdc_BDCvdiffTimerForRecalc;
    zos::float32_t bdc_BDCsdiffTimerForRecalc;
    zos::float32_t bdc_accDiff_f;
    zos::float32_t bdc_spdDiff_f;
    zos::float32_t bdc_disDiff_f;
    zos::float32_t bdc_accDiffFilt_f;
    zos::float32_t bdc_spdDiffFilt_f;
    zos::float32_t bdc_disDiffFilt_f;
    zos::float32_t bdc_disPcompen_f;
    zos::float32_t bdc_disIcompen_f;
    zos::float32_t bdc_asollCompenBasedOnspeed_f;
    zos::float32_t bdc_asollCompenBasedOnspeedSum_f;
    zos::float32_t bdc_accDiffCoef_f;
    zos::float32_t bdc_spdDiffCoef_f;
    zos::float32_t bdc_disDiffCoef_f;
    zos::float32_t bdc_disDiffCoefGain_f;
    zos::float32_t bdc_disDiffIntegralCoefGain_f;
    zos::float32_t bdc_spdDiffCoefGain_f;
    zos::float32_t bdc_asollComp_accDiff_f;
    zos::float32_t bdc_asollComp_spdDiff_f;
    zos::float32_t bdc_asollComp_disDiff_f;
    zos::float32_t bdc_BDCsdiffPIcontrolPropotion;
    zos::bool_t bdc_ResetStop_b;
    zos::bool_t bdc_PrepareStop_b;
    zos::bool_t bdc_TOtostand_b;
    zos::bool_t bdc_PreStop_b;
    zos::bool_t bdc_PrepareSafe_b;
    zos::bool_t bdc_Stop_b;
    zos::bool_t bdc_Softstop_b;
    zos::bool_t bdc_dExpTruck_b;
    zos::bool_t bdc_ActivationEnabled_b;
    zos::uint8_t bdc_state_UB;
    zos::uint8_t bdc_dStState_UB;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(LocFocDebugInfo, axvCvComfortBandUpperValue_f, axvCvComfortBandLowerValue_f, axvCv_f, axvCvMix_f, axvCvMixRaw_f, aDtUpperLimitAxvCv_f, aDtLowerLimitAxvCv_f, axvFiltFollowControlPO_f, ObjectControlType_ub, dDesired_f, torReq_b, DriveOffContinue_b, aApproach1_f, aApproach2_f, DINisActive_ub, DINLeftControl_b, tAxvPOConstant_f, vLimiter_f, vrDeltaPos_f, ObjectIDK1Array_ub, vrelLimitActive_b, distanceLimit_ub, axvFiltSensObjThres4CutInBrking_ub, axvFiltSensObjThres4CutInBrking_f, dnecerrary_f, RatioDnecDact_f, Pdslowdown_f, PdslowdownFilter_f, PdslowdownBase_f, dActDslowdownLimitMin_f, dActDslowdownLimitMax_f, ToCutIntimercycle_f, aDeltaCutin_f, ttc_f, aFOCCutIn_f, aFOCCutinK1_f, aRampCutin_f, objIDK1Approach, dImmerseUnlimit_f, fak2uDact2Dset_f, dDesiredPO_f, dSlowDownPO_f, axvFollowPO_f, axvApproachPO_f, vDeltaRawPO_f, vrFilPO_f, dCalcMinPO_f, tFil_f, axvIntegralPO_f, dImmerseComfortPOK1_f, axvFiltFollowControlPOK1_f, tauVPO_f, tReducedCutInReact_f, FOCtauImmerseCritChar_f, vrSensObjFollowFiltered_f, vrSensObjApproachFiltered_f, vDeltaTORConst_f, vDeltaTORAllkow_f, dImmerse_f, distanceLimit_f, ObjectIDDisLimit_ub, disLimitEnable_ub, fak2uDact2DsetDistanceLimit_f, dSetNoBrake_f, FOCfak2uDistanceLimitMin_f, FOCfak2uDistanceLimitMax_f, taugapDistanceLimit_f, aapproachOffset_f, TargetLostTimer_uw, atargetLost_f, bdc_axvCv_f, bdc_aDtUpperLimitAxvCv_f, bdc_aDtLowerLimitAxvCv_f, bdc_dExpansion_f, bdc_dStandstillCv_f, bdc_BrakePreferred_b, bdc_DecToStopReq_b, bdc_tor_b, bdc_active_b, bdc_SafetyStop_b, bdc_StartStopEnable_b, bdc_tBDCactive_f, bdc_tBDCactivePredicted_f, bdc_tFinalStop_f, bdc_dZO_f, bdc_vRef_f, bdc_vSoll_f, bdc_vsoll_gain_f, bdc_vSollStopFilter_f, bdc_vRollStop_f, bdc_vRollStopPhase_b, bdc_vRollStopGain_f, bdc_SrartToUsevSollStopFilter_b, bdc_vSollOrig_f, bdc_aRef_f, bdc_axvRef_f, bdc_xSoll_f, bdc_xSollOrig_f, bdc_aReg_f, bdc_xZOSoll_f, bdc_d0_ZO_f, bdc_d_BDCDesire_f, bdc_ddiff_f, bdc_dres_f, bdc_dresstop_f, bdc_a_max_f, bdc_aSoll_f, bdc_aSollPredicted_f, bdc_aSollMIX_f, bdc_aMax_f, bdc_dSoll_f, bdc_a0_f, bdc_a0Freeze_f, bdc_ada0_f, bdc_ada0Freeze_f, bdc_da_f, bdc_tStop_f, bdc_tRemain_f, bdc_dstop_f, bdc_v0_f, bdc_dZOSoll_f, bdc_vxvZO_f, bdc_axvZO_f, bdc_axvZOpre_f, bdc_TZO_f, bdc_tStopZO_f, bdc_tStopZOpre_f, bdc_tSafetyStop_f, bdc_aStop_f, bdc_vRollCv_f, bdc_aDtStop_f, bdc_axvProfSlope_f, bdc_CALCPROFILEtimer_ub, bdc_tBDCDelayForCutin_f, bdc_aZOFilter_f, bdc_CALCPROFILE_b, bdc_BDCadiffTimerForRecalc, bdc_BDCvdiffTimerForRecalc, bdc_BDCsdiffTimerForRecalc, bdc_accDiff_f, bdc_spdDiff_f, bdc_disDiff_f, bdc_accDiffFilt_f, bdc_spdDiffFilt_f, bdc_disDiffFilt_f, bdc_disPcompen_f, bdc_disIcompen_f, bdc_asollCompenBasedOnspeed_f, bdc_asollCompenBasedOnspeedSum_f, bdc_accDiffCoef_f, bdc_spdDiffCoef_f, bdc_disDiffCoef_f, bdc_disDiffCoefGain_f, bdc_disDiffIntegralCoefGain_f, bdc_spdDiffCoefGain_f, bdc_asollComp_accDiff_f, bdc_asollComp_spdDiff_f, bdc_asollComp_disDiff_f, bdc_BDCsdiffPIcontrolPropotion, bdc_ResetStop_b, bdc_PrepareStop_b, bdc_TOtostand_b, bdc_PreStop_b, bdc_PrepareSafe_b, bdc_Stop_b, bdc_Softstop_b, bdc_dExpTruck_b, bdc_ActivationEnabled_b, bdc_state_UB, bdc_dStState_UB)

struct LocPtcDebugInfo
{
    zos::float32_t last_lon_acc;
    zos::float32_t hold_car_acc;
    zos::float32_t max_jerk;
    zos::float32_t last_acc_output_before_stop;
    zos::float32_t min_ego_acc;
    zos::float32_t min_acc_launch;
    zos::float32_t lon_control_value1;
    zos::float32_t lon_control_value2;
    zos::float32_t min_acc_rate_path;
    zos::float32_t last_actual_jerk_limit;
    zos::float32_t lon_control_upper_jerk;
    zos::float32_t lon_control_lower_jerk;
    zos::float32_t lon_control_upper_comfort_band;
    zos::float32_t lon_control_lower_comfort_band;
    zos::bool_t hold_car_flag;
    zos::bool_t exit_hold_car_flag;
    zos::bool_t flag_former_loop_has_reset;
    zos::bool_t hold_car_flag_hysteresis;
    zos::bool_t exit_hold_car_flag_hysteresis;
    zos::bool_t lon_control_state_reset_flag;
    zos::bool_t pilot_using_smooth_acc_stop_flag;
    zos::bool_t is_first_set_ctrl_in_pilot_mode;
    zos::bool_t pilot_stop_pt_exist_flag;
    zos::bool_t m_timeDiffisOutOffRange_b;
    zos::bool_t m_dectostop_b;
    zos::bool_t m_brakepreffered_b;
    zos::bool_t m_findStopPoint_b;
    zos::bool_t m_autoDriveoff_b;
    zos::bool_t m_confirmDriveoff_b;
    zos::uint16_t start_car_counter;
    zos::uint16_t hold_car_speed_counter;
    zos::uint16_t lon_new_entry_function_state_check_counter;
    zos::uint16_t lon_override_exit_state_check_counter;
    zos::uint16_t stop_point_counter;
    zos::uint16_t parking_point_id_in_pilot_mode;
    zos::float32_t preview_time_control_point_id;
    zos::float32_t dec_preview_time_control_point_id;
    zos::float32_t projection_control_point_id;
    zos::float32_t time_match_control_point_id;
    zos::float32_t kp;
    zos::float32_t ki;
    zos::float32_t kd;
    zos::float32_t max_integral;
    zos::float32_t min_integral;
    zos::float32_t max_output;
    zos::float32_t min_output;
    zos::float32_t integral_separation_threshold;
    zos::float32_t kp_max_output;
    zos::float32_t kp_min_output;
    zos::uint8_t lon_active_mode;
    zos::uint8_t lon_init_mode;
    zos::uint8_t longitudinal_control_state;
    zos::uint8_t lon_control_model;
    zos::float32_t acc_output;
    zos::float32_t acc_launch;
    zos::float32_t acc_feedforward_output;
    zos::float32_t acc_feedback_output;
    zos::float32_t time_matched_speed;
    zos::float32_t target_speed;
    zos::float32_t ego_speed;
    zos::float32_t ego_speed_filter;
    zos::float32_t time_matched_acc;
    zos::float32_t target_acc;
    zos::float32_t ego_acc;
    zos::float32_t ego_acc_filter;
    zos::float32_t target_distance;
    zos::float32_t acc_assist;
    zos::float32_t slope_ave;
    zos::float32_t target_speed_compensated;
    zos::float32_t kp_position_control;
    zos::float32_t brake_distance;
    zos::float32_t acc_stop;
    zos::float32_t hold_acc;
    zos::float32_t deceleration_max_preview_range;
    zos::float32_t dist_to_stop_pt;
    zos::uint32_t is_exist_stop_pt_flag;
    zos::uint32_t lon_ctrl_mode;
    zos::float32_t acc_kp_coeff_based_target_acc;
    zos::float32_t target_acc_with_slope_for_acc_mode;
    zos::float32_t acc_feedforward_output_without_slope;
    zos::float32_t end_path_point_speed;
    zos::uint8_t lon_control_error_code;
    zos::float32_t accPidOutputP_f;
    zos::float32_t accPidOutputI_f;
    zos::float32_t accPidOutputD_f;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(LocPtcDebugInfo, last_lon_acc, hold_car_acc, max_jerk, last_acc_output_before_stop, min_ego_acc, min_acc_launch, lon_control_value1, lon_control_value2, min_acc_rate_path, last_actual_jerk_limit, lon_control_upper_jerk, lon_control_lower_jerk, lon_control_upper_comfort_band, lon_control_lower_comfort_band, hold_car_flag, exit_hold_car_flag, flag_former_loop_has_reset, hold_car_flag_hysteresis, exit_hold_car_flag_hysteresis, lon_control_state_reset_flag, pilot_using_smooth_acc_stop_flag, is_first_set_ctrl_in_pilot_mode, pilot_stop_pt_exist_flag, m_timeDiffisOutOffRange_b, m_dectostop_b, m_brakepreffered_b, m_findStopPoint_b, m_autoDriveoff_b, m_confirmDriveoff_b, start_car_counter, hold_car_speed_counter, lon_new_entry_function_state_check_counter, lon_override_exit_state_check_counter, stop_point_counter, parking_point_id_in_pilot_mode, preview_time_control_point_id, dec_preview_time_control_point_id, projection_control_point_id, time_match_control_point_id, kp, ki, kd, max_integral, min_integral, max_output, min_output, integral_separation_threshold, kp_max_output, kp_min_output, lon_active_mode, lon_init_mode, longitudinal_control_state, lon_control_model, acc_output, acc_launch, acc_feedforward_output, acc_feedback_output, time_matched_speed, target_speed, ego_speed, ego_speed_filter, time_matched_acc, target_acc, ego_acc, ego_acc_filter, target_distance, acc_assist, slope_ave, target_speed_compensated, kp_position_control, brake_distance, acc_stop, hold_acc, deceleration_max_preview_range, dist_to_stop_pt, is_exist_stop_pt_flag, lon_ctrl_mode, acc_kp_coeff_based_target_acc, target_acc_with_slope_for_acc_mode, acc_feedforward_output_without_slope, end_path_point_speed, lon_control_error_code, accPidOutputP_f, accPidOutputI_f, accPidOutputD_f)

struct LocDinDebugInfo
{
    zos::bool_t ComfortAcceleration_b;
    zos::bool_t ComfortDeceleration_b;
    zos::bool_t SuspendDelay_b;
    zos::bool_t ResumeDelay_b;
    zos::float32_t wOvertake_f;
    zos::float32_t DINtauGapSet_f;
    zos::bool_t OvertakeImpossible_b;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(LocDinDebugInfo, ComfortAcceleration_b, ComfortDeceleration_b, SuspendDelay_b, ResumeDelay_b, wOvertake_f, DINtauGapSet_f, OvertakeImpossible_b)

struct LocTorDebugInfo
{
    zos::bool_t ActivePredictDistance_b;
    zos::bool_t ActiveTOR_b;
    zos::float32_t dThreshold_f;
    zos::bool_t m_torSubjective_b;
    zos::uint16_t m_time4TORSubject_uw;
    zos::uint16_t m_timeTOR_uw;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(LocTorDebugInfo, ActivePredictDistance_b, ActiveTOR_b, dThreshold_f, m_torSubjective_b, m_time4TORSubject_uw, m_timeTOR_uw)

struct LocMixDebugInfo
{
    zos::float32_t axvCvAim_f;
    zos::float32_t axvCvComfortBandUpperValue_f;
    zos::float32_t axvCvComfortBandLowerValue_f;
    zos::float32_t aDtUpperLimitAxvCv_f;
    zos::float32_t aDtLowerLimitAxvCv_f;
    zos::uint8_t ModuleInCommand_ub;
    zos::uint8_t TOInComand_ub;
    zos::float32_t FOC2TSCtimercycle_f;
    zos::float32_t FOC2FOCtimercycle_f;
    zos::float32_t Override2Activetimercycle_f;
    zos::float32_t DistanceLimit2Activetimercycle_f;
    zos::float32_t DThreshold2Velocity_f;
    zos::float32_t aOverride_f;
    zos::float32_t aOverrideIntegral_f;
    zos::bool_t distanceLimitSwitch_b;
    zos::float32_t aAftDistanceLimit_f;
    zos::float32_t aAftDistanceLimitIntegral_f;
    zos::bool_t RaiseAxvCvCloseToANoDrvAction_b;
    zos::bool_t EntryNoaControl_b;
    zos::bool_t ExitNoaControl_b;
    zos::float32_t axcvLimit_f;
    zos::float32_t axcvRamp_f;
    zos::float32_t MIXIaOverride2ActiveChar_f;
    zos::float32_t MIXIaDistanceLimit2ActiveChar_f;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(LocMixDebugInfo, axvCvAim_f, axvCvComfortBandUpperValue_f, axvCvComfortBandLowerValue_f, aDtUpperLimitAxvCv_f, aDtLowerLimitAxvCv_f, ModuleInCommand_ub, TOInComand_ub, FOC2TSCtimercycle_f, FOC2FOCtimercycle_f, Override2Activetimercycle_f, DistanceLimit2Activetimercycle_f, DThreshold2Velocity_f, aOverride_f, aOverrideIntegral_f, distanceLimitSwitch_b, aAftDistanceLimit_f, aAftDistanceLimitIntegral_f, RaiseAxvCvCloseToANoDrvAction_b, EntryNoaControl_b, ExitNoaControl_b, axcvLimit_f, axcvRamp_f, MIXIaOverride2ActiveChar_f, MIXIaDistanceLimit2ActiveChar_f)

struct LocIsmDebugInfo
{
    zos::float32_t m_Ism_ObstaclePosX_f;
    zos::uint8_t m_Ism_targetObjectStates_ub;
    zos::uint8_t m_Ism_CIPV_MotionType_ub;
    zos::float32_t m_Ism_ObjectAccelX_f;
    zos::float32_t m_Ism_ObstacleRelVelX_f;
    zos::bool_t m_Ism_TOSCloseLost_b;
    zos::bool_t m_Ism_TOSMiddleLost_b;
    zos::bool_t m_Ism_TOSLongLost_b;
    zos::uint16_t m_Ism_TOSID_ub;
    zos::bool_t m_Ism_TOSChanged_b;
    zos::float32_t m_Ism_dMaxDected_f;
    zos::float32_t m_Ism_wObjCutInProb_f;
    zos::bool_t m_Ism_ObjIsOnOwnLane_b;
    zos::uint8_t m_Ism_ObjectClass_UB;
    zos::bool_t m_Ism_ObjectNotInControl_b;
    zos::uint8_t m_Ism_NOAstate_b;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(LocIsmDebugInfo, m_Ism_ObstaclePosX_f, m_Ism_targetObjectStates_ub, m_Ism_CIPV_MotionType_ub, m_Ism_ObjectAccelX_f, m_Ism_ObstacleRelVelX_f, m_Ism_TOSCloseLost_b, m_Ism_TOSMiddleLost_b, m_Ism_TOSLongLost_b, m_Ism_TOSID_ub, m_Ism_TOSChanged_b, m_Ism_dMaxDected_f, m_Ism_wObjCutInProb_f, m_Ism_ObjIsOnOwnLane_b, m_Ism_ObjectClass_UB, m_Ism_ObjectNotInControl_b, m_Ism_NOAstate_b)

struct LocOutPutDebugInfo
{
    zos::uint8_t m_objControlType;
    zos::float32_t m_axvCvAim;
    zos::float32_t m_axvCvComfortBVLower;
    zos::float32_t m_axvCvComfortBVUpper;
    zos::float32_t m_axvCvDtNegLimit;
    zos::float32_t m_axvCvDtPosLimit;
    zos::float32_t m_axvCvLowerLimit;
    zos::float32_t m_axvCvUpperLimit;
    zos::bool_t m_isAutDrvOffCondFulfilled;
    zos::bool_t m_isBrakePreferred;
    zos::bool_t m_isConfDrvOffCondFulfilled;
    zos::bool_t m_isConsiderData;
    zos::bool_t m_isDecToStopReq;
    zos::bool_t m_isEnObjectDetect;
    zos::bool_t m_isStartStopEnabled;
    zos::bool_t m_isTORSitObjective;
    zos::bool_t m_isTORSitSubjective;
    zos::bool_t m_isUseAUpperBandIfABelowNoDrvAction;
    zos::float32_t m_timeGapActual;
    zos::float32_t m_dDesiredHMI;
    zos::bool_t m_cscActiveState;
    zos::bool_t m_locYeildCutinCar;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(LocOutPutDebugInfo, m_objControlType, m_axvCvAim, m_axvCvComfortBVLower, m_axvCvComfortBVUpper, m_axvCvDtNegLimit, m_axvCvDtPosLimit, m_axvCvLowerLimit, m_axvCvUpperLimit, m_isAutDrvOffCondFulfilled, m_isBrakePreferred, m_isConfDrvOffCondFulfilled, m_isConsiderData, m_isDecToStopReq, m_isEnObjectDetect, m_isStartStopEnabled, m_isTORSitObjective, m_isTORSitSubjective, m_isUseAUpperBandIfABelowNoDrvAction, m_timeGapActual, m_dDesiredHMI, m_cscActiveState, m_locYeildCutinCar)

enum class EObjControlType
{
    LOC_CONTROL_TYPE_NO_OBJECT_CONTROL = 0,
    LOC_CONTROL_TYPE_PARTIAL_OBJECT_CONTROL = 1,
    LOC_CONTROL_TYPE_FULL_OBJECT_CONTROL = 2,
};
struct CDrivingLongitudinalControl
{
    zos::common::Timestamp m_timestamp;
    zos::common::Timestamp m_timeStampVehicleUtcSec;
    zos::float32_t m_axvCvAim;
    zos::float32_t m_axvCvComfortBVLower;
    zos::float32_t m_axvCvComfortBVUpper;
    zos::float32_t m_axvCvDtNegLimit;
    zos::float32_t m_axvCvDtPosLimit;
    zos::float32_t m_axvCvLowerLimit;
    zos::float32_t m_axvCvUpperLimit;
    zos::float32_t m_timeGapActual;
    zos::float32_t m_dDesiredHMI;
    zos::bool_t m_isAutDrvOffCondFulfilled;
    zos::bool_t m_isConfDrvOffCondFulfilled;
    zos::bool_t m_isBrakePreferred;
    zos::bool_t m_isDecToStopReq;
    zos::bool_t m_isEnObjectDetect;
    zos::bool_t m_isTORSitObjective;
    zos::bool_t m_isUseAUpperBandIfABelowNoDrvAction;
    zos::bool_t m_cscActiveState;
    zos::bool_t m_locYeildCutinCar;
    EObjControlType m_objControlType;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CDrivingLongitudinalControl, m_timestamp, m_timeStampVehicleUtcSec, m_axvCvAim, m_axvCvComfortBVLower, m_axvCvComfortBVUpper, m_axvCvDtNegLimit, m_axvCvDtPosLimit, m_axvCvLowerLimit, m_axvCvUpperLimit, m_timeGapActual, m_dDesiredHMI, m_isAutDrvOffCondFulfilled, m_isConfDrvOffCondFulfilled, m_isBrakePreferred, m_isDecToStopReq, m_isEnObjectDetect, m_isTORSitObjective, m_isUseAUpperBandIfABelowNoDrvAction, m_cscActiveState, m_locYeildCutinCar, m_objControlType)

struct CDrivingLongitudinalControlDebugInfo
{
    LocTscDebugInfo m_TscDebugInfo;
    LocCscDebugInfo m_CscDebugInfo;
    LocFocDebugInfo m_FocDebugInfo;
    LocPtcDebugInfo m_PtcDebugInfo;
    LocDinDebugInfo m_DinDebugInfo;
    LocTorDebugInfo m_TorDebugInfo;
    LocMixDebugInfo m_MixDebugInfo;
    LocIsmDebugInfo m_IsmDebugInfo;
    LocOutPutDebugInfo m_LocOutputDebugInfo;
    zos::uint32_t m_locFaultInfo;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CDrivingLongitudinalControlDebugInfo, m_TscDebugInfo, m_CscDebugInfo, m_FocDebugInfo, m_PtcDebugInfo, m_DinDebugInfo, m_TorDebugInfo, m_MixDebugInfo, m_IsmDebugInfo, m_LocOutputDebugInfo, m_locFaultInfo)

}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::driving::LocTscDebugInfo, StdLayoutTag>
{
    using ValueType = ::zos::driving::LocTscDebugInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::LocTscDebugInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::LocTscDebugInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::LocCscDebugInfo, StdLayoutTag>
{
    using ValueType = ::zos::driving::LocCscDebugInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::LocCscDebugInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::LocCscDebugInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::LocFocDebugInfo, StdLayoutTag>
{
    using ValueType = ::zos::driving::LocFocDebugInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::LocFocDebugInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::LocFocDebugInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::LocPtcDebugInfo, StdLayoutTag>
{
    using ValueType = ::zos::driving::LocPtcDebugInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::LocPtcDebugInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::LocPtcDebugInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::LocDinDebugInfo, StdLayoutTag>
{
    using ValueType = ::zos::driving::LocDinDebugInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::LocDinDebugInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::LocDinDebugInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::LocTorDebugInfo, StdLayoutTag>
{
    using ValueType = ::zos::driving::LocTorDebugInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::LocTorDebugInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::LocTorDebugInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::LocMixDebugInfo, StdLayoutTag>
{
    using ValueType = ::zos::driving::LocMixDebugInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::LocMixDebugInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::LocMixDebugInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::LocIsmDebugInfo, StdLayoutTag>
{
    using ValueType = ::zos::driving::LocIsmDebugInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::LocIsmDebugInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::LocIsmDebugInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::LocOutPutDebugInfo, StdLayoutTag>
{
    using ValueType = ::zos::driving::LocOutPutDebugInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::LocOutPutDebugInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::LocOutPutDebugInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CDrivingLongitudinalControl, StdLayoutTag>
{
    using ValueType = ::zos::driving::CDrivingLongitudinalControl;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CDrivingLongitudinalControl, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::CDrivingLongitudinalControl;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CDrivingLongitudinalControlDebugInfo, StdLayoutTag>
{
    using ValueType = ::zos::driving::CDrivingLongitudinalControlDebugInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CDrivingLongitudinalControlDebugInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::CDrivingLongitudinalControlDebugInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
