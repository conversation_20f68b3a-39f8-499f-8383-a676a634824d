#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>

#include <zos_msg/common/common.hpp>
#include <zos_msg/common/timestamp.hpp>
#include <zos_msg/numerics/vector.hpp>
namespace zos
{
namespace driving
{
struct LacPidDebugInfo
{
    zos::float32_t TargetCtrlBefFilt_f;
    zos::float32_t TargetCtrlAftFilt_f;
    zos::float32_t TargetCtrlBefSlope_f;
    zos::float32_t TargetCtrlAftSlope_f;
    zos::float32_t TargetSlopeBaseOnRadius_f;
    zos::float32_t DistPredFar_f;
    zos::float32_t DistPredFfw_f;
    zos::float32_t DistPredNear_f;
    zos::float32_t HeadAngleDiffFar_f;
    zos::float32_t HeadAngleDiffRate_f;
    zos::float32_t curvaturePred_f;
    zos::float32_t radiusPred_f;
    zos::float32_t relDerCur_f;
    zos::float32_t dyNearCtrl_f;
    zos::float32_t Filter4Radius_f;
    zos::bool_t CurveFilterFlag_b;
    zos::float32_t dYVehCenterDesTrjPred_f;
    zos::float32_t kapCurvaturePred_f;
    zos::float32_t dyDtLp_f;
    zos::float32_t dyDt_dlcL_f;
    zos::float32_t dyDt_dlcR_f;
    zos::float32_t HandTrqFactor_f;
    zos::float32_t farRadiusFactorKp_f;
    zos::float32_t TargetFarCtrl_f;
    zos::uint8_t IntegralHumanState_ub;
    zos::uint8_t IntegralFuncActiveState_ub;
    zos::float32_t NearILimit_f;
    zos::float32_t TargetNearCtrl_f;
    zos::float32_t TargetPsiCtrl_f;
    zos::float32_t Dist2ClearLimitI_f;
    zos::bool_t DistClearStart_b;
    zos::bool_t ALCRealAbortFlag_b;
    zos::bool_t ALCRealAbortPredTimeDealFlag_b;
    zos::bool_t ALCTrajFfwChangeToCenterFlag_b;
    zos::bool_t ALCTrajFarChangeToCenterFlag_b;
    zos::bool_t AlcQuinticPolyPreviewDealFlag_b;
    zos::bool_t ALCFinishFastFollowFlag_b;
    zos::float32_t runTime_f;
    zos::float32_t TargetControl_f;
    zos::float32_t LKSHeadAngDiff_f;
    zos::float32_t TorqueFactor_f;
    zos::float32_t farP_f;
    zos::float32_t farI_f;
    zos::float32_t farD_f;
    zos::float32_t farKp_f;
    zos::float32_t farKi_f;
    zos::float32_t farKd_f;
    zos::float32_t nearP_f;
    zos::float32_t nearI_f;
    zos::float32_t nearD_f;
    zos::float32_t nearKp_f;
    zos::float32_t nearKi_f;
    zos::float32_t nearKd_f;
    zos::bool_t ControlIsActive_b;
    zos::bool_t FuncTrigger_b;
    zos::bool_t plaTrajTypeIsTransition_b;
    zos::uint8_t alcState_en;
    zos::float32_t AngleOffset_f;
    zos::float32_t strWhlAngMean_deg_f;
    zos::float32_t yawRate_f;
    zos::float32_t actualC0_f;
    zos::float32_t GainYawRate_f;
    zos::uint8_t yawRateMode_ub;
    zos::float32_t yawRateGain4Swa_f;
    zos::bool_t LACElkReTrigger_b;
    zos::float32_t TargetOffset_f;
    zos::float32_t TargetOffsetRead_f;
    zos::float32_t TargetOffsetBFSlope_f;
    zos::float32_t y0Mean_f;
    zos::float32_t SWAMean_f;
    zos::bool_t SWAIsFit_b;
    zos::uint8_t TOState_ub;
    zos::bool_t Y0StdIsFit_b;
    zos::float32_t TOStateTimerSum_f;
    zos::bool_t TFSpeedNotSuit_b;
    zos::bool_t LKSTJATrigger_b;
    zos::bool_t LDPTrigger_b;
    zos::bool_t ELKTrigger_b;
    zos::bool_t Rampup_b;
    zos::bool_t Rampdown_b;
    zos::bool_t UpFlag_b;
    zos::bool_t DownFlag_b;
    zos::bool_t ALCFinishDeal_b;
    zos::bool_t ALCAbortStartDeal_b;
    zos::bool_t SpdTooLowFlag_b;
    zos::bool_t DriveOffFlag_b;
    zos::bool_t LowSpdKeepAngleFlag_b;
    zos::bool_t LowSpdQuitSlopeLimitFlag_b;
    zos::float32_t Torque4KeepAngle_f;
    zos::float32_t AngleTarget_f;
    zos::float32_t AngleError_f;
    zos::bool_t Control4Crossing_b;
    zos::float32_t MeanAngle_f;
    zos::float32_t DLC_f;
    zos::float32_t ELKDLCGain_f;
    zos::float32_t LDPDLCGain_f;
    zos::float32_t TargetDLCCtrl_f;
    zos::float32_t TargetDLCDtCtrl_f;
    zos::float32_t targetLimitByOffsetBefore_f;
    zos::float32_t targetLimitByOffsetAfter_f;
    zos::bool_t targetLimitByOffsetFlag_b;
    zos::bool_t lowHandTorque_b;
    zos::bool_t directionSame_b;
    zos::bool_t lowRadius_b;
    zos::float32_t reqDiffToRealAngle_f;
    zos::float32_t TargetFWSloped_f;
    zos::float32_t desiredAy_f;
    zos::float32_t kFeedFwd_f;
    zos::float32_t TargetFeedFwdCtrl_f;
    zos::float32_t ALCFinishFFwSlope_f;
    zos::bool_t ALCTorqueFastIncFlag_b;
    zos::float32_t TargetMaxRate_f;
    zos::float32_t SwitchRate_f;
    zos::uint8_t State4SlopeLimit_en;
    zos::float32_t TimeAfterChange_f;
    zos::float32_t predTime4Ffw_f;
    zos::bool_t TargetControlRequest_b;
    zos::uint8_t controlMode_en;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(LacPidDebugInfo, TargetCtrlBefFilt_f, TargetCtrlAftFilt_f, TargetCtrlBefSlope_f, TargetCtrlAftSlope_f, TargetSlopeBaseOnRadius_f, DistPredFar_f, DistPredFfw_f, DistPredNear_f, HeadAngleDiffFar_f, HeadAngleDiffRate_f, curvaturePred_f, radiusPred_f, relDerCur_f, dyNearCtrl_f, Filter4Radius_f, CurveFilterFlag_b, dYVehCenterDesTrjPred_f, kapCurvaturePred_f, dyDtLp_f, dyDt_dlcL_f, dyDt_dlcR_f, HandTrqFactor_f, farRadiusFactorKp_f, TargetFarCtrl_f, IntegralHumanState_ub, IntegralFuncActiveState_ub, NearILimit_f, TargetNearCtrl_f, TargetPsiCtrl_f, Dist2ClearLimitI_f, DistClearStart_b, ALCRealAbortFlag_b, ALCRealAbortPredTimeDealFlag_b, ALCTrajFfwChangeToCenterFlag_b, ALCTrajFarChangeToCenterFlag_b, AlcQuinticPolyPreviewDealFlag_b, ALCFinishFastFollowFlag_b, runTime_f, TargetControl_f, LKSHeadAngDiff_f, TorqueFactor_f, farP_f, farI_f, farD_f, farKp_f, farKi_f, farKd_f, nearP_f, nearI_f, nearD_f, nearKp_f, nearKi_f, nearKd_f, ControlIsActive_b, FuncTrigger_b, plaTrajTypeIsTransition_b, alcState_en, AngleOffset_f, strWhlAngMean_deg_f, yawRate_f, actualC0_f, GainYawRate_f, yawRateMode_ub, yawRateGain4Swa_f, LACElkReTrigger_b, TargetOffset_f, TargetOffsetRead_f, TargetOffsetBFSlope_f, y0Mean_f, SWAMean_f, SWAIsFit_b, TOState_ub, Y0StdIsFit_b, TOStateTimerSum_f, TFSpeedNotSuit_b, LKSTJATrigger_b, LDPTrigger_b, ELKTrigger_b, Rampup_b, Rampdown_b, UpFlag_b, DownFlag_b, ALCFinishDeal_b, ALCAbortStartDeal_b, SpdTooLowFlag_b, DriveOffFlag_b, LowSpdKeepAngleFlag_b, LowSpdQuitSlopeLimitFlag_b, Torque4KeepAngle_f, AngleTarget_f, AngleError_f, Control4Crossing_b, MeanAngle_f, DLC_f, ELKDLCGain_f, LDPDLCGain_f, TargetDLCCtrl_f, TargetDLCDtCtrl_f, targetLimitByOffsetBefore_f, targetLimitByOffsetAfter_f, targetLimitByOffsetFlag_b, lowHandTorque_b, directionSame_b, lowRadius_b, reqDiffToRealAngle_f, TargetFWSloped_f, desiredAy_f, kFeedFwd_f, TargetFeedFwdCtrl_f, ALCFinishFFwSlope_f, ALCTorqueFastIncFlag_b, TargetMaxRate_f, SwitchRate_f, State4SlopeLimit_en, TimeAfterChange_f, predTime4Ffw_f, TargetControlRequest_b, controlMode_en)

struct LacLqrDebugInfo
{
    zos::float32_t TargetCtrlBefFilt_f;
    zos::float32_t TargetCtrlAftFilt_f;
    zos::float32_t TargetCtrlBefSlope_f;
    zos::float32_t TargetCtrlAftSlope_f;
    zos::float32_t TargetSlopeBaseOnRadius_f;
    zos::float32_t DistPredFfw_f;
    zos::float32_t DistPredNear_f;
    zos::float32_t curvaturePred_f;
    zos::float32_t radiusPred_f;
    zos::float32_t relDerCur_f;
    zos::float32_t dyNearCtrl_f;
    zos::float32_t Filter4Radius_f;
    zos::bool_t CurveFilterFlag_b;
    zos::float32_t kapCurvaturePred_f;
    zos::float32_t dyDt_dlcL_f;
    zos::float32_t dyDt_dlcR_f;
    zos::uint8_t IntegralHumanState_ub;
    zos::uint8_t IntegralFuncActiveState_ub;
    zos::float32_t Dist2ClearLimitI_f;
    zos::bool_t DistClearStart_b;
    zos::bool_t ALCRealAbortFlag_b;
    zos::bool_t ALCRealAbortPredTimeDealFlag_b;
    zos::bool_t ALCTrajFfwChangeToCenterFlag_b;
    zos::bool_t AlcQuinticPolyPreviewDealFlag_b;
    zos::bool_t ALCFinishFastFollowFlag_b;
    zos::float32_t runTime_f;
    zos::float32_t TargetNear_I_f;
    zos::float32_t TargetMPCCtrl_f;
    zos::float32_t TargetNearCtrl_f;
    zos::float32_t TargetPointPsiDtNear_f;
    zos::float32_t latError_f;
    zos::float32_t latErrorRate_f;
    zos::float32_t headError_f;
    zos::float32_t headErrorRate_f;
    zos::float32_t latErrorFilt_f;
    zos::float32_t latErrorRateFilt_f;
    zos::float32_t headErrorFilt_f;
    zos::float32_t headErrorRateFilt_f;
    zos::float32_t TargetNear_e1_f;
    zos::float32_t TargetNear_de1_f;
    zos::float32_t TargetNear_e2_f;
    zos::float32_t TargetNear_de2_f;
    zos::float32_t LqrKiNear_f;
    zos::uint8_t frontClosestIdx_ub;
    zos::float32_t pt_proj_x;
    zos::float32_t pt_proj_y;
    zos::float32_t pt_proj_theta;
    zos::float32_t pt_proj_curvature;
    zos::float32_t pt_proj_speed;
    zos::uint8_t pt_proj_endIndex_ub;
    zos::float32_t pt_fb_x;
    zos::float32_t pt_fb_y;
    zos::float32_t pt_fb_theta;
    zos::float32_t pt_fb_curvature;
    zos::float32_t pt_fb_speed;
    zos::uint8_t pt_fb_endIndex_ub;
    zos::float32_t pt_ff_x;
    zos::float32_t pt_ff_y;
    zos::float32_t pt_ff_theta;
    zos::float32_t pt_ff_curvature;
    zos::float32_t pt_ff_speed;
    zos::uint8_t pt_ff_endIndex_ub;
    zos::float32_t spdRadiusFactor4RNear_f;
    zos::float32_t NearSpdRadiusFactor4K0_f;
    zos::float32_t NearSpdRadiusFactor4K2_f;
    zos::float32_t TargetControl_f;
    zos::float32_t LKSHeadAngDiff_f;
    zos::float32_t TorqueFactor_f;
    zos::numerics::Vector4f matrix_KNear;
    zos::numerics::Vector4f matrix_KtNear;
    zos::bool_t ControlIsActive_b;
    zos::bool_t FuncTrigger_b;
    zos::bool_t plaTrajTypeIsTransition_b;
    zos::uint8_t alcState_en;
    zos::float32_t AngleOffset_f;
    zos::float32_t strWhlAngMean_deg_f;
    zos::float32_t yawRate_f;
    zos::float32_t actualC0_f;
    zos::float32_t GainYawRate_f;
    zos::uint8_t yawRateMode_ub;
    zos::float32_t yawRateGain4Swa_f;
    zos::bool_t LACElkReTrigger_b;
    zos::float32_t TargetOffset_f;
    zos::float32_t TargetOffsetRead_f;
    zos::float32_t TargetOffsetBFSlope_f;
    zos::float32_t y0Mean_f;
    zos::float32_t SWAMean_f;
    zos::bool_t SWAIsFit_b;
    zos::uint8_t TOState_ub;
    zos::bool_t Y0StdIsFit_b;
    zos::float32_t TOStateTimerSum_f;
    zos::bool_t TFSpeedNotSuit_b;
    zos::bool_t LKSTJATrigger_b;
    zos::bool_t LDPTrigger_b;
    zos::bool_t ELKTrigger_b;
    zos::bool_t Rampup_b;
    zos::bool_t Rampdown_b;
    zos::bool_t UpFlag_b;
    zos::bool_t DownFlag_b;
    zos::bool_t ALCFinishDeal_b;
    zos::bool_t ALCAbortStartDeal_b;
    zos::bool_t SpdTooLowFlag_b;
    zos::bool_t DriveOffFlag_b;
    zos::bool_t LowSpdKeepAngleFlag_b;
    zos::bool_t LowSpdQuitSlopeLimitFlag_b;
    zos::float32_t Torque4KeepAngle_f;
    zos::float32_t AngleTarget_f;
    zos::float32_t AngleError_f;
    zos::bool_t Control4Crossing_b;
    zos::float32_t MeanAngle_f;
    zos::float32_t DLC_f;
    zos::float32_t ELKDLCGain_f;
    zos::float32_t LDPDLCGain_f;
    zos::float32_t TargetDLCCtrl_f;
    zos::float32_t TargetDLCDtCtrl_f;
    zos::float32_t targetLimitByOffsetBefore_f;
    zos::float32_t targetLimitByOffsetAfter_f;
    zos::bool_t targetLimitByOffsetFlag_b;
    zos::bool_t lowHandTorque_b;
    zos::bool_t directionSame_b;
    zos::bool_t lowRadius_b;
    zos::float32_t reqDiffToRealAngle_f;
    zos::float32_t TargetFWSloped_f;
    zos::float32_t desiredAy_f;
    zos::float32_t kFeedFwd_f;
    zos::float32_t TargetFeedFwdCtrl_f;
    zos::float32_t ALCFinishFFwSlope_f;
    zos::bool_t ALCTorqueFastIncFlag_b;
    zos::float32_t TargetMaxRate_f;
    zos::float32_t SwitchRate_f;
    zos::uint8_t State4SlopeLimit_en;
    zos::float32_t TimeAfterChange_f;
    zos::float32_t predTime4Ffw_f;
    zos::bool_t TargetControlRequest_b;
    zos::uint8_t controlMode_en;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(LacLqrDebugInfo, TargetCtrlBefFilt_f, TargetCtrlAftFilt_f, TargetCtrlBefSlope_f, TargetCtrlAftSlope_f, TargetSlopeBaseOnRadius_f, DistPredFfw_f, DistPredNear_f, curvaturePred_f, radiusPred_f, relDerCur_f, dyNearCtrl_f, Filter4Radius_f, CurveFilterFlag_b, kapCurvaturePred_f, dyDt_dlcL_f, dyDt_dlcR_f, IntegralHumanState_ub, IntegralFuncActiveState_ub, Dist2ClearLimitI_f, DistClearStart_b, ALCRealAbortFlag_b, ALCRealAbortPredTimeDealFlag_b, ALCTrajFfwChangeToCenterFlag_b, AlcQuinticPolyPreviewDealFlag_b, ALCFinishFastFollowFlag_b, runTime_f, TargetNear_I_f, TargetMPCCtrl_f, TargetNearCtrl_f, TargetPointPsiDtNear_f, latError_f, latErrorRate_f, headError_f, headErrorRate_f, latErrorFilt_f, latErrorRateFilt_f, headErrorFilt_f, headErrorRateFilt_f, TargetNear_e1_f, TargetNear_de1_f, TargetNear_e2_f, TargetNear_de2_f, LqrKiNear_f, frontClosestIdx_ub, pt_proj_x, pt_proj_y, pt_proj_theta, pt_proj_curvature, pt_proj_speed, pt_proj_endIndex_ub, pt_fb_x, pt_fb_y, pt_fb_theta, pt_fb_curvature, pt_fb_speed, pt_fb_endIndex_ub, pt_ff_x, pt_ff_y, pt_ff_theta, pt_ff_curvature, pt_ff_speed, pt_ff_endIndex_ub, spdRadiusFactor4RNear_f, NearSpdRadiusFactor4K0_f, NearSpdRadiusFactor4K2_f, TargetControl_f, LKSHeadAngDiff_f, TorqueFactor_f, matrix_KNear, matrix_KtNear, ControlIsActive_b, FuncTrigger_b, plaTrajTypeIsTransition_b, alcState_en, AngleOffset_f, strWhlAngMean_deg_f, yawRate_f, actualC0_f, GainYawRate_f, yawRateMode_ub, yawRateGain4Swa_f, LACElkReTrigger_b, TargetOffset_f, TargetOffsetRead_f, TargetOffsetBFSlope_f, y0Mean_f, SWAMean_f, SWAIsFit_b, TOState_ub, Y0StdIsFit_b, TOStateTimerSum_f, TFSpeedNotSuit_b, LKSTJATrigger_b, LDPTrigger_b, ELKTrigger_b, Rampup_b, Rampdown_b, UpFlag_b, DownFlag_b, ALCFinishDeal_b, ALCAbortStartDeal_b, SpdTooLowFlag_b, DriveOffFlag_b, LowSpdKeepAngleFlag_b, LowSpdQuitSlopeLimitFlag_b, Torque4KeepAngle_f, AngleTarget_f, AngleError_f, Control4Crossing_b, MeanAngle_f, DLC_f, ELKDLCGain_f, LDPDLCGain_f, TargetDLCCtrl_f, TargetDLCDtCtrl_f, targetLimitByOffsetBefore_f, targetLimitByOffsetAfter_f, targetLimitByOffsetFlag_b, lowHandTorque_b, directionSame_b, lowRadius_b, reqDiffToRealAngle_f, TargetFWSloped_f, desiredAy_f, kFeedFwd_f, TargetFeedFwdCtrl_f, ALCFinishFFwSlope_f, ALCTorqueFastIncFlag_b, TargetMaxRate_f, SwitchRate_f, State4SlopeLimit_en, TimeAfterChange_f, predTime4Ffw_f, TargetControlRequest_b, controlMode_en)

struct LacMpcDebugInfo
{
    zos::float32_t TargetCtrlBefFilt_f;
    zos::float32_t TargetCtrlAftFilt_f;
    zos::float32_t TargetCtrlBefSlope_f;
    zos::float32_t TargetCtrlAftSlope_f;
    zos::float32_t TargetSlopeBaseOnRadius_f;
    zos::float32_t DistPredFfw_f;
    zos::float32_t DistPredNear_f;
    zos::float32_t curvaturePred_f;
    zos::float32_t radiusPred_f;
    zos::float32_t relDerCur_f;
    zos::float32_t dyNearCtrl_f;
    zos::float32_t Filter4Radius_f;
    zos::bool_t CurveFilterFlag_b;
    zos::float32_t kapCurvaturePred_f;
    zos::float32_t dyDt_dlcL_f;
    zos::float32_t dyDt_dlcR_f;
    zos::uint8_t IntegralHumanState_ub;
    zos::uint8_t IntegralFuncActiveState_ub;
    zos::float32_t Dist2ClearLimitI_f;
    zos::bool_t DistClearStart_b;
    zos::bool_t ALCRealAbortFlag_b;
    zos::bool_t ALCRealAbortPredTimeDealFlag_b;
    zos::bool_t ALCTrajFfwChangeToCenterFlag_b;
    zos::bool_t AlcQuinticPolyPreviewDealFlag_b;
    zos::bool_t ALCFinishFastFollowFlag_b;
    zos::float32_t runTime_f;
    zos::float32_t TargetNear_I_f;
    zos::float32_t TargetMPCCtrl_f;
    zos::float32_t TargetNearCtrl_f;
    zos::float32_t TargetPointPsiDtNear_f;
    zos::float32_t latError_f;
    zos::float32_t latErrorRate_f;
    zos::float32_t headError_f;
    zos::float32_t headErrorRate_f;
    zos::float32_t latErrorFilt_f;
    zos::float32_t latErrorRateFilt_f;
    zos::float32_t headErrorFilt_f;
    zos::float32_t headErrorRateFilt_f;
    zos::float32_t mpcKiNear_f;
    zos::uint8_t frontClosestIdx_ub;
    zos::float32_t pt_proj_x;
    zos::float32_t pt_proj_y;
    zos::float32_t pt_proj_theta;
    zos::float32_t pt_proj_curvature;
    zos::float32_t pt_proj_speed;
    zos::uint8_t pt_proj_endIndex_ub;
    zos::float32_t pt_fb_x;
    zos::float32_t pt_fb_y;
    zos::float32_t pt_fb_theta;
    zos::float32_t pt_fb_curvature;
    zos::float32_t pt_fb_speed;
    zos::uint8_t pt_fb_endIndex_ub;
    zos::float32_t pt_ff_x;
    zos::float32_t pt_ff_y;
    zos::float32_t pt_ff_theta;
    zos::float32_t pt_ff_curvature;
    zos::float32_t pt_ff_speed;
    zos::uint8_t pt_ff_endIndex_ub;
    zos::float32_t lat_err_factor_f;
    zos::float32_t head_err_factor_f;
    zos::float32_t control_factor_f;
    zos::float32_t TargetControl_f;
    zos::float32_t LKSHeadAngDiff_f;
    zos::float32_t TorqueFactor_f;
    zos::bool_t ControlIsActive_b;
    zos::bool_t FuncTrigger_b;
    zos::bool_t plaTrajTypeIsTransition_b;
    zos::uint8_t alcState_en;
    zos::float32_t AngleOffset_f;
    zos::float32_t strWhlAngMean_deg_f;
    zos::float32_t yawRate_f;
    zos::float32_t actualC0_f;
    zos::float32_t GainYawRate_f;
    zos::uint8_t yawRateMode_ub;
    zos::float32_t yawRateGain4Swa_f;
    zos::bool_t LACElkReTrigger_b;
    zos::float32_t TargetOffset_f;
    zos::float32_t TargetOffsetRead_f;
    zos::float32_t TargetOffsetBFSlope_f;
    zos::float32_t y0Mean_f;
    zos::float32_t SWAMean_f;
    zos::bool_t SWAIsFit_b;
    zos::uint8_t TOState_ub;
    zos::bool_t Y0StdIsFit_b;
    zos::float32_t TOStateTimerSum_f;
    zos::bool_t TFSpeedNotSuit_b;
    zos::bool_t LKSTJATrigger_b;
    zos::bool_t LDPTrigger_b;
    zos::bool_t ELKTrigger_b;
    zos::bool_t Rampup_b;
    zos::bool_t Rampdown_b;
    zos::bool_t UpFlag_b;
    zos::bool_t DownFlag_b;
    zos::bool_t ALCFinishDeal_b;
    zos::bool_t ALCAbortStartDeal_b;
    zos::bool_t SpdTooLowFlag_b;
    zos::bool_t DriveOffFlag_b;
    zos::bool_t LowSpdKeepAngleFlag_b;
    zos::bool_t LowSpdQuitSlopeLimitFlag_b;
    zos::float32_t Torque4KeepAngle_f;
    zos::float32_t AngleTarget_f;
    zos::float32_t AngleError_f;
    zos::bool_t Control4Crossing_b;
    zos::float32_t MeanAngle_f;
    zos::float32_t DLC_f;
    zos::float32_t ELKDLCGain_f;
    zos::float32_t LDPDLCGain_f;
    zos::float32_t TargetDLCCtrl_f;
    zos::float32_t TargetDLCDtCtrl_f;
    zos::float32_t targetLimitByOffsetBefore_f;
    zos::float32_t targetLimitByOffsetAfter_f;
    zos::bool_t targetLimitByOffsetFlag_b;
    zos::bool_t lowHandTorque_b;
    zos::bool_t directionSame_b;
    zos::bool_t lowRadius_b;
    zos::float32_t reqDiffToRealAngle_f;
    zos::float32_t TargetFWSloped_f;
    zos::float32_t desiredAy_f;
    zos::float32_t kFeedFwd_f;
    zos::float32_t TargetFeedFwdCtrl_f;
    zos::float32_t ALCFinishFFwSlope_f;
    zos::bool_t ALCTorqueFastIncFlag_b;
    zos::float32_t TargetMaxRate_f;
    zos::float32_t SwitchRate_f;
    zos::uint8_t State4SlopeLimit_en;
    zos::float32_t TimeAfterChange_f;
    zos::float32_t predTime4Ffw_f;
    zos::bool_t TargetControlRequest_b;
    zos::uint8_t controlMode_en;
    zos::bool_t noaNewEntry_b;
    zos::uint32_t faultInfo_ub;
    zos::float32_t xOdometry_f;
    zos::float32_t yOdometry_f;
    zos::float32_t thetaOdometry_f;
    zos::float32_t latErrWeight_f;
    zos::float32_t headErrWeight_f;
    zos::uint8_t noaState_ub;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(LacMpcDebugInfo, TargetCtrlBefFilt_f, TargetCtrlAftFilt_f, TargetCtrlBefSlope_f, TargetCtrlAftSlope_f, TargetSlopeBaseOnRadius_f, DistPredFfw_f, DistPredNear_f, curvaturePred_f, radiusPred_f, relDerCur_f, dyNearCtrl_f, Filter4Radius_f, CurveFilterFlag_b, kapCurvaturePred_f, dyDt_dlcL_f, dyDt_dlcR_f, IntegralHumanState_ub, IntegralFuncActiveState_ub, Dist2ClearLimitI_f, DistClearStart_b, ALCRealAbortFlag_b, ALCRealAbortPredTimeDealFlag_b, ALCTrajFfwChangeToCenterFlag_b, AlcQuinticPolyPreviewDealFlag_b, ALCFinishFastFollowFlag_b, runTime_f, TargetNear_I_f, TargetMPCCtrl_f, TargetNearCtrl_f, TargetPointPsiDtNear_f, latError_f, latErrorRate_f, headError_f, headErrorRate_f, latErrorFilt_f, latErrorRateFilt_f, headErrorFilt_f, headErrorRateFilt_f, mpcKiNear_f, frontClosestIdx_ub, pt_proj_x, pt_proj_y, pt_proj_theta, pt_proj_curvature, pt_proj_speed, pt_proj_endIndex_ub, pt_fb_x, pt_fb_y, pt_fb_theta, pt_fb_curvature, pt_fb_speed, pt_fb_endIndex_ub, pt_ff_x, pt_ff_y, pt_ff_theta, pt_ff_curvature, pt_ff_speed, pt_ff_endIndex_ub, lat_err_factor_f, head_err_factor_f, control_factor_f, TargetControl_f, LKSHeadAngDiff_f, TorqueFactor_f, ControlIsActive_b, FuncTrigger_b, plaTrajTypeIsTransition_b, alcState_en, AngleOffset_f, strWhlAngMean_deg_f, yawRate_f, actualC0_f, GainYawRate_f, yawRateMode_ub, yawRateGain4Swa_f, LACElkReTrigger_b, TargetOffset_f, TargetOffsetRead_f, TargetOffsetBFSlope_f, y0Mean_f, SWAMean_f, SWAIsFit_b, TOState_ub, Y0StdIsFit_b, TOStateTimerSum_f, TFSpeedNotSuit_b, LKSTJATrigger_b, LDPTrigger_b, ELKTrigger_b, Rampup_b, Rampdown_b, UpFlag_b, DownFlag_b, ALCFinishDeal_b, ALCAbortStartDeal_b, SpdTooLowFlag_b, DriveOffFlag_b, LowSpdKeepAngleFlag_b, LowSpdQuitSlopeLimitFlag_b, Torque4KeepAngle_f, AngleTarget_f, AngleError_f, Control4Crossing_b, MeanAngle_f, DLC_f, ELKDLCGain_f, LDPDLCGain_f, TargetDLCCtrl_f, TargetDLCDtCtrl_f, targetLimitByOffsetBefore_f, targetLimitByOffsetAfter_f, targetLimitByOffsetFlag_b, lowHandTorque_b, directionSame_b, lowRadius_b, reqDiffToRealAngle_f, TargetFWSloped_f, desiredAy_f, kFeedFwd_f, TargetFeedFwdCtrl_f, ALCFinishFFwSlope_f, ALCTorqueFastIncFlag_b, TargetMaxRate_f, SwitchRate_f, State4SlopeLimit_en, TimeAfterChange_f, predTime4Ffw_f, TargetControlRequest_b, controlMode_en, noaNewEntry_b, faultInfo_ub, xOdometry_f, yOdometry_f, thetaOdometry_f, latErrWeight_f, headErrWeight_f, noaState_ub)

struct CDrivingLateralControl
{
    zos::common::Timestamp m_timestamp;
    zos::common::Timestamp m_timeStampVehicleUtcSec;
    zos::bool_t m_targetControlRequest_b;
    zos::float32_t m_targetControl;
    zos::float32_t m_targetFactor;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CDrivingLateralControl, m_timestamp, m_timeStampVehicleUtcSec, m_targetControlRequest_b, m_targetControl, m_targetFactor)

struct CDrivingLateralControlDebugInfo
{
    LacPidDebugInfo m_pidDebugInfo;
    LacLqrDebugInfo m_lqrDebugInfo;
    LacMpcDebugInfo m_mpcDebugInfo;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CDrivingLateralControlDebugInfo, m_pidDebugInfo, m_lqrDebugInfo, m_mpcDebugInfo)

}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::driving::LacPidDebugInfo, StdLayoutTag>
{
    using ValueType = ::zos::driving::LacPidDebugInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::LacPidDebugInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::LacPidDebugInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::LacLqrDebugInfo, StdLayoutTag>
{
    using ValueType = ::zos::driving::LacLqrDebugInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::LacLqrDebugInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::LacLqrDebugInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::LacMpcDebugInfo, StdLayoutTag>
{
    using ValueType = ::zos::driving::LacMpcDebugInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::LacMpcDebugInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::LacMpcDebugInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CDrivingLateralControl, StdLayoutTag>
{
    using ValueType = ::zos::driving::CDrivingLateralControl;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CDrivingLateralControl, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::CDrivingLateralControl;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CDrivingLateralControlDebugInfo, StdLayoutTag>
{
    using ValueType = ::zos::driving::CDrivingLateralControlDebugInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::CDrivingLateralControlDebugInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::CDrivingLateralControlDebugInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
