#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>

namespace zos
{
namespace driving
{
namespace as
{
struct CHostVehicleParameters2
{
    zos::float32_t dxvMountPosAxSensor;
    zos::int16_t dVehicleWidthMin_sw;
    zos::float32_t dVehicleWidthMax_sw;
    zos::float32_t dxvFrontBumper_sw;
    zos::float32_t dxvRearBumper_sw;
    zos::uint16_t fak1uBetaCorrSideSlipAngle_uw;
    zos::float32_t lWheelBase_sw;
    zos::int16_t lWheelTrack_sw;
    zos::int16_t dxvOriginRotationPoint_sw;
    zos::int16_t vCh_sw;
    zos::uint16_t tLatencyWheelVelocity;
    zos::uint16_t tLatencyPsiDt;
    zos::uint16_t tLatencyVxvSensor;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CHostVehicleParameters2, dxvMountPosAxSensor, dVehicleWidthMin_sw, dVehicleWidthMax_sw, dxvFrontBumper_sw, dxvRearBumper_sw, fak1uBetaCorrSideSlipAngle_uw, lWheelBase_sw, lWheelTrack_sw, dxvOriginRotationPoint_sw, vCh_sw, tLatencyWheelVelocity, tLatencyPsiDt, tLatencyVxvSensor)

enum class GearPosition
{
    EGO_GEAR_PARKING = 0,
    EGO_GEAR_REVERSE = 1,
    EGO_GEAR_NEUTRAL = 2,
    EGO_GEAR_DRIVING = 3,
    EGO_GEAR_MANNUAL = 4,
};
struct CHostVehicleMovementStates2
{
    zos::float32_t alpSideSlipAngle_sw;
    zos::float32_t alpSideSlipAngleErr;
    zos::float32_t axvRef_sw;
    zos::float32_t axvRaw;
    zos::float32_t axvFilter;
    zos::float32_t axvRefErr;
    zos::int16_t axvRoadSlope_sw;
    zos::float32_t axvRoadSlopeErr;
    zos::float32_t roadSlopeHistRange;
    zos::float32_t ayvRef_sw;
    zos::float32_t ayvRefErr;
    zos::float32_t kapCurvTraj_sw;
    zos::float32_t kapCurvTrajErr;
    zos::float32_t curvTrajDt;
    zos::float32_t psiDtOpt_sw;
    zos::float32_t vxvRef_sw;
    zos::int16_t vyvRef_sw;
    zos::float32_t psiDtOptErr_sw;
    zos::int16_t vxvRefErr_sw;
    zos::int16_t vyvRefErr_sw;
    zos::uint8_t vehicleStandStill_ub;
    zos::float32_t wheelAngleFront;
    zos::float32_t wheelAngleFrontErr;
    zos::float32_t wheelAngleGradientFront;
    zos::float32_t wheelAngleGradientFrontFilter;
    zos::float32_t ipbRodStroke;
    zos::float32_t wheelAngleGradientFrontErr;
    zos::float32_t wheelAngleRear;
    zos::float32_t wheelAngleRearErr;
    zos::uint32_t tAbsRefTime_u32;
    zos::bool_t isAebActive;
    GearPosition gearPosition;
    zos::bool_t isBrakePedalApplied;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CHostVehicleMovementStates2, alpSideSlipAngle_sw, alpSideSlipAngleErr, axvRef_sw, axvRaw, axvFilter, axvRefErr, axvRoadSlope_sw, axvRoadSlopeErr, roadSlopeHistRange, ayvRef_sw, ayvRefErr, kapCurvTraj_sw, kapCurvTrajErr, curvTrajDt, psiDtOpt_sw, vxvRef_sw, vyvRef_sw, psiDtOptErr_sw, vxvRefErr_sw, vyvRefErr_sw, vehicleStandStill_ub, wheelAngleFront, wheelAngleFrontErr, wheelAngleGradientFront, wheelAngleGradientFrontFilter, ipbRodStroke, wheelAngleGradientFrontErr, wheelAngleRear, wheelAngleRearErr, tAbsRefTime_u32, isAebActive, gearPosition, isBrakePedalApplied)

struct CDebugVariablesFcaEgoContext
{
    CHostVehicleParameters2 pPortHostVehParameters;
    CHostVehicleMovementStates2 pPortHostVehMovementStates;
    zos::float32_t driverDecelerationDemand;
    zos::float32_t smoothedLonDecel;
    zos::bool_t isAdaptiveCruiseControlActive;
    zos::float32_t brakePedalPosition;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CDebugVariablesFcaEgoContext, pPortHostVehParameters, pPortHostVehMovementStates, driverDecelerationDemand, smoothedLonDecel, isAdaptiveCruiseControlActive, brakePedalPosition)

}
}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::driving::as::CHostVehicleParameters2, StdLayoutTag>
{
    using ValueType = ::zos::driving::as::CHostVehicleParameters2;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::as::CHostVehicleParameters2, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::as::CHostVehicleParameters2;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::as::CHostVehicleMovementStates2, StdLayoutTag>
{
    using ValueType = ::zos::driving::as::CHostVehicleMovementStates2;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::as::CHostVehicleMovementStates2, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::as::CHostVehicleMovementStates2;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::as::CDebugVariablesFcaEgoContext, StdLayoutTag>
{
    using ValueType = ::zos::driving::as::CDebugVariablesFcaEgoContext;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::as::CDebugVariablesFcaEgoContext, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::as::CDebugVariablesFcaEgoContext;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
