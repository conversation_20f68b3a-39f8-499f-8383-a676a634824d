#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>

#include <zos_msg/driving/driving_activesafety/as_hostVehicleInfo.hpp>
#include <zos_msg/driving/driving_activesafety/as_fusedObjectInfo.hpp>
namespace zos
{
namespace driving
{
namespace as
{
struct CBehaviorEvaluation
{
    zos::float32_t necessity_f32;
    zos::float32_t collisionProbability_f32;
    zos::float32_t selfAccessment_f32;
    zos::float32_t recommendedTimeToBrake_f32;
    zos::float32_t timeToCollision_f32;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CBehaviorEvaluation, necessity_f32, collisionProbability_f32, selfAccessment_f32, recommendedTimeToBrake_f32, timeToCollision_f32)

struct CEvaluationOutput
{
    CHostVehicleMovementStates egoInfo;
    CFsmUseSignals fsmUseSignals;
    CBehaviorEvaluation fcaEvaluatorResult_st;
    CBehaviorEvaluation fcwEvaluatorResult_st;
    CBehaviorEvaluation craEvaluatorResult_st;
    CBehaviorEvaluation ltaEvaluatorResult_st;
    CAsFusedObjectInfo fcaTargetObjectInfo_f32;
    CAsFusedObjectInfo fcwTargetObjectInfo_f32;
    CAsFusedObjectInfo craTargetObjectInfo_f32;
    CAsFusedObjectInfo ltaTargetObjectInfo_f32;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CEvaluationOutput, egoInfo, fsmUseSignals, fcaEvaluatorResult_st, fcwEvaluatorResult_st, craEvaluatorResult_st, ltaEvaluatorResult_st, fcaTargetObjectInfo_f32, fcwTargetObjectInfo_f32, craTargetObjectInfo_f32, ltaTargetObjectInfo_f32)

}
}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::driving::as::CBehaviorEvaluation, StdLayoutTag>
{
    using ValueType = ::zos::driving::as::CBehaviorEvaluation;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::as::CBehaviorEvaluation, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::as::CBehaviorEvaluation;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::as::CEvaluationOutput, StdLayoutTag>
{
    using ValueType = ::zos::driving::as::CEvaluationOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::as::CEvaluationOutput, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::as::CEvaluationOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
