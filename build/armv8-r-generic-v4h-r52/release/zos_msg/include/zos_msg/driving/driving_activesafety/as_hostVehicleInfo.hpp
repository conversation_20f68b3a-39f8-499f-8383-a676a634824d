#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>

namespace zos
{
namespace driving
{
namespace as
{
struct CAebSubFunctionsStatus
{
    zos::bool_t isAEBActive_b;
    zos::bool_t isAEBAvailable_b;
    zos::bool_t isPrefillActive_b;
    zos::bool_t isPrefillAvailable_b;
    zos::bool_t isAWBActive_b;
    zos::bool_t isAWBAvailable_b;
    zos::bool_t isFCWActive_b;
    zos::bool_t isHBAAvailable_b;
    zos::bool_t isStandStill_b;
    zos::uint8_t FCWFault_ub;
    zos::uint8_t AEBFault_ub;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CAebSubFunctionsStatus, isAEBActive_b, isAEBAvailable_b, isPrefillActive_b, isPrefillAvailable_b, isAWBActive_b, isAWBAvailable_b, isFCWActive_b, isHBAAvailable_b, isStandStill_b, FCWFault_ub, AEBFault_ub)

enum class ESensitivityType
{
    LOW_SENSITIVITY = 0,
    NORMAL_SENSITIVITY = 1,
    HIGH_SENSITIVITY = 2,
};
struct CHmiSignals
{
    zos::bool_t aebHmiSwitchStatus_b;
    zos::bool_t fcwHmiSwitchStatus_b;
    ESensitivityType fcwHmiSensitivity;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CHmiSignals, aebHmiSwitchStatus_b, fcwHmiSwitchStatus_b, fcwHmiSensitivity)

struct CDiagnosticSignals
{
    zos::bool_t AEBDiagnosticSwitchStatus_b;
    zos::bool_t FCWDiagnosticSwitchStatus_b;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CDiagnosticSignals, AEBDiagnosticSwitchStatus_b, FCWDiagnosticSwitchStatus_b)

struct CESCState
{
    zos::bool_t ESPFailure_b;
    zos::bool_t ASRActive_b;
    zos::bool_t ABSActive_b;
    zos::bool_t EPBActive_b;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CESCState, ESPFailure_b, ASRActive_b, ABSActive_b, EPBActive_b)

enum class ETurnIndicatorType
{
    DIRCNIND_OFF = 0,
    DIRCNIND_TURN_LEFT = 1,
    DIRCNIND_TURN_RIGHT = 2,
};
struct CHostVehicleMovementStates
{
    zos::uint32_t timeStamp_ul;
    zos::float32_t vxv_f32;
    zos::float32_t vyv_f32;
    zos::float32_t axv_f32;
    zos::float32_t ayv_f32;
    zos::float32_t yawrate_f32;
    zos::int16_t alpSideSlipAngle_sw;
    zos::float32_t curvature_f32;
    zos::float32_t curvRate_f32;
    zos::float32_t wheelAngleFront_f32;
    zos::float32_t wheelAngleGradientFront_f32;
    zos::float32_t wheelAngleRear_f32;
    CAebSubFunctionsStatus AebSubFunctionsStatus_st;
    zos::bool_t isAccActive_b;
    zos::float32_t ipbRodStroke_f32;
    zos::bool_t isBrakePedalApplied_b;
    zos::float32_t brakePedalPosition_f32;
    zos::float32_t acceleratorPedalPosition_f32;
    zos::uint8_t driverMode_ub;
    ETurnIndicatorType turnLight;
    zos::uint8_t headWiper_ub;
    zos::uint8_t powerMode_ub;
    zos::uint8_t gearPosition_ub;
    CHmiSignals HmiSignals_st;
    CDiagnosticSignals DiagnosticSignals_st;
    CESCState EscState_st;
    zos::uint8_t egoMovementDirection_ub;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CHostVehicleMovementStates, timeStamp_ul, vxv_f32, vyv_f32, axv_f32, ayv_f32, yawrate_f32, alpSideSlipAngle_sw, curvature_f32, curvRate_f32, wheelAngleFront_f32, wheelAngleGradientFront_f32, wheelAngleRear_f32, AebSubFunctionsStatus_st, isAccActive_b, ipbRodStroke_f32, isBrakePedalApplied_b, brakePedalPosition_f32, acceleratorPedalPosition_f32, driverMode_ub, turnLight, headWiper_ub, powerMode_ub, gearPosition_ub, HmiSignals_st, DiagnosticSignals_st, EscState_st, egoMovementDirection_ub)

struct CWheelMovementDirection
{
    zos::uint8_t wheelMovementDirectionFL_ub;
    zos::uint8_t wheelMovementDirectionFR_ub;
    zos::uint8_t wheelMovementDirectionRL_ub;
    zos::uint8_t wheelMovementDirectionRR_ub;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CWheelMovementDirection, wheelMovementDirectionFL_ub, wheelMovementDirectionFR_ub, wheelMovementDirectionRL_ub, wheelMovementDirectionRR_ub)

struct CWheelSpeedValid
{
    zos::bool_t isWheelSpeedValidFL_b;
    zos::bool_t isWheelSpeedValidFR_b;
    zos::bool_t isWheelSpeedValidRL_b;
    zos::bool_t isWheelSpeedValidRR_b;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CWheelSpeedValid, isWheelSpeedValidFL_b, isWheelSpeedValidFR_b, isWheelSpeedValidRL_b, isWheelSpeedValidRR_b)

struct CAEBSubFunctionEnable
{
    zos::bool_t isEBAEnable_b;
    zos::bool_t isAWBEnable_b;
    zos::bool_t isAebEnableVru_b;
    zos::bool_t isAebEnableCar_b;
    zos::bool_t isWarnEnableVru_b;
    zos::bool_t isWarnEnableMotorVehicle_b;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CAEBSubFunctionEnable, isEBAEnable_b, isAWBEnable_b, isAebEnableVru_b, isAebEnableCar_b, isWarnEnableVru_b, isWarnEnableMotorVehicle_b)

struct CFsmUseSignals
{
    CWheelMovementDirection wheelMovementDirection_st;
    CWheelSpeedValid isWheelSpeedValid_st;
    CAEBSubFunctionEnable isAebSubFunctionEnabled_st;
    zos::uint8_t carMode_ub;
    zos::bool_t isAirbagOpen_b;
    zos::bool_t isDriverSeatBeltFastened_b;
    zos::bool_t isDriverDoorClosed_b;
    zos::bool_t isEgoSpeedValid_b;
    zos::bool_t isEgoAxvValid_b;
    zos::bool_t isEgoAyvValid_b;
    zos::bool_t isEgoYawrateValid_b;
    zos::uint8_t tr_shift_lvr_pos_ub;
    zos::uint8_t vseMode_ub;
    zos::uint8_t vseStatus_ub;
    zos::uint8_t tcsOpngMode_ub;
    zos::uint8_t tcsOpngStatus_ub;
    zos::uint8_t en_drag_rducnA_ub;
    zos::uint8_t hdc_sys_sts_ub;
    zos::uint8_t en_run_a_ub;
    zos::uint8_t AEBDecelReqResponse_ub;
    zos::uint8_t steeringAngleSensorCalSts_ub;
    zos::uint8_t steeringAngleSensorFault_ub;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CFsmUseSignals, wheelMovementDirection_st, isWheelSpeedValid_st, isAebSubFunctionEnabled_st, carMode_ub, isAirbagOpen_b, isDriverSeatBeltFastened_b, isDriverDoorClosed_b, isEgoSpeedValid_b, isEgoAxvValid_b, isEgoAyvValid_b, isEgoYawrateValid_b, tr_shift_lvr_pos_ub, vseMode_ub, vseStatus_ub, tcsOpngMode_ub, tcsOpngStatus_ub, en_drag_rducnA_ub, hdc_sys_sts_ub, en_run_a_ub, AEBDecelReqResponse_ub, steeringAngleSensorCalSts_ub, steeringAngleSensorFault_ub)

}
}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::driving::as::CAebSubFunctionsStatus, StdLayoutTag>
{
    using ValueType = ::zos::driving::as::CAebSubFunctionsStatus;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::as::CAebSubFunctionsStatus, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::as::CAebSubFunctionsStatus;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::as::CHmiSignals, StdLayoutTag>
{
    using ValueType = ::zos::driving::as::CHmiSignals;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::as::CHmiSignals, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::as::CHmiSignals;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::as::CDiagnosticSignals, StdLayoutTag>
{
    using ValueType = ::zos::driving::as::CDiagnosticSignals;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::as::CDiagnosticSignals, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::as::CDiagnosticSignals;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::as::CESCState, StdLayoutTag>
{
    using ValueType = ::zos::driving::as::CESCState;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::as::CESCState, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::as::CESCState;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::as::CHostVehicleMovementStates, StdLayoutTag>
{
    using ValueType = ::zos::driving::as::CHostVehicleMovementStates;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::as::CHostVehicleMovementStates, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::as::CHostVehicleMovementStates;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::as::CWheelMovementDirection, StdLayoutTag>
{
    using ValueType = ::zos::driving::as::CWheelMovementDirection;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::as::CWheelMovementDirection, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::as::CWheelMovementDirection;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::as::CWheelSpeedValid, StdLayoutTag>
{
    using ValueType = ::zos::driving::as::CWheelSpeedValid;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::as::CWheelSpeedValid, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::as::CWheelSpeedValid;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::as::CAEBSubFunctionEnable, StdLayoutTag>
{
    using ValueType = ::zos::driving::as::CAEBSubFunctionEnable;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::as::CAEBSubFunctionEnable, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::as::CAEBSubFunctionEnable;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::as::CFsmUseSignals, StdLayoutTag>
{
    using ValueType = ::zos::driving::as::CFsmUseSignals;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::as::CFsmUseSignals, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::as::CFsmUseSignals;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
