#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>

namespace zos
{
namespace driving
{
namespace as
{
struct FUS_StationaryTypes_st
{
    zos::uint8_t prob1RoadsideBarrier_ub;
    zos::uint8_t prob1Unknown_ub;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(FUS_StationaryTypes_st, prob1RoadsideBarrier_ub, prob1Unknown_ub)

struct FUS_FourPlusWheelerTypes_st
{
    zos::uint8_t prob1PassengerCar_ub;
    zos::uint8_t prob1Truck_ub;
    zos::uint8_t prob1Unknown_ub;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(FUS_FourPlusWheelerTypes_st, prob1PassengerCar_ub, prob1Truck_ub, prob1Unknown_ub)

struct FUS_MobileTypes_st
{
    FUS_FourPlusWheelerTypes_st probFourPlusWheeler_st;
    zos::uint8_t prob1FourPlusWheeler_ub;
    zos::uint8_t prob1TwoWheeler_ub;
    zos::uint8_t prob1Pedestrian_ub;
    zos::uint8_t prob1Unknown_ub;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(FUS_MobileTypes_st, probFourPlusWheeler_st, prob1FourPlusWheeler_ub, prob1TwoWheeler_ub, prob1Pedestrian_ub, prob1Unknown_ub)

struct FUS_ObstacleType_st
{
    FUS_MobileTypes_st probMobile_st;
    zos::uint8_t prob1Mobile_ub;
    FUS_StationaryTypes_st probStationary_st;
    zos::uint8_t prob1Stationary_ub;
    zos::uint8_t prob1Unknown_ub;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(FUS_ObstacleType_st, probMobile_st, prob1Mobile_ub, probStationary_st, prob1Stationary_ub, prob1Unknown_ub)

struct FUS_ObjectType_st
{
    FUS_ObstacleType_st probObstacle_st;
    zos::uint8_t prob1Obstacle_ub;
    zos::uint8_t prob1NonObstacle_ub;
    zos::uint8_t prob1Unknown_ub;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(FUS_ObjectType_st, probObstacle_st, prob1Obstacle_ub, prob1NonObstacle_ub, prob1Unknown_ub)

enum class EMotionType
{
    Unknown = 0,
    MovingEgoDirectionDriving = 1,
    MovingEgoDirectionReversing = 2,
    MovingOncoming = 3,
    MovingCrossing = 4,
    MovingEgoDirectionStopped = 5,
    Stationary = 6,
};
enum class ObstacleType
{
    OBSTACLE_TYPE_UNKNOWN = 0,
    OBSTACLE_TYPE_PEDESTRIAN = 1,
    OBSTACLE_TYPE_ANIMAL = 2,
    OBSTACLE_TYPE_GENERIC = 3,
    OBSTACLE_TYPE_BICYCLE = 4,
    OBSTACLE_TYPE_MOTORCYCLE = 5,
    OBSTACLE_TYPE_CAR = 6,
    OBSTACLE_TYPE_TRUCK = 7,
    OBSTACLE_TYPE_VEHICLE_UNKNOWN = 8,
};
struct CAsFusedObjectInfo
{
    zos::int16_t fusedHandle_sw;
    zos::uint32_t visionHandle_ul;
    zos::uint32_t updatedSensor_ul;
    zos::uint16_t refPoint_uw;
    zos::uint8_t refPointIndex_ub;
    zos::float32_t dxv_f32;
    zos::uint16_t dVarXv_uw;
    zos::float32_t dyv_f32;
    zos::uint16_t dVarYv_uw;
    zos::float32_t dzv_f32;
    zos::float32_t vxvRef_f32;
    zos::float32_t vyvRef_f32;
    zos::float32_t vxvAbs_f32;
    zos::float32_t vyvAbs_f32;
    zos::float32_t axvAbs_f32;
    zos::float32_t ayvAbs_f32;
    zos::uint16_t aRelVarXv_uw;
    zos::uint16_t aRelVarYv_uw;
    zos::int16_t alpPiYawAngle_sw;
    zos::uint16_t alpPiVarYawAngle_uw;
    zos::float32_t headingAngle_f32;
    zos::float32_t headingAngleVar_f32;
    zos::float32_t dWidth_f32;
    zos::float32_t dLength_f32;
    zos::uint16_t wExistProb_uw;
    zos::uint16_t age_uw;
    ObstacleType objectType;
    zos::uint8_t prob1Moving_ub;
    zos::uint8_t prob1HasBeenObservedMoving_ub;
    FUS_ObjectType_st FusType_st;
    zos::float32_t obstacleProb_f32;
    zos::uint16_t timePassedSinceAllActiveSensorTypesContributed_uw;
    zos::uint16_t timePassedSinceAllActiveSensorTypesStartedToContribute_uw;
    EMotionType objMotionTypeVision;
    zos::uint8_t visualSignalStatus_ub;
    zos::uint8_t obstacleSubType_ub;
    zos::uint8_t visionMotionCategory_ub;
    zos::uint8_t visiualLaneAssign_ub;
    zos::float32_t dHeight_f32;
    zos::bool_t isCipvObject_b;
    zos::uint32_t timeStamp_ul;
    zos::bool_t isObjectOccluded_b;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CAsFusedObjectInfo, fusedHandle_sw, visionHandle_ul, updatedSensor_ul, refPoint_uw, refPointIndex_ub, dxv_f32, dVarXv_uw, dyv_f32, dVarYv_uw, dzv_f32, vxvRef_f32, vyvRef_f32, vxvAbs_f32, vyvAbs_f32, axvAbs_f32, ayvAbs_f32, aRelVarXv_uw, aRelVarYv_uw, alpPiYawAngle_sw, alpPiVarYawAngle_uw, headingAngle_f32, headingAngleVar_f32, dWidth_f32, dLength_f32, wExistProb_uw, age_uw, objectType, prob1Moving_ub, prob1HasBeenObservedMoving_ub, FusType_st, obstacleProb_f32, timePassedSinceAllActiveSensorTypesContributed_uw, timePassedSinceAllActiveSensorTypesStartedToContribute_uw, objMotionTypeVision, visualSignalStatus_ub, obstacleSubType_ub, visionMotionCategory_ub, visiualLaneAssign_ub, dHeight_f32, isCipvObject_b, timeStamp_ul, isObjectOccluded_b)

}
}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::driving::as::FUS_StationaryTypes_st, StdLayoutTag>
{
    using ValueType = ::zos::driving::as::FUS_StationaryTypes_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::as::FUS_StationaryTypes_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::as::FUS_StationaryTypes_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::as::FUS_FourPlusWheelerTypes_st, StdLayoutTag>
{
    using ValueType = ::zos::driving::as::FUS_FourPlusWheelerTypes_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::as::FUS_FourPlusWheelerTypes_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::as::FUS_FourPlusWheelerTypes_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::as::FUS_MobileTypes_st, StdLayoutTag>
{
    using ValueType = ::zos::driving::as::FUS_MobileTypes_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::as::FUS_MobileTypes_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::as::FUS_MobileTypes_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::as::FUS_ObstacleType_st, StdLayoutTag>
{
    using ValueType = ::zos::driving::as::FUS_ObstacleType_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::as::FUS_ObstacleType_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::as::FUS_ObstacleType_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::as::FUS_ObjectType_st, StdLayoutTag>
{
    using ValueType = ::zos::driving::as::FUS_ObjectType_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::as::FUS_ObjectType_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::as::FUS_ObjectType_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::as::CAsFusedObjectInfo, StdLayoutTag>
{
    using ValueType = ::zos::driving::as::CAsFusedObjectInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::driving::as::CAsFusedObjectInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::driving::as::CAsFusedObjectInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
