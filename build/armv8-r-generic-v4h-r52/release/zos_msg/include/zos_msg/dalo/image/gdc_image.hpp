#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/container/vector.hpp>
#include <basic_types/types.hpp>

#include <zos_msg/common/timestamp.hpp>
namespace zos
{
namespace dalo
{
namespace image
{
enum class ImageFormat
{
    UNKNOWN = 0,
    GRAY = 1,
    BGR = 2,
    RGB = 3,
    BGRA = 4,
    RGBA = 5,
    YV12 = 6,
    YUV420P = 7,
    I420 = 8,
    YUV422P = 9,
    YUV444P = 10,
    NV12 = 11,
    NV21 = 12,
    YUYV = 13,
    UYVY = 14,
    HSV = 15,
    Lab = 16,
    YCrCb = 17,
    JPEG = 18,
    PNG = 19,
    WEBP = 20,
    TIFF = 21,
    BMP = 22,
    GIF = 23,
    MJPEG = 24,
    CR12 = 25,
    RAW = 26,
    BAD = 27,
    TIMEOUT = 28,
    BYPASS_ONLY = 29,
};
using GdcImageData=zos::FixedVector<zos::uint8_t, 16588800>;
struct GdcImageView
{
    zos::common::Timestamp m_timestamp;
    ImageFormat m_imageFormat;
    zos::uint32_t m_imageNumber;
    zos::uint32_t m_imageWidth;
    zos::uint32_t m_imageHeight;
    zos::uint32_t m_imageStride;
    zos::uint32_t m_imageSize;
    GdcImageData m_imageData;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(GdcImageView, m_timestamp, m_imageFormat, m_imageNumber, m_imageWidth, m_imageHeight, m_imageStride, m_imageSize, m_imageData)

enum class CodecEnumType
{
    UNKNOWN = 0,
    H264 = 1,
    H265 = 2,
};
using GdcImageCodecData=zos::FixedVector<zos::uint8_t, 829440>;
struct GdcImageCodec
{
    zos::common::Timestamp m_timestamp;
    ImageFormat m_imageFormat;
    CodecEnumType m_codecType;
    zos::uint32_t m_imageNumber;
    zos::uint32_t m_imageWidth;
    zos::uint32_t m_imageHeight;
    zos::uint32_t m_imageStride;
    zos::uint32_t m_imageSize;
    GdcImageCodecData m_imageData;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(GdcImageCodec, m_timestamp, m_imageFormat, m_codecType, m_imageNumber, m_imageWidth, m_imageHeight, m_imageStride, m_imageSize, m_imageData)

}
}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::dalo::image::GdcImageView, StdLayoutTag>
{
    using ValueType = ::zos::dalo::image::GdcImageView;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::dalo::image::GdcImageView, StdLayoutTruncTag>
{
    using ValueType = ::zos::dalo::image::GdcImageView;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, m_imageData) + sizeof(uint32_t);
        size += sample.m_imageData.size() * sizeof(decltype(sample.m_imageData)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,m_imageData));
        uint32_t des_size = offsetof(ValueType,m_imageData) + element_size * sizeof(decltype(sample.m_imageData)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::dalo::image::GdcImageCodec, StdLayoutTag>
{
    using ValueType = ::zos::dalo::image::GdcImageCodec;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::dalo::image::GdcImageCodec, StdLayoutTruncTag>
{
    using ValueType = ::zos::dalo::image::GdcImageCodec;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, m_imageData) + sizeof(uint32_t);
        size += sample.m_imageData.size() * sizeof(decltype(sample.m_imageData)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,m_imageData));
        uint32_t des_size = offsetof(ValueType,m_imageData) + element_size * sizeof(decltype(sample.m_imageData)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
