#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/container/string.hpp>
#include <basic_types/container/vector.hpp>
#include <basic_types/types.hpp>

#include <zos_msg/common/timestamp.hpp>
namespace zos
{
namespace dalo
{
namespace version
{
using String=zos::FixedString<128>;
struct DictionaryItem
{
    String key;
    String value;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(DictionaryItem, key, value)

using DictionaryList=zos::FixedVector<DictionaryItem, 1000>;
struct SystemVersionInfo
{
    zos::uint32_t info_count;
    DictionaryList info_list;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(SystemVersionInfo, info_count, info_list)

}
}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::dalo::version::DictionaryItem, StdLayoutTag>
{
    using ValueType = ::zos::dalo::version::DictionaryItem;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::dalo::version::DictionaryItem, StdLayoutTruncTag>
{
    using ValueType = ::zos::dalo::version::DictionaryItem;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, value) + sizeof(uint32_t);
        size += sample.value.size() * sizeof(decltype(sample.value)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,value));
        uint32_t des_size = offsetof(ValueType,value) + element_size * sizeof(decltype(sample.value)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::dalo::version::SystemVersionInfo, StdLayoutTag>
{
    using ValueType = ::zos::dalo::version::SystemVersionInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::dalo::version::SystemVersionInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::dalo::version::SystemVersionInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, info_list) + sizeof(uint32_t);
        size += sample.info_list.size() * sizeof(decltype(sample.info_list)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,info_list));
        uint32_t des_size = offsetof(ValueType,info_list) + element_size * sizeof(decltype(sample.info_list)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
