#pragma once
#include <basic_types/serialization/serialization_traits.hpp>

#include <zos_msg/common/timestamp.hpp>
namespace zos
{
namespace ipolaris
{
enum class AppRunningMode : zos::uint16_t
{
    HIIBERNATING_MODE = 0,
    BASIC_DRIVING_MODE = 1,
    HIGHWAY_NOP_ONLY_MODE = 2,
    HIGHWAY_URBAN_NOP_MODE = 3,
    TCP_MAPPING_CACHING_MODE = 4,
    TCP_MAPPING_POST_PROCESS_MODE = 5,
    TCP_MAPPING_ENDING_PROCESS_MODE = 6,
    TCP_NOP_MODE = 7,
    TCP_NOP_ENDING_PROCESS_MODE = 8,
};
enum class TcpSubRunningStatus : zos::uint16_t
{
    TCP_UNKNOWN = 0,
    TCP_MAPPING_CACHING_CHECKING = 1,
    TCP_MAPPING_CACHING_RUNNING = 2,
    TCP_MAPPING_POST_PROCESSING = 3,
    TCP_MAPPING_FAILED = 4,
    TCP_MAPPING_SUCCESS = 5,
    TCP_NOP_RELOCATING = 6,
    TCP_NOP_RUNNING = 7,
    TCP_NOP_FINISH = 8,
    TCP_NOP_RELOCATING_FAIL = 9,
};
enum class ErrorCode : zos::uint32_t
{
    NO_ERROR = 0,
    NO_IMU_INPUT = 1,
    NO_CHASSIS_STATE_INPUT = 2,
};
struct AppStatus
{
    zos::common::Timestamp timestamp;
    AppRunningMode app_running_mode;
    TcpSubRunningStatus tcp_sub_running_status;
    ErrorCode error_code;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(AppStatus, timestamp, app_running_mode, tcp_sub_running_status, error_code)

}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::ipolaris::AppStatus, StdLayoutTag>
{
    using ValueType = ::zos::ipolaris::AppStatus;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipolaris::AppStatus, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipolaris::AppStatus;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
