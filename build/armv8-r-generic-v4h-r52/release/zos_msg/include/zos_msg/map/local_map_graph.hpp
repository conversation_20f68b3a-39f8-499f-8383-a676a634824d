#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/container/vector.hpp>

#include <zos_msg/map/local_map_graph_common.hpp>
#include <zos_msg/map/local_map_graph_link.hpp>
#include <zos_msg/map/local_map_graph_lane.hpp>
#include <zos_msg/map/local_map_graph_boundary.hpp>
#include <zos_msg/map/local_map_graph_objects.hpp>
#include <zos_msg/map/local_map_graph_extension.hpp>
#include <zos_msg/navigation/navigation.hpp>
namespace zos
{
namespace map
{
const zos::uint8_t MAX_LINK_SIZE_IN_LOCAL_MAP_GRAPH = 24;
const zos::uint16_t MAX_LANE_SIZE_IN_LOCAL_MAP_GRAPH = (link::MAX_LANE_SIZE_IN_LINK * MAX_LINK_SIZE_IN_LOCAL_MAP_GRAPH);
const zos::uint16_t MAX_BOUNDARY_SIZE_IN_LOCAL_MAP_GRAPH = (MAX_LANE_SIZE_IN_LOCAL_MAP_GRAPH * 1.05);
const zos::uint8_t MAX_INTERSECTION_SIZE_IN_LOCAL_MAP_GRAPH = 8;
const zos::uint8_t MAX_ENTRY_AND_EXIT_PORT_SIZE_IN_ONE_INTERSECTION = 8;
const zos::uint16_t MAX_STOP_LINE_SIZE_IN_LOCAL_MAP_GRAPH = (MAX_INTERSECTION_SIZE_IN_LOCAL_MAP_GRAPH * MAX_ENTRY_AND_EXIT_PORT_SIZE_IN_ONE_INTERSECTION);
const zos::uint16_t MAX_PEDESTRIAN_CROSSING_SIZE_IN_LOCAL_MAP_GRAPH = (MAX_INTERSECTION_SIZE_IN_LOCAL_MAP_GRAPH * MAX_ENTRY_AND_EXIT_PORT_SIZE_IN_ONE_INTERSECTION);
const zos::uint16_t MAX_LIGHT_SIZE_IN_LOCAL_MAP_GRAPH = (MAX_INTERSECTION_SIZE_IN_LOCAL_MAP_GRAPH * 4);
const zos::uint16_t MAX_SIGN_SIZE_IN_LOCAL_MAP_GRAPH = 10;
const zos::uint16_t MAX_MARKING_SIZE_IN_LOCAL_MAP_GRAPH = 10;
const zos::uint8_t MAX_LANE_ASSOCIATION_SIZE_IN_LOCAL_MAP_GRAPH = 10;
using LinkSequence=zos::FixedVector<Link, MAX_LINK_SIZE_IN_LOCAL_MAP_GRAPH>;
using LaneSequence=zos::FixedVector<Lane, MAX_LANE_SIZE_IN_LOCAL_MAP_GRAPH>;
using BoundarySequence=zos::FixedVector<Boundary, MAX_BOUNDARY_SIZE_IN_LOCAL_MAP_GRAPH>;
using IntersectionSequence=zos::FixedVector<Intersection, MAX_INTERSECTION_SIZE_IN_LOCAL_MAP_GRAPH>;
using StopLineSequence=zos::FixedVector<StopLine, MAX_STOP_LINE_SIZE_IN_LOCAL_MAP_GRAPH>;
using PedestrianCrossingSequence=zos::FixedVector<PedestrianCrossing, MAX_PEDESTRIAN_CROSSING_SIZE_IN_LOCAL_MAP_GRAPH>;
using LightSequence=zos::FixedVector<Light, MAX_LIGHT_SIZE_IN_LOCAL_MAP_GRAPH>;
using SignSequence=zos::FixedVector<Sign, MAX_SIGN_SIZE_IN_LOCAL_MAP_GRAPH>;
using MarkingSequence=zos::FixedVector<Marking, MAX_MARKING_SIZE_IN_LOCAL_MAP_GRAPH>;
using LaneAssociationSequence=zos::FixedVector<FeatureAssociation, MAX_LANE_ASSOCIATION_SIZE_IN_LOCAL_MAP_GRAPH>;
struct LocalMapGraph
{
    zos::common::Timestamp timestamp;
    zos::common::Coordinate coordinate;
    FeatureSourceType source;
    FeatureLevelType level;
    LinkSequence links;
    LaneSequence lanes;
    BoundarySequence boundaries;
    IntersectionSequence intersections;
    StopLineSequence stop_lines;
    PedestrianCrossingSequence pedestrian_crossings;
    LightSequence lights;
    SignSequence signs;
    MarkingSequence markings;
    LaneAssociationSequence lane_associations;
    zos::geometry::Pose3f corrected_pose_error;
    zos::navigation::Navigation navigation;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(LocalMapGraph, timestamp, coordinate, source, level, links, lanes, boundaries, intersections, stop_lines, pedestrian_crossings, lights, signs, markings, lane_associations, corrected_pose_error, navigation)

const zos::uint16_t MAX_LANE_SIZE_IN_LITE_LOCAL_MAP_GRAPH = 10;
const zos::uint16_t MAX_BOUNDARY_SIZE_IN_LITE_LOCAL_MAP_GRAPH = (MAX_LANE_SIZE_IN_LITE_LOCAL_MAP_GRAPH * 2);
const zos::uint8_t MAX_INTERSECTION_SIZE_IN_LITE_LOCAL_MAP_GRAPH = 3;
using LiteLaneSequence=zos::FixedVector<LiteLane, MAX_LANE_SIZE_IN_LITE_LOCAL_MAP_GRAPH>;
using LiteBoundarySequence=zos::FixedVector<LiteBoundary, MAX_BOUNDARY_SIZE_IN_LITE_LOCAL_MAP_GRAPH>;
using LiteIntersectionSequence=zos::FixedVector<LiteIntersection, MAX_INTERSECTION_SIZE_IN_LITE_LOCAL_MAP_GRAPH>;
struct LiteLocalMapGraph
{
    zos::common::Timestamp timestamp;
    zos::common::Coordinate coordinate;
    FeatureSourceType source;
    FeatureLevelType level;
    LiteLaneSequence lanes;
    LiteBoundarySequence boundaries;
    LiteIntersectionSequence intersections;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(LiteLocalMapGraph, timestamp, coordinate, source, level, lanes, boundaries, intersections)

}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::map::LocalMapGraph, StdLayoutTag>
{
    using ValueType = ::zos::map::LocalMapGraph;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::map::LocalMapGraph, StdLayoutTruncTag>
{
    using ValueType = ::zos::map::LocalMapGraph;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::map::LiteLocalMapGraph, StdLayoutTag>
{
    using ValueType = ::zos::map::LiteLocalMapGraph;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::map::LiteLocalMapGraph, StdLayoutTruncTag>
{
    using ValueType = ::zos::map::LiteLocalMapGraph;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, intersections) + sizeof(uint32_t);
        size += sample.intersections.size() * sizeof(decltype(sample.intersections)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,intersections));
        uint32_t des_size = offsetof(ValueType,intersections) + element_size * sizeof(decltype(sample.intersections)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
