#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>
#include <basic_types/container/vector.hpp>

#include <zos_msg/map/local_map_graph_common.hpp>
namespace zos
{
namespace map
{
namespace boundary
{
enum class BoundaryType
{
    UNKNOWN = 0,
    BOUNDARY = 1,
    BARRIER = 2,
    CURB = 3,
    VIRTUAL = 4,
    WALL = 5,
    CLIFF = 6,
    DITCH = 7,
    OTHERS = 255,
};
enum class BoundaryStyleType
{
    UNKNOWN = 0,
    SINGLE_SOLID = 1,
    DOUBLE_SOLID = 2,
    SINGLE_DASHED = 3,
    DOUBLE_DASHED = 4,
    LEFT_DASHED_AND_RIGHT_SOLID_LINE = 5,
    LEFT_SOLID_AND_RIGHT_DASHED_LINE = 6,
    LONG_DASHED_LINE = 7,
    SHORT_DASHED_LINE = 8,
    NORMAL_FISH_BONE_LINE = 9,
    CENTER_SINGLE_SOLID_AND_FISH_BONE_LINE = 10,
    CENTER_SINGLE_DASHED_AND_FISH_BONE_LINE = 11,
    CENTER_DOUBLE_SOLID_AND_FISH_BONE_LINE = 12,
    CENTER_DOUBLE_DASHED_AND_FISH_BONE_LINE = 13,
    CENTER_LEFT_SOLID_AND_RIGHT_DASHED_AND_FISH_BONE_LINE = 14,
    CENTER_LEFT_DASHED_AND_RIGHT_SOLID_AND_FISH_BONE_LINE = 15,
    LEFT_SOLID_AND_RIGHT_FISH_BONE_LINE = 16,
    RIGHT_SOLID_AND_LEFT_FISH_BONE_LINE = 17,
    LEFT_DASHED_AND_RIGHT_FISH_BONE_LINE = 18,
    RIGHT_DASHED_AND_LEFT_FISH_BONE_LINE = 19,
    OTHERS_FISH_BONE_LINE = 20,
    SHADED_AREA_MARKING = 21,
    VIRTUAL = 22,
    OTHERS = 255,
};
enum class BoundaryColorType
{
    UNKNOWN = 0,
    WHITE = 1,
    YELLOW = 2,
};
struct BoundaryAttributeInterval
{
    IntervalBase interval_info;
    BoundaryType type;
    BoundaryStyleType style;
    BoundaryColorType color;
    CentermeterType width;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(BoundaryAttributeInterval, interval_info, type, style, color, width)

enum class BoundarySequenceDirection
{
    UNKNOWN = 0,
    EGO_TO_LEFT = 1,
    EGO_TO_RIGHT = 2,
};
struct BoundarySequence
{
    BoundarySequenceDirection direction;
    zos::uint8_t sequence;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(BoundarySequence, direction, sequence)

const zos::uint8_t MAX_PRECEDING_SUCCEEDING_BOUNDARIES_SIZE = 3;
const zos::uint16_t MAX_BOUNDARY_CENTER_LINE_POINTS_SIZE = 200;
const zos::uint8_t MAX_BOUNDARY_ATTRIBUTE_INTERVAL_SIZE = 5;
using PrecedingSucceedingBoundariesIdSequence=zos::FixedVector<FeatureIdType, MAX_PRECEDING_SUCCEEDING_BOUNDARIES_SIZE>;
using CenterLineGoemetrySequence=zos::FixedVector<FeatureGeometry2fType, MAX_BOUNDARY_CENTER_LINE_POINTS_SIZE>;
using BoundaryAttributeIntervalSequence=zos::FixedVector<BoundaryAttributeInterval, MAX_BOUNDARY_ATTRIBUTE_INTERVAL_SIZE>;
struct Boundary
{
    FeatureIdType id;
    FeatureSourceType source;
    FeatureLevelType level;
    BoundaryAttributeIntervalSequence attribute_intervals;
    CenterLineGoemetrySequence center_line;
    FeatureIdType link_id;
    FeatureIdType left_lane_id;
    FeatureIdType right_lane_id;
    PrecedingSucceedingBoundariesIdSequence preceding_boundary_ids;
    PrecedingSucceedingBoundariesIdSequence succeeding_boundary_ids;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(Boundary, id, source, level, attribute_intervals, center_line, link_id, left_lane_id, right_lane_id, preceding_boundary_ids, succeeding_boundary_ids)

struct LiteBoundary
{
    FeatureIdType id;
    FeatureSourceType source;
    FeatureLevelType level;
    BoundaryAttributeIntervalSequence attribute_intervals;
    BoundarySequence sequence;
    CentermeterType length;
    ScalarType range_start_x;
    ScalarType range_end_x;
    zos::numerics::Vector4f coefficients;
    FeatureIdType left_lane_id;
    FeatureIdType right_lane_id;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(LiteBoundary, id, source, level, attribute_intervals, sequence, length, range_start_x, range_end_x, coefficients, left_lane_id, right_lane_id)

}
using Boundary=boundary::Boundary;
using LiteBoundary=boundary::LiteBoundary;
}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::map::boundary::BoundaryAttributeInterval, StdLayoutTag>
{
    using ValueType = ::zos::map::boundary::BoundaryAttributeInterval;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::map::boundary::BoundaryAttributeInterval, StdLayoutTruncTag>
{
    using ValueType = ::zos::map::boundary::BoundaryAttributeInterval;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::map::boundary::BoundarySequence, StdLayoutTag>
{
    using ValueType = ::zos::map::boundary::BoundarySequence;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::map::boundary::BoundarySequence, StdLayoutTruncTag>
{
    using ValueType = ::zos::map::boundary::BoundarySequence;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::map::boundary::Boundary, StdLayoutTag>
{
    using ValueType = ::zos::map::boundary::Boundary;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::map::boundary::Boundary, StdLayoutTruncTag>
{
    using ValueType = ::zos::map::boundary::Boundary;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, succeeding_boundary_ids) + sizeof(uint32_t);
        size += sample.succeeding_boundary_ids.size() * sizeof(decltype(sample.succeeding_boundary_ids)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,succeeding_boundary_ids));
        uint32_t des_size = offsetof(ValueType,succeeding_boundary_ids) + element_size * sizeof(decltype(sample.succeeding_boundary_ids)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::map::boundary::LiteBoundary, StdLayoutTag>
{
    using ValueType = ::zos::map::boundary::LiteBoundary;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::map::boundary::LiteBoundary, StdLayoutTruncTag>
{
    using ValueType = ::zos::map::boundary::LiteBoundary;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
