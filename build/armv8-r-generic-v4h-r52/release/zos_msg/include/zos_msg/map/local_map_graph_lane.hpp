#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>
#include <basic_types/container/vector.hpp>

#include <zos_msg/map/local_map_graph_common.hpp>
namespace zos
{
namespace map
{
namespace lane
{
enum class SequenceDirectionType
{
    LEFT_TO_RIGHT = 0,
    RIGHT_TO_LEFT = 1,
    EGO_RELATIVE = 2,
};
struct Sequence
{
    zos::bool_t is_valid;
    SequenceDirectionType direction;
    zos::int8_t sequence_number;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(Sequence, is_valid, direction, sequence_number)

struct SpeedLimitInterval
{
    IntervalBase interval_info;
    ScalarType max_speed;
    ScalarType min_speed;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(SpeedLimitInterval, interval_info, max_speed, min_speed)

struct WidthInterval
{
    IntervalBase interval_info;
    zos::uint32_t width;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(WidthInterval, interval_info, width)

enum class ChangeDirectionType
{
    UNKNOWN = 0,
    CHANGE_DIRECTION_TO_LEFT = 1,
    CHANGE_DIRECTION_TO_RIGHT = 2,
    SPLIT_DIRECTION_TO_LEFT = 3,
    MERGE_DIRECTION_TO_LEFT = 4,
    SPLIT_DIRECTION_TO_RIGHT = 5,
    MERGE_DIRECTION_TO_RIGHT = 6,
};
struct ChangeRegionInterval
{
    IntervalBase interval_info;
    ChangeDirectionType direction;
    FeatureIdType target_lane_id;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(ChangeRegionInterval, interval_info, direction, target_lane_id)

struct AdjacentRegionInterval
{
    IntervalBase interval_info;
    FeatureIdType adjacent_lane_id;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(AdjacentRegionInterval, interval_info, adjacent_lane_id)

enum class LaneType
{
    UNKNOWN = 0,
    REGULAR = 1,
    EXPRESS = 2,
    SLOW = 3,
    EMERGENCY = 4,
    EMERGENCY_PARKING_STRIP = 5,
    CLIMBING = 6,
    ESCAPE = 7,
    ENTRY = 8,
    EXIT = 9,
    ACCELERATE = 10,
    DECELERATE = 11,
    RAMP = 12,
    RAMP_ABORD = 13,
    RAMP_ASHORE = 14,
    RAMP_JCT = 15,
    TOLLBOOTH = 16,
    NONDRIVEWAY = 17,
    DEDICATED_BUS = 18,
    DEDICATED_CUSTOM = 19,
    REVERSIBLE = 20,
    VARIABLE = 21,
    DRIVABLE_SHOULDER = 22,
    DISDRIVABLE_SHOULDER = 23,
    BICYCLE = 24,
    OTHERS = 255,
};
enum class LaneTransitionType
{
    UNKNOWN = 0,
    NONE = 1,
    OPENING = 2,
    ENDING = 3,
    WIDE_STEP_BY_STEP = 4,
    NARROW_STEP_BY_STEP = 5,
    OPENING_AND_ENDING = 6,
    SPLIT_LEFT = 7,
    SPLIT_RIGHT = 8,
    MERGE_LEFT = 9,
    MERGE_RIGHT = 10,
    MERGE_PASS_BY_LEFT = 11,
    MERGE_PASS_BY_RIGHT = 12,
    OTHERS = 255,
};
enum class LaneTurnType
{
    UNKNOWN = 0,
    LEFT = 1,
    RIGHT = 2,
    LEFT_AROUND = 4,
    RIGHT_AROUND = 8,
    STRAIGHT = 16,
    LEFT_AREA = 32,
};
const zos::uint8_t MAX_PRECEDING_SUCCEEDING_LANE_SIZE = 10;
const zos::uint16_t MAX_LANE_CENTER_LINE_POINTS_SIZE = 200;
const zos::uint8_t MAX_WINTH_INTERVAL_SIZE = MAX_LANE_CENTER_LINE_POINTS_SIZE;
const zos::uint8_t MAX_DEFAULT_INTERVAL_SIZE = 5;
const zos::uint8_t MAX_ASSOCIATED_OBJECTS_FEATURE_ID_SIZE = 5;
using PrecedingSucceedingLanesIdSequence=zos::FixedVector<FeatureIdType, MAX_PRECEDING_SUCCEEDING_LANE_SIZE>;
using CenterLineGoemetrySequence=zos::FixedVector<FeatureGeometry2fType, MAX_LANE_CENTER_LINE_POINTS_SIZE>;
using WidthIntervalSequence=zos::FixedVector<WidthInterval, MAX_WINTH_INTERVAL_SIZE>;
using SpeedLimitIntervalSequence=zos::FixedVector<SpeedLimitInterval, MAX_DEFAULT_INTERVAL_SIZE>;
using ChangeRegionIntervalSequence=zos::FixedVector<ChangeRegionInterval, MAX_DEFAULT_INTERVAL_SIZE>;
using AdjacentRegionIntervalSequence=zos::FixedVector<AdjacentRegionInterval, MAX_DEFAULT_INTERVAL_SIZE>;
using AssociatedObjectsIdSequence=zos::FixedVector<FeatureIdType, MAX_ASSOCIATED_OBJECTS_FEATURE_ID_SIZE>;
struct Lane
{
    FeatureIdType id;
    FeatureSourceType source;
    FeatureLevelType level;
    LaneType lane_type;
    LaneTransitionType transition_type;
    LaneTurnType turn_type;
    Sequence absolute_left_to_right_sequence;
    Sequence absolute_right_to_left_sequence;
    Sequence relative_sequence;
    CentermeterType length;
    CenterLineGoemetrySequence center_line;
    WidthIntervalSequence width_intervals;
    SpeedLimitIntervalSequence speed_limit_intervals;
    ChangeRegionIntervalSequence left_change_region_intervals;
    ChangeRegionIntervalSequence right_change_region_intervals;
    AdjacentRegionIntervalSequence left_adjacent_region_intervals;
    AdjacentRegionIntervalSequence right_adjacent_region_intervals;
    FeatureIdType link_id;
    FeatureIdType left_lane_id;
    FeatureIdType right_lane_id;
    PrecedingSucceedingLanesIdSequence preceding_lane_ids;
    PrecedingSucceedingLanesIdSequence succeeding_lane_ids;
    FeatureIdType left_boundary_id;
    FeatureIdType right_boundary_id;
    FeatureIdType belonged_intersection_id;
    AssociatedObjectsIdSequence stop_line_ids;
    AssociatedObjectsIdSequence sign_ids;
    AssociatedObjectsIdSequence traffic_light_ids;
    AssociatedObjectsIdSequence marker_ids;
    AssociatedObjectsIdSequence pedestrian_crossing_ids;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(Lane, id, source, level, lane_type, transition_type, turn_type, absolute_left_to_right_sequence, absolute_right_to_left_sequence, relative_sequence, length, center_line, width_intervals, speed_limit_intervals, left_change_region_intervals, right_change_region_intervals, left_adjacent_region_intervals, right_adjacent_region_intervals, link_id, left_lane_id, right_lane_id, preceding_lane_ids, succeeding_lane_ids, left_boundary_id, right_boundary_id, belonged_intersection_id, stop_line_ids, sign_ids, traffic_light_ids, marker_ids, pedestrian_crossing_ids)

struct LiteLane
{
    FeatureIdType id;
    FeatureSourceType source;
    FeatureLevelType level;
    LaneType lane_type;
    LaneTransitionType transition_type;
    LaneTurnType turn_type;
    Sequence relative_sequence;
    CentermeterType length;
    ScalarType range_start_x;
    ScalarType range_end_x;
    zos::numerics::Vector4f center_line_coefficients;
    FeatureIdType left_lane_id;
    FeatureIdType right_lane_id;
    FeatureIdType left_boundary_id;
    FeatureIdType right_boundary_id;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(LiteLane, id, source, level, lane_type, transition_type, turn_type, relative_sequence, length, range_start_x, range_end_x, center_line_coefficients, left_lane_id, right_lane_id, left_boundary_id, right_boundary_id)

}
using Lane=lane::Lane;
using LiteLane=lane::LiteLane;
}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::map::lane::Sequence, StdLayoutTag>
{
    using ValueType = ::zos::map::lane::Sequence;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::map::lane::Sequence, StdLayoutTruncTag>
{
    using ValueType = ::zos::map::lane::Sequence;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::map::lane::SpeedLimitInterval, StdLayoutTag>
{
    using ValueType = ::zos::map::lane::SpeedLimitInterval;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::map::lane::SpeedLimitInterval, StdLayoutTruncTag>
{
    using ValueType = ::zos::map::lane::SpeedLimitInterval;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::map::lane::WidthInterval, StdLayoutTag>
{
    using ValueType = ::zos::map::lane::WidthInterval;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::map::lane::WidthInterval, StdLayoutTruncTag>
{
    using ValueType = ::zos::map::lane::WidthInterval;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::map::lane::ChangeRegionInterval, StdLayoutTag>
{
    using ValueType = ::zos::map::lane::ChangeRegionInterval;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::map::lane::ChangeRegionInterval, StdLayoutTruncTag>
{
    using ValueType = ::zos::map::lane::ChangeRegionInterval;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::map::lane::AdjacentRegionInterval, StdLayoutTag>
{
    using ValueType = ::zos::map::lane::AdjacentRegionInterval;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::map::lane::AdjacentRegionInterval, StdLayoutTruncTag>
{
    using ValueType = ::zos::map::lane::AdjacentRegionInterval;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::map::lane::Lane, StdLayoutTag>
{
    using ValueType = ::zos::map::lane::Lane;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::map::lane::Lane, StdLayoutTruncTag>
{
    using ValueType = ::zos::map::lane::Lane;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, pedestrian_crossing_ids) + sizeof(uint32_t);
        size += sample.pedestrian_crossing_ids.size() * sizeof(decltype(sample.pedestrian_crossing_ids)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,pedestrian_crossing_ids));
        uint32_t des_size = offsetof(ValueType,pedestrian_crossing_ids) + element_size * sizeof(decltype(sample.pedestrian_crossing_ids)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::map::lane::LiteLane, StdLayoutTag>
{
    using ValueType = ::zos::map::lane::LiteLane;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::map::lane::LiteLane, StdLayoutTruncTag>
{
    using ValueType = ::zos::map::lane::LiteLane;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
