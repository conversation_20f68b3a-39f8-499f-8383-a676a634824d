#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/container/vector.hpp>
#include <basic_types/container/string.hpp>
#include <basic_types/types.hpp>

#include <zos_msg/map/local_map_graph_common.hpp>
namespace zos
{
namespace map
{
namespace object
{
const zos::uint8_t MAX_STOP_LINE_GEOMETRY_POINTS_SIZE = 2;
const zos::uint8_t MAX_STOP_LINE_ASSOCIATED_LANE_SIZE = 10;
using StopLineGeometryPointSequence=zos::FixedVector<FeatureGeometry2fType, MAX_STOP_LINE_GEOMETRY_POINTS_SIZE>;
using StopLineAssociatedLanesIdSequence=zos::FixedVector<FeatureIdType, MAX_STOP_LINE_ASSOCIATED_LANE_SIZE>;
struct StopLine
{
    FeatureIdType id;
    FeatureSourceType source;
    FeatureLevelType level;
    StopLineGeometryPointSequence geometry;
    StopLineAssociatedLanesIdSequence associated_lane_ids;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(StopLine, id, source, level, geometry, associated_lane_ids)

enum class MarkingColorType
{
    UNKNOWN = 0,
    WHITE = 1,
    YELLOW = 2,
    ORANGE = 3,
};
enum class MarkingType
{
    UNKNOWN = 0,
    TEXT = 1,
    STRAIGHT = 2,
    STRAIGHT_OR_LEFT = 3,
    STRAIGHT_OR_RIGHT = 4,
    STRAIGHT_U_TURN = 5,
    LEFT_TURN = 6,
    LEFT_TURN_U_TURN = 7,
    LEFT_TURN_AND_INTERFLOW = 8,
    RIGHT_TURN = 9,
    RIGHT_TURN_AND_INTERFLOW = 10,
    LEFT_RIGHT_TURN = 11,
    U_TURN = 12,
    NO_LEFT_TURN = 13,
    NO_RIGHT_TURN = 14,
    NO_U_TURN = 15,
    STRAIGHT_LEFT_RIGHT = 16,
    STRAIGHT_U_LEFT = 17,
    RIGHT_U_TURN = 18,
    MAX_SPEEDLIMIT = 19,
    MIN_SPEEDLIMIT = 20,
    TIME = 21,
    CHECK_FOLLOWING_DISTANCE = 22,
    STOP_TO_GIVEWAY = 23,
    SLOWDOWN_TO_GIVEWAY = 24,
    STOP_MARK = 25,
    NETS = 26,
    OTHERS = 255,
};
const zos::uint8_t MAX_MARKING_GEOMETRY_POINTS_SIZE = 4;
const zos::uint8_t MAX_MARKING_ASSOCIATED_LANE_SIZE = 10;
const zos::uint8_t MAX_MARKING_TEXT_SIZE = 100;
using MarkingGeometryPointSequence=zos::FixedVector<FeatureGeometry2fType, MAX_MARKING_GEOMETRY_POINTS_SIZE>;
using MarkingAssociatedLanesIdSequence=zos::FixedVector<FeatureIdType, MAX_MARKING_ASSOCIATED_LANE_SIZE>;
using MarkingTextType=zos::FixedString<MAX_MARKING_TEXT_SIZE>;
struct Marking
{
    FeatureIdType id;
    FeatureSourceType source;
    FeatureLevelType level;
    MarkingColorType color;
    MarkingType type;
    zos::geometry::Box2f geometry_box;
    MarkingGeometryPointSequence geometry_points;
    MarkingAssociatedLanesIdSequence associated_lane_ids;
    MarkingTextType semantic_text;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(Marking, id, source, level, color, type, geometry_box, geometry_points, associated_lane_ids, semantic_text)

enum class SignType
{
    UNKNOWN = 0,
    LIMIT_SPEED_MAX_10 = 1,
    LIMIT_SPEED_MAX_20 = 2,
    LIMIT_SPEED_MAX_30 = 3,
    LIMIT_SPEED_MAX_40 = 4,
    LIMIT_SPEED_MAX_50 = 5,
    LIMIT_SPEED_MAX_60 = 6,
    LIMIT_SPEED_MAX_70 = 7,
    LIMIT_SPEED_MAX_80 = 8,
    LIMIT_SPEED_MAX_90 = 9,
    LIMIT_SPEED_MAX_100 = 10,
    LIMIT_SPEED_MAX_110 = 11,
    LIMIT_SPEED_MAX_120 = 12,
    LIMIT_SPEED_MAX_130 = 13,
    LIMIT_SPEED_MIN_30 = 14,
    LIMIT_SPEED_MIN_40 = 15,
    LIMIT_SPEED_MIN_50 = 16,
    LIMIT_SPEED_MIN_60 = 17,
    LIMIT_SPEED_MIN_70 = 18,
    LIMIT_SPEED_MIN_80 = 19,
    LIMIT_SPEED_MIN_90 = 20,
    LIMIT_SPEED_MIN_100 = 21,
    LIMIT_SPEED_MIN_110 = 22,
    REMOVE_LIMIT_SPEED_MAX_20 = 23,
    REMOVE_LIMIT_SPEED_MAX_40 = 24,
    REMOVE_LIMIT_SPEED_MAX_60 = 25,
    REMOVE_LIMIT_SPEED_MAX_80 = 26,
    REMOVE_LIMIT_SPEED_MAX_100 = 27,
    RAMP_SPEEDLIMIT = 28,
    RANGE_SPEEDLIMIT = 29,
    NO_ENTRY = 30,
    NO_PARKING = 31,
    NO_LEFT = 32,
    NO_RIGHT_TURN = 33,
    NO_TURN_AROUND = 34,
    NO_PASSING = 35,
    NO_LONG_DRIVING = 36,
    NO_MOTOR_VEHICLE = 37,
    NO_TOOTING = 38,
    NO_OVERTAKING = 39,
    NO_MOTORCYCLE = 40,
    NO_TRUCK = 41,
    NO_BUS = 42,
    NO_NONMOTORCYCLE = 43,
    NO_VEHICLE_WITH_DANGEROUS_GOODS = 44,
    ROAD_NARROWING_LEFT = 45,
    ROAD_NARROWING_RIGHT = 46,
    ROAD_NARROWING_BOTH_SIDES = 47,
    MERGE_LEFT = 48,
    MERGE_RIGHT = 49,
    MERGE_GENERAL = 50,
    AHEAD_JUNCTION = 51,
    AHEAD_LEFT_JUNCTION = 52,
    AHEAD_RIGHT_JUNCTION = 53,
    AHEAD_CROSSROAD = 54,
    AHEAD_ROUNDABOUT = 55,
    AHEAD_SHARP_LEFT_TURN = 56,
    AHEAD_SHARP_RIGHT_TURN = 57,
    AHEAD_CONSECUTIVE_CURVES = 58,
    LANE_DECREASE = 59,
    AHEAD_ROAD_CHANGE = 60,
    AHEAD_RAMP = 61,
    AHEAD_TUNNEL = 62,
    AHEAD_CAMELBACK_BRIDGE = 63,
    AHEAD_UP_HILL = 64,
    AHEAD_DOWN_HILL = 65,
    HIGHWAY_ENDING = 66,
    WARNING_DANGEROUS_AREA = 67,
    WARNING_PEDESTRIANS = 68,
    WARNING_KIDS = 69,
    WARNING_NONMOTOR_VEHICLES = 70,
    WARNING_CROSS_WIND = 71,
    WARNING_SIGNAL_LAMP = 72,
    WARNING_SLOW_DOWN = 73,
    WARNING_STOP_TO_GIVE_WAY = 74,
    WARNING_SLOWDOWN_TO_GIVE_WAY = 75,
    WARNING_CAR_MEETING_GIVE_WAY = 76,
    WARNING_ACCIDENT_BLACK_SPOT = 77,
    WARNING_VILLAGE_AHEAD = 78,
    WARNING_ROCKS_AHEAD = 79,
    WARNING_SCHOOL_ZONE = 80,
    WARNING_ELECTRONIC_EYE = 81,
    GO_STRAIGHT = 82,
    TURN_LEFT = 83,
    TURN_RIGHT = 84,
    KEEP_RIGHT = 85,
    KEEP_LEFT = 86,
    DRIVE_ROUNDABOUT = 87,
    WALK_SIGN = 88,
    HONK_SIGN = 89,
    MOTOR_VEHICLE_SIGN = 90,
    NONMOTOR_VEHICLE_SIGN = 91,
    PEDESTRIAN_CROSSING_SIGN = 92,
    U_TURN_LANE_SIGN = 93,
    PARKING_LOT_SIGN = 94,
    PASSING_BAY_SIGN = 95,
    AUXILIARY_SIGN = 96,
    ETC_MTC_SIGN = 97,
    ALLOW_RIGHT_TURN_ON_RED = 98,
    TOURIST_SPOT_SIGN = 99,
    LANE_LEFT_TURN = 100,
    LANE_RIGHT_TURN = 101,
    LANE_U_TURN = 102,
    LANE_LEFT_STRAIGHT = 103,
    LANE_RIGHT_STRAIGHT = 104,
    LANE_LEFT_U_TURN = 105,
    LANE_MOTOR_VEHICLE = 106,
    LANE_NONMOTOR_VEHICLE = 107,
    LANE_BUS = 108,
    LANE_HOV = 109,
    LANE_ESCAPE = 110,
    LANE_VARIABLE = 111,
    SIGN_OTHERS = 112,
    SIGN_TURN_ON_LIGHT_IN_TUNNEL = 113,
    OTHERS = 255,
};
const zos::uint8_t MAX_SIGN_ASSOCIATED_LANE_SIZE = 10;
using SignAssociatedLanesIdSequence=zos::FixedVector<FeatureIdType, MAX_SIGN_ASSOCIATED_LANE_SIZE>;
struct Sign
{
    FeatureIdType id;
    FeatureSourceType source;
    FeatureLevelType level;
    SignType type;
    zos::geometry::Box3f geometry_box;
    SignAssociatedLanesIdSequence associated_lane_ids;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(Sign, id, source, level, type, geometry_box, associated_lane_ids)

enum class LightSignalClassType
{
    UNKNOWN = 0,
    VEHICLE = 1,
    PEDESTRIAN = 2,
    BICYCLE = 3,
    OTHERS = 255,
};
enum class LightSignalColorType
{
    UNKNOW = 0,
    RED = 1,
    GREEN = 2,
    YELLOW = 3,
    WHITE = 4,
    OTHERS = 255,
};
enum class LightSignalStatusType
{
    UNKNOWN = 0,
    CIRCULAR = 1,
    FORWARD_GO = 2,
    LEFT_TURN = 3,
    LEFT_AND_RIGHT_TURN = 4,
    LEFT_TURN_AND_U_TURN = 5,
    LEFT_TURN_AND_FORWARD = 6,
    LEFT_RIGHT_TURN_AND_FORWARD = 7,
    RIGHT_TURN = 8,
    RIGHT_TURN_AND_FORWARD = 9,
    RIGHT_TURN_AND_U_TURN = 10,
    U_TURN = 11,
    NO_ENTRY = 12,
    ALLOW_ENTRY = 13,
    PEDESTRIAN_ONLY = 14,
    NON_MOTOR_VEHICLE_ONLY = 15,
    OTHER_STATUSES = 255,
};
struct SignalCountDown
{
    zos::bool_t is_active;
    zos::uint16_t count_down_time;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(SignalCountDown, is_active, count_down_time)

const zos::uint8_t MAX_LIGHT_ASSOCIATED_LANE_SIZE = 10;
using LightAssociatedLanesIdSequence=zos::FixedVector<FeatureIdType, MAX_LIGHT_ASSOCIATED_LANE_SIZE>;
struct Light
{
    FeatureIdType id;
    FeatureSourceType source;
    FeatureLevelType level;
    LightSignalClassType signal_type;
    LightSignalColorType signal_color;
    LightSignalStatusType signal_status;
    SignalCountDown count_down;
    zos::geometry::Box3f geometry_box;
    LightAssociatedLanesIdSequence associated_lane_ids;
    zos::bool_t is_light_on;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(Light, id, source, level, signal_type, signal_color, signal_status, count_down, geometry_box, associated_lane_ids, is_light_on)

const zos::uint8_t MAX_PDESTRIAN_CROSSING_ASSOCIATED_LANE_SIZE = 10;
const zos::uint8_t MAX_PDESTRIAN_CROSSING_GEOMETRY_POINTS_SIZE = 4;
using PedestrianCrossingAssociatedLanesIdSequence=zos::FixedVector<FeatureIdType, MAX_PDESTRIAN_CROSSING_ASSOCIATED_LANE_SIZE>;
using PedestrianCrossingGeometryPointSequence=zos::FixedVector<FeatureGeometry2fType, MAX_PDESTRIAN_CROSSING_GEOMETRY_POINTS_SIZE>;
struct PedestrianCrossing
{
    FeatureIdType id;
    FeatureSourceType source;
    FeatureLevelType level;
    zos::geometry::Box2f geometry_box;
    PedestrianCrossingGeometryPointSequence geometry_points;
    PedestrianCrossingAssociatedLanesIdSequence associated_lane_ids;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(PedestrianCrossing, id, source, level, geometry_box, geometry_points, associated_lane_ids)

const zos::uint8_t MAX_INTERSECTION_ASSOCIATED_LANE_SIZE = 10;
const zos::uint8_t MAX_INTERSECTION_ASSOCIATED_LINK_SIZE = 32;
const zos::uint8_t MAX_INTERSECTION_GEOMETRY_POINTS_SIZE = 32;
using IntersectionAssociatedLanesIdSequence=zos::FixedVector<FeatureIdType, MAX_INTERSECTION_ASSOCIATED_LANE_SIZE>;
using IntersectionAssociatedLinkIdSequence=zos::FixedVector<FeatureIdType, MAX_INTERSECTION_ASSOCIATED_LINK_SIZE>;
using IntersectionGeometryPointSequence=zos::FixedVector<FeatureGeometry2fType, MAX_INTERSECTION_GEOMETRY_POINTS_SIZE>;
struct Intersection
{
    FeatureIdType id;
    FeatureSourceType source;
    FeatureLevelType level;
    IntersectionAssociatedLanesIdSequence lane_ids_within;
    IntersectionAssociatedLinkIdSequence link_ids_within;
    IntersectionGeometryPointSequence geometry_points;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(Intersection, id, source, level, lane_ids_within, link_ids_within, geometry_points)

struct LiteIntersection
{
    FeatureIdType id;
    FeatureSourceType source;
    FeatureLevelType level;
    FeatureGeometry2fType position;
    ScalarType distance_to_intersection;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(LiteIntersection, id, source, level, position, distance_to_intersection)

}
using StopLine=object::StopLine;
using Marking=object::Marking;
using Sign=object::Sign;
using Light=object::Light;
using PedestrianCrossing=object::PedestrianCrossing;
using Intersection=object::Intersection;
using LiteIntersection=object::LiteIntersection;
}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::map::object::StopLine, StdLayoutTag>
{
    using ValueType = ::zos::map::object::StopLine;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::map::object::StopLine, StdLayoutTruncTag>
{
    using ValueType = ::zos::map::object::StopLine;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, associated_lane_ids) + sizeof(uint32_t);
        size += sample.associated_lane_ids.size() * sizeof(decltype(sample.associated_lane_ids)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,associated_lane_ids));
        uint32_t des_size = offsetof(ValueType,associated_lane_ids) + element_size * sizeof(decltype(sample.associated_lane_ids)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::map::object::Marking, StdLayoutTag>
{
    using ValueType = ::zos::map::object::Marking;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::map::object::Marking, StdLayoutTruncTag>
{
    using ValueType = ::zos::map::object::Marking;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, semantic_text) + sizeof(uint32_t);
        size += sample.semantic_text.size() * sizeof(decltype(sample.semantic_text)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,semantic_text));
        uint32_t des_size = offsetof(ValueType,semantic_text) + element_size * sizeof(decltype(sample.semantic_text)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::map::object::Sign, StdLayoutTag>
{
    using ValueType = ::zos::map::object::Sign;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::map::object::Sign, StdLayoutTruncTag>
{
    using ValueType = ::zos::map::object::Sign;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, associated_lane_ids) + sizeof(uint32_t);
        size += sample.associated_lane_ids.size() * sizeof(decltype(sample.associated_lane_ids)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,associated_lane_ids));
        uint32_t des_size = offsetof(ValueType,associated_lane_ids) + element_size * sizeof(decltype(sample.associated_lane_ids)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::map::object::SignalCountDown, StdLayoutTag>
{
    using ValueType = ::zos::map::object::SignalCountDown;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::map::object::SignalCountDown, StdLayoutTruncTag>
{
    using ValueType = ::zos::map::object::SignalCountDown;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::map::object::Light, StdLayoutTag>
{
    using ValueType = ::zos::map::object::Light;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::map::object::Light, StdLayoutTruncTag>
{
    using ValueType = ::zos::map::object::Light;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::map::object::PedestrianCrossing, StdLayoutTag>
{
    using ValueType = ::zos::map::object::PedestrianCrossing;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::map::object::PedestrianCrossing, StdLayoutTruncTag>
{
    using ValueType = ::zos::map::object::PedestrianCrossing;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, associated_lane_ids) + sizeof(uint32_t);
        size += sample.associated_lane_ids.size() * sizeof(decltype(sample.associated_lane_ids)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,associated_lane_ids));
        uint32_t des_size = offsetof(ValueType,associated_lane_ids) + element_size * sizeof(decltype(sample.associated_lane_ids)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::map::object::Intersection, StdLayoutTag>
{
    using ValueType = ::zos::map::object::Intersection;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::map::object::Intersection, StdLayoutTruncTag>
{
    using ValueType = ::zos::map::object::Intersection;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, geometry_points) + sizeof(uint32_t);
        size += sample.geometry_points.size() * sizeof(decltype(sample.geometry_points)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,geometry_points));
        uint32_t des_size = offsetof(ValueType,geometry_points) + element_size * sizeof(decltype(sample.geometry_points)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::map::object::LiteIntersection, StdLayoutTag>
{
    using ValueType = ::zos::map::object::LiteIntersection;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::map::object::LiteIntersection, StdLayoutTruncTag>
{
    using ValueType = ::zos::map::object::LiteIntersection;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
