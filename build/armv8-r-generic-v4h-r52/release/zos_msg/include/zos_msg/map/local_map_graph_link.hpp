#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/container/vector.hpp>

#include <zos_msg/map/local_map_graph_common.hpp>
namespace zos
{
namespace map
{
namespace link
{
enum class LinkType
{
    UNKNOWN = 0,
    MAIN = 1,
    AUXILIARY = 2,
    ENTRY_RAMP = 3,
    EXIT_RAMP = 4,
    RAMP = 5,
    JCT = 6,
    SERVICE = 7,
    INTERSECTION = 8,
    ROUNDABOUT = 9,
    PEDESTRIAN = 10,
    PARALLEL = 11,
    TOLLBOOTH = 12,
    OTHERS = 255,
};
enum class LinkClassType
{
    UNKNOWN = 0,
    EXPRESSWAY = 1,
    FREEWAY = 2,
    REGULAR = 3,
    OTHERS = 255,
};
enum class LinkAttributeType
{
    UNKNOWN = 0,
    TUNNEL = 1,
    BRIDGE = 2,
    VIADUCT = 3,
    TOLLGATE = 4,
    TOLLENTRY = 5,
    TOLLEXIT = 6,
    BREAKUP = 7,
    OTHERS = 255,
};
const zos::uint8_t MAX_LANE_SIZE_IN_LINK = 10;
const zos::uint8_t MAX_PRECEDING_SUCCEEDING_LINKS_SIZE = 10;
const zos::uint16_t MAX_LINK_CENTER_LINE_POINTS_SIZE = 200;
using LaneIdSequence=zos::FixedVector<FeatureIdType, MAX_LANE_SIZE_IN_LINK>;
using PrecedingSucceedingLinksIdSequence=zos::FixedVector<FeatureIdType, MAX_PRECEDING_SUCCEEDING_LINKS_SIZE>;
using CenterLineGoemetrySequence=zos::FixedVector<FeatureGeometry2fType, MAX_LINK_CENTER_LINE_POINTS_SIZE>;
struct Link
{
    FeatureIdType id;
    FeatureSourceType source;
    FeatureLevelType level;
    LinkType link_type;
    LinkClassType link_class;
    LinkAttributeType link_attribute;
    CentermeterType length;
    LaneIdSequence lane_ids_in_link;
    PrecedingSucceedingLinksIdSequence preceding_link_ids;
    PrecedingSucceedingLinksIdSequence succeeding_link_ids;
    FeatureIdType most_left_boundary_id;
    FeatureIdType most_right_boundary_id;
    FeatureIdType belonged_intersection_id;
    CenterLineGoemetrySequence center_line;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(Link, id, source, level, link_type, link_class, link_attribute, length, lane_ids_in_link, preceding_link_ids, succeeding_link_ids, most_left_boundary_id, most_right_boundary_id, belonged_intersection_id, center_line)

}
using Link=link::Link;
}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::map::link::Link, StdLayoutTag>
{
    using ValueType = ::zos::map::link::Link;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::map::link::Link, StdLayoutTruncTag>
{
    using ValueType = ::zos::map::link::Link;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        size_t size = offsetof(ValueType, center_line) + sizeof(uint32_t);
        size += sample.center_line.size() * sizeof(decltype(sample.center_line)::value_type);
        return size;
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t element_size = *reinterpret_cast<uint32_t const*>(buffer + offsetof(ValueType,center_line));
        uint32_t des_size = offsetof(ValueType,center_line) + element_size * sizeof(decltype(sample.center_line)::value_type);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
