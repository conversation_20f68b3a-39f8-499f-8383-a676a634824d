#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>

#include <zos_msg/common/common.hpp>
#include <zos_msg/common/timestamp.hpp>
#include <zos_msg/geometry/pose.hpp>
#include <zos_msg/geometry/box.hpp>
#include <zos_msg/numerics/vector.hpp>
namespace zos
{
namespace map
{
using ScalarType=zos::float32_t;
using FeatureIdType=zos::uint64_t;
using CentermeterType=zos::uint32_t;
using FeatureGeometry3dType=::zos::geometry::Point3d;
using FeatureGeometry2dType=::zos::geometry::Point2d;
using FeatureGeometry3fType=::zos::geometry::Point3f;
using FeatureGeometry2fType=::zos::geometry::Point2f;
enum class FeatureSourceType
{
    UNKNOWN = 0,
    HD = 1,
    SD = 2,
    SDP = 3,
    IBM = 4,
};
enum class FeatureLevelType
{
    UNKNOWN = 0,
    GOOD = 1,
    MODERATE = 2,
    INCOMPLETE = 3,
    UNUSABLE = 4,
    OUTDATED = 5,
};
namespace interval_base
{
enum class IntervalOffsetType
{
    UNKNOWN = 0,
    PERCENT = 1,
    INDEX = 2,
};
using OffsetValueType=zos::float32_t;
struct IntervalBase
{
    IntervalOffsetType offset_type;
    OffsetValueType start_offset;
    OffsetValueType end_offset;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(IntervalBase, offset_type, start_offset, end_offset)

}
using IntervalBase=zos::map::interval_base::IntervalBase;
}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::map::interval_base::IntervalBase, StdLayoutTag>
{
    using ValueType = ::zos::map::interval_base::IntervalBase;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::map::interval_base::IntervalBase, StdLayoutTruncTag>
{
    using ValueType = ::zos::map::interval_base::IntervalBase;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
