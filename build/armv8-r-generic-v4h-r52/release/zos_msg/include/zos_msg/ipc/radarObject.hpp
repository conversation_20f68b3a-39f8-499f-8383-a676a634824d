#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>
#include <basic_types/container/array.hpp>

#include <zos_msg/common/timestamp.hpp>
namespace zos
{
namespace ipc
{
const zos::uint32_t G_RADAR_NUMBER_U32 = 40;
enum class ERadarObjectType
{
    RADAR_OBJECT_TYPE_UNFILLED = 0,
    RADAR_OBJECT_TYPE_WHEELER4 = 1,
    RADAR_OBJECT_TYPE_WHEELER2 = 2,
    RADAR_OBJECT_TYPE_PEDESTRIAN = 3,
    RADAR_OBJECT_TYPE_CAR = 4,
    RADAR_OBJECT_TYPE_TRUCK = 5,
    RADAR_OBJECT_TYPE_HIGH_ALTITUDE_TARGET = 6,
};
enum class ERadarMotionStatus
{
    RADAR_MOTION_STATUS_UNFILLED = 0,
    RADAR_MOTION_STATUS_STATIONARY = 1,
    RADAR_MOTION_STATUS_STOPPED = 2,
    RADAR_MOTION_STATUS_MOVING = 3,
};
enum class ERadarObjectDetStatus
{
    RADAR_OBJ_DET_DELETED = 0,
    RADAR_OBJ_DET_NEW = 1,
    RADAR_OBJ_DET_MEASURED = 2,
    RADAR_OBJ_DET_PREDICTED = 3,
    RADAR_OBJ_DET_DELETED_MERGE = 4,
    RADAR_OBJ_DET_NEW_MERGE = 5,
};
struct CRadarObj
{
    zos::uint16_t m_id;
    zos::uint8_t m_lifeCycles;
    zos::bool_t m_isValid;
    zos::float32_t m_dx;
    zos::float32_t m_dxStd;
    zos::float32_t m_dy;
    zos::float32_t m_dyStd;
    zos::float32_t m_vx;
    zos::float32_t m_vxStd;
    zos::float32_t m_vy;
    zos::float32_t m_vyStd;
    zos::float32_t m_ax;
    zos::float32_t m_axStd;
    zos::float32_t m_ay;
    zos::float32_t m_ayStd;
    zos::float32_t m_length;
    zos::float32_t m_width;
    zos::float32_t m_heading;
    zos::float32_t m_existProb;
    zos::float32_t m_obstacleProb;
    ERadarObjectType m_radarObjType;
    ERadarMotionStatus m_radarMotionStatus;
    ERadarObjectDetStatus m_radarDetStatus;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CRadarObj, m_id, m_lifeCycles, m_isValid, m_dx, m_dxStd, m_dy, m_dyStd, m_vx, m_vxStd, m_vy, m_vyStd, m_ax, m_axStd, m_ay, m_ayStd, m_length, m_width, m_heading, m_existProb, m_obstacleProb, m_radarObjType, m_radarMotionStatus, m_radarDetStatus)

enum class ERadarSensorType
{
    RADAR_TYPE_UNKNOWN = 0,
    RADAR_TYPE_FONT_RADAR = 1,
    RADAR_TYPE_REAR_RADAR = 2,
    RADAR_TYPE_FONT_LEFT_RADAR = 3,
    RADAR_TYPE_FONT_RIGHT_RADAR = 4,
    RADAR_TYPE_REAR_LEFT_RADAR = 5,
    RADAR_TYPE_REAR_RIGHT_RADAR = 6,
};
struct CRadarStatus
{
    zos::bool_t m_isRadarFailure;
    zos::bool_t m_isRadarBlindness;
    zos::uint8_t m_RadarObjNum;
    ERadarSensorType m_RadarSensorType;
    zos::common::Timestamp m_RadarObjTimestamp;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CRadarStatus, m_isRadarFailure, m_isRadarBlindness, m_RadarObjNum, m_RadarSensorType, m_RadarObjTimestamp)

using RadarObjArray=zos::Array<CRadarObj, G_RADAR_NUMBER_U32>;
struct CRadarInfo
{
    CRadarStatus m_RadarStatus;
    RadarObjArray m_RadarObj;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CRadarInfo, m_RadarStatus, m_RadarObj)

using RadarInfoArray=zos::Array<CRadarInfo, 5>;
struct radarObject
{
    zos::common::Timestamp m_sampleTimestamp;
    zos::uint32_t m_seq;
    CRadarObj m_BSDLCWLeftRadarObj;
    CRadarObj m_BSDLCWRightRadarObj;
    RadarInfoArray m_RadarInfo;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(radarObject, m_sampleTimestamp, m_seq, m_BSDLCWLeftRadarObj, m_BSDLCWRightRadarObj, m_RadarInfo)

}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::ipc::CRadarObj, StdLayoutTag>
{
    using ValueType = ::zos::ipc::CRadarObj;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CRadarObj, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::CRadarObj;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CRadarStatus, StdLayoutTag>
{
    using ValueType = ::zos::ipc::CRadarStatus;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CRadarStatus, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::CRadarStatus;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CRadarInfo, StdLayoutTag>
{
    using ValueType = ::zos::ipc::CRadarInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CRadarInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::CRadarInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::radarObject, StdLayoutTag>
{
    using ValueType = ::zos::ipc::radarObject;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::radarObject, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::radarObject;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
