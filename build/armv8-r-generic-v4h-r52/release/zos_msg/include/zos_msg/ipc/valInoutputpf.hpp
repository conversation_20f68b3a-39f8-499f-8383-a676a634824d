#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>

#include <zos_msg/common/common.hpp>
#include <zos_msg/common/timestamp.hpp>
namespace zos
{
namespace ipc
{
enum class EGearBoxType
{
    GEARBOX_NONE = 0,
    GEARBOX_MANUAL = 1,
    GEARBOX_AUTOMATIC = 2,
    GEARBOX_ERROR = 3,
};
enum class EGearStatus
{
    GEAR_PARK = 0,
    GEAR_REVERSE = 1,
    GEAR_NEUTRAL = 2,
    GEAR_DRIVE = 3,
    GEAR_NOSIGNAL = 4,
};
struct CPfGear
{
    EGearBoxType m_gearBoxType;
    EGearStatus m_gearStatus;
    EGearStatus m_TargetGearStatus;
    zos::bool_t m_APAGearIntervention;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CPfGear, m_gearBoxType, m_gearStatus, m_TargetGearStatus, m_APAGearIntervention)

enum class EStatusProtFlapFront
{
    PROT_FLAP_FRONT_UNCOV = 0,
    PROT_FLAP_FRONT_COV = 1,
    PROT_FLAP_FRONT_MOV = 2,
    PROT_FLAP_FRONT_INVALID = 15,
};
enum class EStatusProtFlapRear
{
    PROT_FLAP_REAR_UNCOV = 0,
    PROT_FLAP_REAR_COV = 1,
    PROT_FLAP_REAR_MOV = 2,
    PROT_FLAP_REAR_INVALID = 15,
};
enum class EStatusRearCamPosition
{
    REARCAM_POSITION_PRIMARY = 0,
    REARCAM_POSITION_SECONDARY = 1,
    REARCAM_POSITION_MOVING = 2,
    REARCAM_POSITION_INVALID = 15,
};
enum class EStatusFrontCamPosition
{
    FRONTCAM_POSITION_PRIMARY = 0,
    FRONTCAM_POSITION_SECONDARY = 1,
    FRONTCAM_POSITION_MOVING = 2,
    FRONTCAM_POSITION_INVALID = 15,
};
struct CPfCamera
{
    EStatusProtFlapFront m_statusProtectionFlapFront;
    EStatusProtFlapRear m_statusProtectionFlapRear;
    EStatusRearCamPosition m_statusRearCamPosition;
    EStatusFrontCamPosition m_statusFrontCamPosition;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CPfCamera, m_statusProtectionFlapFront, m_statusProtectionFlapRear, m_statusRearCamPosition, m_statusFrontCamPosition)

enum class EStateDoorSwitchFL
{
    DOOR_FL_OPEN = 0,
    DOOR_FL_CLOSED = 1,
    DOOR_FL_INVALID = 15,
};
enum class ESrfOperateSts
{
    STARTUP = 0,
    TILT = 1,
    CLOSE = 2,
    OPENING = 3,
    STOP = 4,
    CLOSING = 5,
    OPEN = 6,
    COMFORT_OPEN = 7,
};
enum class EStateDoorSwitchFR
{
    DOOR_FR_OPEN = 0,
    DOOR_FR_CLOSED = 1,
    DOOR_FR_INVALID = 15,
};
enum class EStateDoorSwitchRL
{
    DOOR_RL_OPEN = 0,
    DOOR_RL_CLOSED = 1,
    DOOR_RL_INVALID = 15,
};
enum class EStateDoorSwitchRR
{
    DOOR_RR_OPEN = 0,
    DOOR_RR_CLOSED = 1,
    DOOR_RR_INVALID = 15,
};
enum class EStatusBootLid
{
    BOOT_LID_OPEN = 0,
    BOOT_LID_CLOSED = 1,
    BOOT_LID_INVALID = 15,
};
enum class EStatusFrontLid
{
    FRONT_LID_OPEN = 0,
    FRONT_LID_CLOSED = 1,
    FRONT_LID_INVALID = 15,
};
enum class EStateMirror
{
    MIRROR_UNFOLDED = 0,
    MIRROR_FOLDED = 1,
    MIRROR_UNFOLDING = 2,
    MIRROR_FOLDING = 3,
    MIRROR_INVALID = 15,
};
enum class EMirrFldState
{
    MIRROR_FLD_IDLE = 0,
    MIRROR_FLD_PARKPOS = 1,
    MIRROR_FLD_DRVPOS = 2,
    MIRROR_FLD_FOLD = 3,
};
struct CPfDoorAndMirror
{
    EStateDoorSwitchFL m_stateDoorSwitchFrontLeft;
    EStateDoorSwitchFR m_stateDoorSwitchFrontRight;
    EStateDoorSwitchRL m_stateDoorSwitchRearLeft;
    EStateDoorSwitchRR m_stateDoorSwitchRearRight;
    EStateMirror m_stateExteriorMirrorLeft;
    EStateMirror m_stateExteriorMirrorRight;
    EMirrFldState m_mirrFldState;
    EStatusBootLid m_statusContactBootLid;
    EStatusFrontLid m_statusContactFrontLid;
    ESrfOperateSts m_SrfOperateSts;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CPfDoorAndMirror, m_stateDoorSwitchFrontLeft, m_stateDoorSwitchFrontRight, m_stateDoorSwitchRearLeft, m_stateDoorSwitchRearRight, m_stateExteriorMirrorLeft, m_stateExteriorMirrorRight, m_mirrFldState, m_statusContactBootLid, m_statusContactFrontLid, m_SrfOperateSts)

enum class EVehicleDrvDir
{
    DRVDIR_VOID = 0,
    DRVDIR_FWD = 1,
    DRVDIR_BWD = 2,
    DRVDIR_UNKNOWN = 15,
};
enum class EQualifierVehicleVelocity
{
    Q_VEHICLE_VELOCITY_VALID = 0,
    Q_VEHICLE_VELOCITY_INVALID = 15,
};
enum class EWheelDrvDir
{
    WHEEL_DRV_DIR_FWD = 0,
    WHEEL_DRV_DIR_BWD = 1,
    WHEEL_DRV_DIR_UNKNOWN = 2,
    WHEEL_DRV_DIR_STOP = 3,
};
enum class EWheelRotationQualifier
{
    Q_WHEEL_ROTATION_VALID = 0,
    Q_WHEEL_ROTATION_INVALID = 15,
};
enum class ELateralAccQualifier
{
    Q_LATERAL_ACC_VALID = 0,
    Q_LATERAL_ACC_INVALID = 15,
};
enum class ELongAccQualifier
{
    Q_LONG_ACC_VALID = 0,
    Q_LONG_ACC_INVALID = 15,
};
enum class EQualifierYawVelocityVehicle
{
    Q_YAW_VELOCITY_VEH_VALID = 0,
    Q_YAW_VELOCITY_VEH_INVALID = 15,
};
struct CPfOdometry
{
    zos::float32_t m_vehicleVelocity;
    zos::float32_t m_vehicleVelocityDisplay;
    zos::float32_t m_brktrq;
    zos::float32_t m_longitudinalAcceleration;
    zos::float32_t m_lateralAcceleration;
    zos::float32_t m_yawRate;
    zos::common::Timestamp m_wheelImpCtrFLTimestamp;
    zos::common::Timestamp m_wheelImpCtrFRTimestamp;
    zos::common::Timestamp m_wheelImpCtrRLTimestamp;
    zos::common::Timestamp m_wheelImpCtrRRTimestamp;
    zos::uint16_t m_wheelImpCtrFL;
    zos::uint16_t m_wheelImpCtrFR;
    zos::uint16_t m_wheelImpCtrRL;
    zos::uint16_t m_wheelImpCtrRR;
    EVehicleDrvDir m_vehicleDrvDir;
    EQualifierVehicleVelocity m_qualifierVehicleVelocity;
    EWheelDrvDir m_wheelDrvDirFL;
    EWheelDrvDir m_wheelDrvDirFR;
    EWheelDrvDir m_wheelDrvDirRL;
    EWheelDrvDir m_wheelDrvDirRR;
    EWheelRotationQualifier m_wheelRotationFLQualifier;
    EWheelRotationQualifier m_wheelRotationFRQualifier;
    EWheelRotationQualifier m_wheelRotationRLQualifier;
    EWheelRotationQualifier m_wheelRotationRRQualifier;
    ELateralAccQualifier m_lateralAccelerationQualifier;
    ELongAccQualifier m_longitudinalAccelerationQualifier;
    EQualifierYawVelocityVehicle m_qualifierYawVelocityVehicle;
    zos::float32_t m_PboxImuAcc_X;
    zos::float32_t m_PboxImuAcc_Y;
    zos::float32_t m_PboxImuAcc_Z;
    zos::float32_t m_PboxImuGyro_X;
    zos::float32_t m_PboxImuGyro_Y;
    zos::float32_t m_PboxImuGyro_Z;
    zos::float32_t m_PitchInstallAngle_f32;
    zos::float32_t m_reserve1_f32;
    zos::float32_t m_reserve2_f32;
    zos::float32_t m_reserve3_f32;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CPfOdometry, m_vehicleVelocity, m_vehicleVelocityDisplay, m_brktrq, m_longitudinalAcceleration, m_lateralAcceleration, m_yawRate, m_wheelImpCtrFLTimestamp, m_wheelImpCtrFRTimestamp, m_wheelImpCtrRLTimestamp, m_wheelImpCtrRRTimestamp, m_wheelImpCtrFL, m_wheelImpCtrFR, m_wheelImpCtrRL, m_wheelImpCtrRR, m_vehicleDrvDir, m_qualifierVehicleVelocity, m_wheelDrvDirFL, m_wheelDrvDirFR, m_wheelDrvDirRL, m_wheelDrvDirRR, m_wheelRotationFLQualifier, m_wheelRotationFRQualifier, m_wheelRotationRLQualifier, m_wheelRotationRRQualifier, m_lateralAccelerationQualifier, m_longitudinalAccelerationQualifier, m_qualifierYawVelocityVehicle, m_PboxImuAcc_X, m_PboxImuAcc_Y, m_PboxImuAcc_Z, m_PboxImuGyro_X, m_PboxImuGyro_Y, m_PboxImuGyro_Z, m_PitchInstallAngle_f32, m_reserve1_f32, m_reserve2_f32, m_reserve3_f32)

enum class EVehicleStopState
{
    VEHICLE_STOP_STATE_NOT_DETECTED = 0,
    VEHICLE_STOP_STATE_DETECTED = 1,
    VEHICLE_STOP_STATE_NOT_UNKNOWN = 15,
};
struct CPfPmaFixedPoint
{
    zos::uint16_t m_vehSpeed;
    zos::float32_t m_steeringWheelAngle;
    zos::uint16_t m_wheelRotationFL;
    zos::uint16_t m_wheelRotationFR;
    zos::uint16_t m_wheelRotationRL;
    zos::uint16_t m_wheelRotationRR;
    zos::float32_t m_wheelSpeedFL;
    zos::float32_t m_wheelSpeedFR;
    zos::float32_t m_wheelSpeedRL;
    zos::float32_t m_wheelSpeedRR;
    zos::int8_t m_outsideTemp;
    EVehicleStopState m_vehicleStopState;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CPfPmaFixedPoint, m_vehSpeed, m_steeringWheelAngle, m_wheelRotationFL, m_wheelRotationFR, m_wheelRotationRL, m_wheelRotationRR, m_wheelSpeedFL, m_wheelSpeedFR, m_wheelSpeedRL, m_wheelSpeedRR, m_outsideTemp, m_vehicleStopState)

enum class EQualifierSteeringAngle
{
    STEERING_ANGLE_VALID = 0,
    STEERING_ANGLE_INVALID = 15,
};
enum class EFrontWheelAngleStatus
{
    FWA_HIGH_PRECISION = 0,
    FWA_LOW_PRECISION = 1,
    FWA_NOT_INIT = 2,
};
enum class EEpsStatus
{
    EPS_NOT_PRESENT = 0,
    EPS_INACTIVE = 1,
    EPS_READY = 2,
    EPS_ACTIVE = 3,
};
enum class EEpsStatusRas
{
    EPS_RAS_NOT_PRESENT = 0,
    EPS_RAS_INACTIVE = 1,
    EPS_RAS_READY = 2,
    EPS_RAS_ACTIVE = 3,
};
enum class EQualifierRearAngle
{
    REAR_STEERING_ANGLE_INVALID = 0,
    REAR_STEERING_ANGLE_VALID = 1,
    REAR_STEERING_ANGLE_ERR = 2,
};
struct CPfSteering
{
    zos::float32_t m_frontWheelAngle;
    zos::float32_t m_frontWheelAngleOffset;
    zos::float32_t m_steeringWheelAngle;
    zos::float32_t m_steeringWheelAngleSpd;
    zos::float32_t m_rearAxleSteeringAngle;
    zos::bool_t m_frontWheelAngleValid;
    zos::bool_t m_frontWheelAngleOffsetValid;
    zos::bool_t m_rearAxleSteeringAngleValid;
    EQualifierSteeringAngle m_qualifierSteeringAngle;
    EFrontWheelAngleStatus m_frontWheelAngleStatus;
    zos::float32_t m_steerWhlTrq;
    zos::float32_t m_steerWhlTrqOffset;
    zos::float32_t m_steerWhlTrqHys;
    zos::float32_t m_steerWhlRimTrqHys;
    EEpsStatus m_epsStatus;
    zos::common::Timestamp m_frontWheelAngleTimestamp;
    EEpsStatusRas m_epsStatusRas;
    EQualifierRearAngle m_rearWheelAngleStatus;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CPfSteering, m_frontWheelAngle, m_frontWheelAngleOffset, m_steeringWheelAngle, m_steeringWheelAngleSpd, m_rearAxleSteeringAngle, m_frontWheelAngleValid, m_frontWheelAngleOffsetValid, m_rearAxleSteeringAngleValid, m_qualifierSteeringAngle, m_frontWheelAngleStatus, m_steerWhlTrq, m_steerWhlTrqOffset, m_steerWhlTrqHys, m_steerWhlRimTrqHys, m_epsStatus, m_frontWheelAngleTimestamp, m_epsStatusRas, m_rearWheelAngleStatus)

struct CPfTrailer
{
    zos::bool_t m_trailerState;
    zos::bool_t m_trailerHitchPresent;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CPfTrailer, m_trailerState, m_trailerHitchPresent)

enum class EQualifierVehicleLevel
{
    VEHICLE_LEVEL_VALID = 0,
    VEHICLE_LEVEL_INVALID = 15,
};
struct CPfVehicleLevel
{
    zos::float32_t m_relativeVehicleLevel;
    zos::float32_t m_vehicleLevelFL;
    zos::float32_t m_vehicleLevelFR;
    zos::float32_t m_vehicleLevelRL;
    zos::float32_t m_vehicleLevelRR;
    EQualifierVehicleLevel m_qualifierVehicleLevel;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CPfVehicleLevel, m_relativeVehicleLevel, m_vehicleLevelFL, m_vehicleLevelFR, m_vehicleLevelRL, m_vehicleLevelRR, m_qualifierVehicleLevel)

enum class EConfig
{
    VIN_LENGTH = 17,
};
enum class EIgnSwState
{
    IGN_SW_ST_LOCK = 0,
    IGN_SW_ST_OFF = 1,
    IGN_SW_ST_ACC = 2,
    IGN_SW_ST_ON = 3,
    IGN_SW_ST_START = 4,
    IGN_SW_ST_SNA = 7,
};
enum class EIndicatorStatus
{
    INDICATE_RIGHT = 0,
    INDICATE_LEFT = 1,
    INDICATE_IDLE = 2,
    INDICATE_WARNING = 3,
};
enum class EWiperSts
{
    WIPPER_OFF = 0,
    WIPPER_INTERVAL = 1,
    WIPPER_LOW = 2,
    WIPPER_HIGH = 3,
};
enum class EEngineStart
{
    ENGINE_NO_START = 0,
    ENGINE_FIRST_START = 1,
    ENGINE_RESTART = 2,
    ENGINE_START_SNA = 255,
};
enum class EDriveMode
{
    VEH_NORMAL_MODE = 0,
    VEH_SPORT_MODE = 1,
    VEH_ECO_MODE = 2,
    VEH_SNOW_MODE = 3,
    VEH_MUD_MODE = 4,
    VEH_OFFROAD_MODE = 5,
    VEH_SAND_MODE = 6,
    VEH_INVALID = 255,
};
enum class EBackgroundLightState
{
    BG_LIGHT_OFF = 0,
    BG_LIGHT_ON = 1,
    BG_LIGHT_RESERVED = 2,
    BG_LIGHT_INVALID = 3,
};
enum class EDayNightModeState
{
    DAY_NIGHT_MODE_DAY = 0,
    DAY_NIGHT_MODE_NIGHT = 1,
    DAY_NIGHT_MODE_RESERVED = 2,
    DAY_NIGHT_MODE_INVALID = 3,
};
struct CPfVehicleInfo
{
    zos::uint8_t m_uBatt;
    zos::bool_t m_KL15_status;
    EIgnSwState m_ignSwState;
    EIndicatorStatus m_indicatorStatus;
    zos::bool_t m_lowBeamLeft;
    zos::bool_t m_lowBeamRight;
    zos::bool_t m_highBeamLeft;
    zos::bool_t m_highBeamRight;
    zos::bool_t m_fogLampFront;
    zos::bool_t m_fogLampRear;
    zos::bool_t m_brkLightAct;
    zos::bool_t m_DriverSeatBeltNotBuckled;
    zos::bool_t m_externalFuncAct;
    zos::bool_t m_chargeState;
    EEngineStart m_engineStart;
    zos::bool_t m_engineIsRunning;
    zos::float32_t m_accPedalPosition;
    zos::bool_t m_accPedalIntervened;
    EDriveMode m_VehDriveMode;
    EBackgroundLightState m_bgLightState;
    EDayNightModeState m_dayNightModeState;
    zos::float32_t m_motorTorqFeedback_nm;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CPfVehicleInfo, m_uBatt, m_KL15_status, m_ignSwState, m_indicatorStatus, m_lowBeamLeft, m_lowBeamRight, m_highBeamLeft, m_highBeamRight, m_fogLampFront, m_fogLampRear, m_brkLightAct, m_DriverSeatBeltNotBuckled, m_externalFuncAct, m_chargeState, m_engineStart, m_engineIsRunning, m_accPedalPosition, m_accPedalIntervened, m_VehDriveMode, m_bgLightState, m_dayNightModeState, m_motorTorqFeedback_nm)

enum class ETirePressQualifier
{
    Q_TIRE_PRESS_VALID = 0,
    Q_TIRE_PRESS_INVALID = 15,
};
struct CPfTirePress
{
    zos::float32_t m_tirePressFL;
    zos::float32_t m_tirePressFR;
    zos::float32_t m_tirePressRL;
    zos::float32_t m_tirePressRR;
    ETirePressQualifier m_tirePressQualifier;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CPfTirePress, m_tirePressFL, m_tirePressFR, m_tirePressRL, m_tirePressRR, m_tirePressQualifier)

enum class EEspOpMode
{
    ABS_ASR_ESP_CTRL_ACTIVE = 0,
    ABS_ASR_ESP_CTRL_INACTIVE = 1,
};
enum class EPrkgBrkState
{
    PRKG_BRK_NOTAPPLIED = 0,
    PRKG_BRK_APPLIED_STATIC = 1,
    PRKG_BRK_APPLIED_DYNAMIC = 2,
};
enum class EBrkPedlState
{
    BRK_PEDL_NOTPSD = 0,
    BRK_PEDL_PSD = 1,
    BRK_PEDL_INVALID = 255,
};
struct CPfBrakes
{
    EEspOpMode m_espOpMode;
    zos::bool_t m_absActive;
    zos::bool_t m_aebActive;
    EPrkgBrkState m_prkgBrkState;
    EBrkPedlState m_brkPedlState;
    zos::float32_t m_masterCylinderPress;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CPfBrakes, m_espOpMode, m_absActive, m_aebActive, m_prkgBrkState, m_brkPedlState, m_masterCylinderPress)

enum class EDynamometerMode
{
    DYN_MODE_OFF = 0,
    DYN_MODE_ON = 1,
    DYN_MODE_INVALID = 15,
};
struct CPfEnvironment
{
    zos::uint32_t m_absoluteTimeUnix;
    zos::uint64_t m_steeringWheelAngleSequenceCounter;
    EDynamometerMode m_dynamometerMode;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CPfEnvironment, m_absoluteTimeUnix, m_steeringWheelAngleSequenceCounter, m_dynamometerMode)

struct CPfHil
{
    zos::uint8_t m_hilMode;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CPfHil, m_hilMode)

struct ValInOutputPf
{
    CPfGear m_pfGear;
    CPfCamera m_pfCamera;
    CPfDoorAndMirror m_pfDoorAndMirror;
    CPfOdometry m_pfOdometry;
    CPfPmaFixedPoint m_pfPmaFixedPoint;
    CPfSteering m_pfSteering;
    CPfTrailer m_pfTrailer;
    CPfVehicleLevel m_pfVehicleLevel;
    CPfVehicleInfo m_pfVehicleInfo;
    CPfTirePress m_pfTirePress;
    CPfBrakes m_pfBrakes;
    CPfEnvironment m_pfEnvironment;
    CPfHil m_pfHil;
    zos::common::Timestamp m_senderCallTimestamp;
    zos::uint64_t m_timeStampVehicleUtcUs;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(ValInOutputPf, m_pfGear, m_pfCamera, m_pfDoorAndMirror, m_pfOdometry, m_pfPmaFixedPoint, m_pfSteering, m_pfTrailer, m_pfVehicleLevel, m_pfVehicleInfo, m_pfTirePress, m_pfBrakes, m_pfEnvironment, m_pfHil, m_senderCallTimestamp, m_timeStampVehicleUtcUs)

}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::ipc::CPfGear, StdLayoutTag>
{
    using ValueType = ::zos::ipc::CPfGear;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CPfGear, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::CPfGear;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CPfCamera, StdLayoutTag>
{
    using ValueType = ::zos::ipc::CPfCamera;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CPfCamera, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::CPfCamera;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CPfDoorAndMirror, StdLayoutTag>
{
    using ValueType = ::zos::ipc::CPfDoorAndMirror;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CPfDoorAndMirror, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::CPfDoorAndMirror;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CPfOdometry, StdLayoutTag>
{
    using ValueType = ::zos::ipc::CPfOdometry;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CPfOdometry, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::CPfOdometry;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CPfPmaFixedPoint, StdLayoutTag>
{
    using ValueType = ::zos::ipc::CPfPmaFixedPoint;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CPfPmaFixedPoint, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::CPfPmaFixedPoint;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CPfSteering, StdLayoutTag>
{
    using ValueType = ::zos::ipc::CPfSteering;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CPfSteering, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::CPfSteering;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CPfTrailer, StdLayoutTag>
{
    using ValueType = ::zos::ipc::CPfTrailer;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CPfTrailer, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::CPfTrailer;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CPfVehicleLevel, StdLayoutTag>
{
    using ValueType = ::zos::ipc::CPfVehicleLevel;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CPfVehicleLevel, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::CPfVehicleLevel;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CPfVehicleInfo, StdLayoutTag>
{
    using ValueType = ::zos::ipc::CPfVehicleInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CPfVehicleInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::CPfVehicleInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CPfTirePress, StdLayoutTag>
{
    using ValueType = ::zos::ipc::CPfTirePress;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CPfTirePress, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::CPfTirePress;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CPfBrakes, StdLayoutTag>
{
    using ValueType = ::zos::ipc::CPfBrakes;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CPfBrakes, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::CPfBrakes;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CPfEnvironment, StdLayoutTag>
{
    using ValueType = ::zos::ipc::CPfEnvironment;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CPfEnvironment, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::CPfEnvironment;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CPfHil, StdLayoutTag>
{
    using ValueType = ::zos::ipc::CPfHil;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CPfHil, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::CPfHil;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::ValInOutputPf, StdLayoutTag>
{
    using ValueType = ::zos::ipc::ValInOutputPf;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::ValInOutputPf, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::ValInOutputPf;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
