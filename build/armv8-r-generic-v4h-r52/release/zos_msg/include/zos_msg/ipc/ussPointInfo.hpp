#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>
#include <basic_types/container/array.hpp>

#include <zos_msg/common/common.hpp>
#include <zos_msg/geometry/point.hpp>
#include <zos_msg/common/timestamp.hpp>
namespace zos
{
namespace ipc
{
struct imo_Type_LineSegment_st
{
    zos::geometry::Point2f P1_st;
    zos::geometry::Point2f P2_st;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(imo_Type_LineSegment_st, P1_st, P2_st)

enum class imo_Type_ObjGen_ObjType_en
{
    imo_OT_NONE_enm = 0,
    imo_OT_DE_enm = 1,
    imo_OT_DE_DE_enm = 2,
    imo_OT_CE_enm = 3,
    imo_OT_DE_CE_enm = 4,
    imo_OT_ROUND_POST_enm = 5,
    imo_OT_WALL_enm = 6,
};
struct imo_Type_SensorSource_st
{
    zos::uint8_t TxSensorID_u8;
    zos::uint8_t RxSensorID_u8;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(imo_Type_SensorSource_st, TxSensorID_u8, RxSensorID_u8)

struct imo_Type_measObjectMainInfo_st
{
    imo_Type_LineSegment_st Coords_st;
    zos::uint16_t DETx_u16;
    zos::uint16_t DERx_u16;
    zos::uint16_t CE_u16;
    imo_Type_ObjGen_ObjType_en measObjType_en;
    zos::uint16_t DETxTraceID_u16;
    zos::uint16_t DERxTraceID_u16;
    zos::uint16_t CETraceID_u16;
    imo_Type_SensorSource_st SensorSource_st;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(imo_Type_measObjectMainInfo_st, Coords_st, DETx_u16, DERx_u16, CE_u16, measObjType_en, DETxTraceID_u16, DERxTraceID_u16, CETraceID_u16, SensorSource_st)

struct imo_Type_ObjGen_measObjHeightInfo_st
{
    zos::uint8_t Probability_u8;
    zos::uint8_t Certainty_u8;
    zos::uint8_t Estimation_u8;
    zos::uint8_t MaxError_u8;
    zos::uint8_t CeilingBeamProb_u8;
    zos::bool_t CeilingBeamProbUncertain_b;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(imo_Type_ObjGen_measObjHeightInfo_st, Probability_u8, Certainty_u8, Estimation_u8, MaxError_u8, CeilingBeamProb_u8, CeilingBeamProbUncertain_b)

enum class imo_Type_HeightClassSpecialCase_en
{
    imo_HCSpecialCase_None_enm = 0,
    imo_HCSpecialCase_NearRange_enm = 1,
    imo_HCSpecialCase_DeplausLow_enm = 2,
    imo_HCSpecialCase_PotentialLowWall_enm = 3,
    imo_HCSpecialCase_HeightEstimation_enm = 4,
    imo_HCSpecialCase_PatternDetected_enm = 5,
    imo_HCSpecialCase_DynamicObject_enm = 6,
    imo_HCSpecialCase_MixedReflectPntWall_enm = 7,
    imo_HCSpecialCase_StablePntOrShortWall_enm = 8,
    imo_HCSpecialCase_HeightFreezeForever_enm = 9,
    imo_HCSpecialCase_D2dGroundRefleciton_enm = 10,
    imo_HCSpecialCase_NoRealPost_enm = 11,
    imo_HCSpecialCase_FragileObjectAlwaysLow = 12,
    imo_HCSpecialCase_LowWallByStatisticObjType = 13,
    imo_HCSpecialCase_GarageEntry_enm = 14,
    imo_HCSpecialCase_GarageEdge_enm = 15,
    imo_HCSpecialCase_AmplitudeFilter_enm = 16,
    imo_HCSpecialCase_AmplitudeFilterLow_enm = 17,
    imo_HCSpecialCase_AmplitudeFilterHigh_enm = 18,
    imo_HCSpecialCase_DoubleReflection1_enm = 19,
    imo_HCSpecialCase_DoubleReflection2_enm = 20,
    imo_HCSpecialCase_PotentialGarageEntry_enm = 21,
    imo_HCSpecialCase_SmallWallNotTraversable_enm = 22,
    imo_HCSpecialCase_LowClutterDecisionTreeTowardsLow_enm = 23,
    imo_HCSpecialCase_LowClutterDecisionTreeTowardsHigh_enm = 24,
    imo_HCSpecialCase_LowClutterDeplaus_enm = 25,
    imo_HCSpecialCase_LowObjByFreeEntry_enm = 26,
    imo_HCSpecialCase_Pedestrian_enm = 27,
    imo_HCSpecialCase_LowNearObjColCrit_enm = 28,
    imo_HCSpecialCase_PolebyGrID_enm = 29,
    imo_HCSpecialCase_PedestrianWrnBrk_enm = 30,
};
enum class imo_Type_ObjType_en
{
    imo_OTNone_enm = 0,
    imo_OTPoint_enm = 1,
    imo_OTStraight_0Corner_enm = 2,
    imo_OTStraight_1Corner_enm = 3,
    imo_OTStraight_2Corner_enm = 4,
    imo_OTCurbstone_enm = 5,
    imo_OTBlind_enm = 6,
    imo_OTRoadMarking_enm = 7,
    imo_OTObstacle_enm = 8,
};
enum class imo_Type_HeightStatus_en
{
    imo_HS_Traversable_enm = 0,
    imo_HS_Low_enm = 1,
    imo_HS_Unknown_enm = 2,
    imo_HS_High_enm = 3,
    imo_HS_BodyTraversable_enm = 4,
};
enum class imo_Type_ObjGen_updatePattern_en
{
    imo_updatePattern_FullUpdate_enm = 0,
    imo_updatePattern_UpdateButNoPos_enm = 1,
    imo_updatePattern_UpdateButNoPosNoOVFType_enm = 2,
    imo_updatePattern_UpdateWithSingleEchoDE_enm = 3,
    imo_updatePattern_UpdateWithSingleEchoCE_enm = 4,
    imo_updatePattern_UpdateOnlyPos_enm = 5,
};
struct imo_Type_MapObj2MeasObjInfoMapping_st
{
    imo_Type_ObjType_en MapObjType_en;
    imo_Type_HeightStatus_en MapObjHeightStatus_en;
    imo_Type_HeightClassSpecialCase_en MapObjHeightSpecialCase_en;
    zos::float32_t DeD2DModelCoeff_f32;
    zos::float32_t CeD2DModelCoeff_f32;
    zos::uint16_t DeD2DModelCtr_u16;
    zos::uint16_t CeD2DModelCtr_u16;
    zos::uint16_t MapObjID_u16;
    zos::uint8_t MapHeightProb_u8;
    zos::bool_t MapObjDynamic_b;
    zos::int8_t MapVelocityX_s8;
    zos::int8_t MapVelocityY_s8;
    imo_Type_HeightStatus_en MapObjHeightStatus_tpopt_en;
    imo_Type_ObjGen_updatePattern_en UpdatePattern_en;
    zos::uint8_t numObjUpdates_u8;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(imo_Type_MapObj2MeasObjInfoMapping_st, MapObjType_en, MapObjHeightStatus_en, MapObjHeightSpecialCase_en, DeD2DModelCoeff_f32, CeD2DModelCoeff_f32, DeD2DModelCtr_u16, CeD2DModelCtr_u16, MapObjID_u16, MapHeightProb_u8, MapObjDynamic_b, MapVelocityX_s8, MapVelocityY_s8, MapObjHeightStatus_tpopt_en, UpdatePattern_en, numObjUpdates_u8)

struct imo_Type_EchoRange_st
{
    zos::uint16_t DistMin_u16;
    zos::uint16_t DistMax_u16;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(imo_Type_EchoRange_st, DistMin_u16, DistMax_u16)

struct imo_Type_ADIMeasObject_st
{
    imo_Type_measObjectMainInfo_st Info_st;
    zos::geometry::Point3f egoPose;
    zos::geometry::Point2f TxSensPos_st;
    zos::geometry::Point2f RxSensPos_st;
    zos::uint8_t P1ErrorRadius_u8;
    zos::uint8_t P2ErrorRadius_u8;
    zos::uint8_t ExistProb_u8;
    imo_Type_ObjGen_measObjHeightInfo_st Height_st;
    imo_Type_HeightClassSpecialCase_en HeightSpecialCase_en;
    imo_Type_MapObj2MeasObjInfoMapping_st MapInfoMapping_st;
    imo_Type_EchoRange_st EchoRange_st;
    zos::bool_t ValidMeas_b;
    zos::bool_t Mobs_TrafficLightStartPresent_b;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(imo_Type_ADIMeasObject_st, Info_st, egoPose, TxSensPos_st, RxSensPos_st, P1ErrorRadius_u8, P2ErrorRadius_u8, ExistProb_u8, Height_st, HeightSpecialCase_en, MapInfoMapping_st, EchoRange_st, ValidMeas_b, Mobs_TrafficLightStartPresent_b)

using MobArray=zos::Array<imo_Type_ADIMeasObject_st, 17>;
struct ObjGenMOBOutSample
{
    MobArray Mobs;
    zos::uint32_t sens_cycle_count;
    zos::uint32_t MeasObjECUTime_u32;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(ObjGenMOBOutSample, Mobs, sens_cycle_count, MeasObjECUTime_u32)

using Flag=zos::Array<zos::bool_t, 2>;
using sensorDistArray2D=zos::Array<zos::Array<zos::uint16_t, 4>, 12>;
using detectionRangeArray=zos::Array<zos::uint16_t, 4>;
struct ObjGenMonitorData_st
{
    Flag oloEvalFlag_pb;
    sensorDistArray2D sensorDist_ppu16;
    detectionRangeArray DetectionRange_pu16;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(ObjGenMonitorData_st, oloEvalFlag_pb, sensorDist_ppu16, DetectionRange_pu16)

struct imo_Type_MapObject_st
{
    imo_Type_LineSegment_st Coords_st;
    zos::uint16_t ObjID_u16;
    imo_Type_ObjType_en ObjType_en;
    zos::uint8_t ObjInputType_u8;
    zos::uint8_t P1ErrorRadius_u8;
    zos::uint8_t P2ErrorRadius_u8;
    zos::uint8_t ExistProb_u8;
    zos::uint8_t HeightProb_u8;
    zos::uint8_t SourceID_u8;
    imo_Type_HeightStatus_en HeightStatus_en;
    imo_Type_HeightStatus_en HeightStatus_tpopt_en;
    zos::uint16_t TrckTime_u16;
    zos::uint16_t TrckDist_u16;
    zos::uint16_t Dist_u16;
    zos::uint16_t StateFlags_u16;
    zos::int8_t VelocityX_s8;
    zos::int8_t VelocityY_s8;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(imo_Type_MapObject_st, Coords_st, ObjID_u16, ObjType_en, ObjInputType_u8, P1ErrorRadius_u8, P2ErrorRadius_u8, ExistProb_u8, HeightProb_u8, SourceID_u8, HeightStatus_en, HeightStatus_tpopt_en, TrckTime_u16, TrckDist_u16, Dist_u16, StateFlags_u16, VelocityX_s8, VelocityY_s8)

struct imo_Type_Line_st
{
    zos::geometry::Point2f P_st;
    zos::int16_t Phi_s16;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(imo_Type_Line_st, P_st, Phi_s16)

using mapObjsArray=zos::Array<imo_Type_MapObject_st, 250>;
struct MAPData_st
{
    mapObjsArray MapObjs_pst;
    zos::uint16_t mapObjLstSize_u16;
    zos::uint16_t numMapObjs_u16;
    zos::bool_t mapComponentIsOff_b;
    zos::geometry::Point2f centerOfMapInVHM16Bit;
    zos::geometry::Point2f centerOfMapInVHM32Bit;
    imo_Type_Line_st VehPosMapCoord_st;
    imo_Type_Line_st TrafoMM_st;
    zos::bool_t TrafoTriggered_b;
    zos::uint16_t TrckDistIncr_u16;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(MAPData_st, MapObjs_pst, mapObjLstSize_u16, numMapObjs_u16, mapComponentIsOff_b, centerOfMapInVHM16Bit, centerOfMapInVHM32Bit, VehPosMapCoord_st, TrafoMM_st, TrafoTriggered_b, TrckDistIncr_u16)

struct ussPointInfo
{
    zos::common::Timestamp m_UssTimestamp;
    ObjGenMOBOutSample m_ObjGenMeasObjs_st;
    ObjGenMonitorData_st m_ObjGenOutput;
    MAPData_st m_MAPObjs;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(ussPointInfo, m_UssTimestamp, m_ObjGenMeasObjs_st, m_ObjGenOutput, m_MAPObjs)

}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::ipc::imo_Type_LineSegment_st, StdLayoutTag>
{
    using ValueType = ::zos::ipc::imo_Type_LineSegment_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::imo_Type_LineSegment_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::imo_Type_LineSegment_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::imo_Type_SensorSource_st, StdLayoutTag>
{
    using ValueType = ::zos::ipc::imo_Type_SensorSource_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::imo_Type_SensorSource_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::imo_Type_SensorSource_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::imo_Type_measObjectMainInfo_st, StdLayoutTag>
{
    using ValueType = ::zos::ipc::imo_Type_measObjectMainInfo_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::imo_Type_measObjectMainInfo_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::imo_Type_measObjectMainInfo_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::imo_Type_ObjGen_measObjHeightInfo_st, StdLayoutTag>
{
    using ValueType = ::zos::ipc::imo_Type_ObjGen_measObjHeightInfo_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::imo_Type_ObjGen_measObjHeightInfo_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::imo_Type_ObjGen_measObjHeightInfo_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::imo_Type_MapObj2MeasObjInfoMapping_st, StdLayoutTag>
{
    using ValueType = ::zos::ipc::imo_Type_MapObj2MeasObjInfoMapping_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::imo_Type_MapObj2MeasObjInfoMapping_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::imo_Type_MapObj2MeasObjInfoMapping_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::imo_Type_EchoRange_st, StdLayoutTag>
{
    using ValueType = ::zos::ipc::imo_Type_EchoRange_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::imo_Type_EchoRange_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::imo_Type_EchoRange_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::imo_Type_ADIMeasObject_st, StdLayoutTag>
{
    using ValueType = ::zos::ipc::imo_Type_ADIMeasObject_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::imo_Type_ADIMeasObject_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::imo_Type_ADIMeasObject_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::ObjGenMOBOutSample, StdLayoutTag>
{
    using ValueType = ::zos::ipc::ObjGenMOBOutSample;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::ObjGenMOBOutSample, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::ObjGenMOBOutSample;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::ObjGenMonitorData_st, StdLayoutTag>
{
    using ValueType = ::zos::ipc::ObjGenMonitorData_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::ObjGenMonitorData_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::ObjGenMonitorData_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::imo_Type_MapObject_st, StdLayoutTag>
{
    using ValueType = ::zos::ipc::imo_Type_MapObject_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::imo_Type_MapObject_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::imo_Type_MapObject_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::imo_Type_Line_st, StdLayoutTag>
{
    using ValueType = ::zos::ipc::imo_Type_Line_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::imo_Type_Line_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::imo_Type_Line_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::MAPData_st, StdLayoutTag>
{
    using ValueType = ::zos::ipc::MAPData_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::MAPData_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::MAPData_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::ussPointInfo, StdLayoutTag>
{
    using ValueType = ::zos::ipc::ussPointInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::ussPointInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::ussPointInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
