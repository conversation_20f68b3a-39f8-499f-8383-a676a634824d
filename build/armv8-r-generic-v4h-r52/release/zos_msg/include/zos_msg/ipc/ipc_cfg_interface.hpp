#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/container/array.hpp>
#include <basic_types/types.hpp>

#include <zos_msg/ipc/valInoutputpf.hpp>
#include <zos_msg/ipc/valInoutputcpj.hpp>
#include <zos_msg/ipc/valInPilotChassis.hpp>
#include <zos_msg/ipc/valInPilotVehicle.hpp>
#include <zos_msg/parking/parkapp/parkAciOutput.hpp>
#include <zos_msg/ipc/radarObject.hpp>
#include <zos_msg/ipc/ussPointInfo.hpp>
#include <zos_msg/driving/driving_control/DrivingControl.hpp>
#include <zos_msg/driving/driving_apps/driving_automaton_state.hpp>
#include <zos_msg/driving/driving_apps/driving_hmi_command.hpp>
#include <zos_msg/sensors/imuInfo.hpp>
#include <zos_msg/park_dr/vhm_abst_output.hpp>
#include <zos_msg/uss_perception/uss_data_backflow.hpp>
#include <zos_msg/uss_perception/uss_data_output.hpp>
#include <zos_msg/uss_perception/uss_hardware_input.hpp>
namespace zos
{
namespace ipc
{
const zos::uint32_t RtosToLinuxSIZE = 10;
const zos::uint32_t LinuxToRtosSIZE = 6;
enum class Data_RtosToLinux_enm
{
    HEADER_R2L,
    VALINOUTPUTPF,
    VALINOUTPUTCPJ,
    VALINPILOTCHASSISINFO,
    VALINPILOTVEHICLEINFO,
    RADAROBJECT,
    USSPOINTINFO,
    IMUINFO,
    USSAPPOUTPUT,
    RECORDECHOINPUTDATA,
    NUMBER_OF_SIGNALS_R2L,
};
enum class Data_LinuxToRtos_enm
{
    HEADER_L2R,
    PARKACICTRLOUTPUT,
    DRIVINGCONTROLOUTPUT,
    DRIVINGAUTOMATONSTATE,
    DRIVINGHMICOMMAND,
    PARKDRODOMETRY,
    NUMBER_OF_SIGNALS_L2R,
};
using sizesr2lArray=zos::Array<zos::uint16_t, RtosToLinuxSIZE>;
using countersr2lArray=zos::Array<zos::uint8_t, RtosToLinuxSIZE>;
struct TIPCSyncRtosToLinuxHeader
{
    zos::uint8_t m_numStructs;
    sizesr2lArray m_sizes;
    countersr2lArray m_counters;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(TIPCSyncRtosToLinuxHeader, m_numStructs, m_sizes, m_counters)

using IPCSyncHeader_RtosToLinux_st=TIPCSyncRtosToLinuxHeader;
struct IpcContainer_RtosToLinux_st
{
    zos::int32_t locked;
    IPCSyncHeader_RtosToLinux_st m_syncHeader_RtosToLinux;
    zos::ipc::ValInOutputPf m_valinOutputPf_RtosToLinux;
    zos::ipc::ValInOutputCpj m_valinOutputCpj_RtosToLinux;
    zos::ipc::valInPilotChassisInfo m_valinPilotChassisInfo_RtosToLinux;
    zos::ipc::valInPilotVehicleInfo m_valinPilotVehicleInfo_RtosToLinux;
    zos::ipc::radarObject m_radarObject_RtosToLinux;
    zos::ipc::ussPointInfo m_ussPointInfo_RtosToLinux;
    zos::sensors::ImuInfo m_imuInfo_RtosToLinux;
    zos::uss_perception::USS_UssAppOutput_st m_ussAppOutput_RtosToLinux;
    zos::uss_perception::CRecord_EchoInputData_st m_recordEchoInputData_RtosToLinux;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(IpcContainer_RtosToLinux_st, locked, m_syncHeader_RtosToLinux, m_valinOutputPf_RtosToLinux, m_valinOutputCpj_RtosToLinux, m_valinPilotChassisInfo_RtosToLinux, m_valinPilotVehicleInfo_RtosToLinux, m_radarObject_RtosToLinux, m_ussPointInfo_RtosToLinux, m_imuInfo_RtosToLinux, m_ussAppOutput_RtosToLinux, m_recordEchoInputData_RtosToLinux)

using sizesl2rArray=zos::Array<zos::uint16_t, LinuxToRtosSIZE>;
using countersl2rArray=zos::Array<zos::uint8_t, LinuxToRtosSIZE>;
struct TIPCSyncLinuxToRtosHeader
{
    zos::uint8_t m_numStructs;
    sizesl2rArray m_sizes;
    countersl2rArray m_counters;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(TIPCSyncLinuxToRtosHeader, m_numStructs, m_sizes, m_counters)

using IPCSyncHeader_LinuxToRtos_st=TIPCSyncLinuxToRtosHeader;
struct IpcContainer_LinuxToRtos_st
{
    zos::int32_t locked;
    IPCSyncHeader_LinuxToRtos_st m_syncHeader_LinuxToRtos;
    zos::parkapp::CParkAciOutput m_parkAciCtrlOutput_LinuxToRtos;
    zos::driving::CDrivingControlOutput m_drivingControlOutput_LinuxToRtos;
    zos::driving::CAutomatonOutput m_drivingAutomatonState_LinuxToRtos;
    zos::driving::CHmiCommand m_drivingHmiCommand_LinuxToRtos;
    zos::park_dr::CVhmAbstOutput m_vhmAbstOutput_LinuxToRtos;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(IpcContainer_LinuxToRtos_st, locked, m_syncHeader_LinuxToRtos, m_parkAciCtrlOutput_LinuxToRtos, m_drivingControlOutput_LinuxToRtos, m_drivingAutomatonState_LinuxToRtos, m_drivingHmiCommand_LinuxToRtos, m_vhmAbstOutput_LinuxToRtos)

}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::ipc::TIPCSyncRtosToLinuxHeader, StdLayoutTag>
{
    using ValueType = ::zos::ipc::TIPCSyncRtosToLinuxHeader;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::TIPCSyncRtosToLinuxHeader, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::TIPCSyncRtosToLinuxHeader;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::IpcContainer_RtosToLinux_st, StdLayoutTag>
{
    using ValueType = ::zos::ipc::IpcContainer_RtosToLinux_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::IpcContainer_RtosToLinux_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::IpcContainer_RtosToLinux_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::TIPCSyncLinuxToRtosHeader, StdLayoutTag>
{
    using ValueType = ::zos::ipc::TIPCSyncLinuxToRtosHeader;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::TIPCSyncLinuxToRtosHeader, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::TIPCSyncLinuxToRtosHeader;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::IpcContainer_LinuxToRtos_st, StdLayoutTag>
{
    using ValueType = ::zos::ipc::IpcContainer_LinuxToRtos_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::IpcContainer_LinuxToRtos_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::IpcContainer_LinuxToRtos_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
