#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/container/array.hpp>
#include <basic_types/types.hpp>

#include <zos_msg/perception/calibration/calibDcomReq.hpp>
#include <zos_msg/perception/calibration/calibDcomResult.hpp>
namespace zos
{
namespace ipc
{
namespace calib
{
const zos::uint32_t RtosToLinuxSIZE = 2;
const zos::uint32_t LinuxToRtosSIZE = 2;
enum class Data_RtosToLinux_enm
{
    HEADER_R2L,
    CALIBDCOMREQ,
    NUMBER_OF_SIGNALS_R2L,
};
enum class Data_LinuxToRtos_enm
{
    HEADER_L2R,
    CALIBDCOMRESULT,
    NUMBER_OF_SIGNALS_L2R,
};
using sizesr2lArray=zos::Array<zos::uint16_t, RtosToLinuxSIZE>;
using countersr2lArray=zos::Array<zos::uint8_t, RtosToLinuxSIZE>;
struct TIPCSyncRtosToLinuxHeader
{
    zos::uint8_t m_numStructs;
    sizesr2lArray m_sizes;
    countersr2lArray m_counters;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(TIPCSyncRtosToLinuxHeader, m_numStructs, m_sizes, m_counters)

using IPCSyncHeader_RtosToLinux_st=TIPCSyncRtosToLinuxHeader;
struct IpcContainer_RtosToLinux_st
{
    zos::int32_t locked;
    IPCSyncHeader_RtosToLinux_st m_syncHeader_RtosToLinux;
    zos::perception::calibration::CalibRequest m_calibDcomReq_RtosToLinux;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(IpcContainer_RtosToLinux_st, locked, m_syncHeader_RtosToLinux, m_calibDcomReq_RtosToLinux)

using sizesl2rArray=zos::Array<zos::uint16_t, LinuxToRtosSIZE>;
using countersl2rArray=zos::Array<zos::uint8_t, LinuxToRtosSIZE>;
struct TIPCSyncLinuxToRtosHeader
{
    zos::uint8_t m_numStructs;
    sizesl2rArray m_sizes;
    countersl2rArray m_counters;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(TIPCSyncLinuxToRtosHeader, m_numStructs, m_sizes, m_counters)

using IPCSyncHeader_LinuxToRtos_st=TIPCSyncLinuxToRtosHeader;
struct IpcContainer_LinuxToRtos_st
{
    zos::int32_t locked;
    IPCSyncHeader_LinuxToRtos_st m_syncHeader_LinuxToRtos;
    zos::perception::calibration::CalibDcomResult m_calibDcomResult_LinuxToRtos;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(IpcContainer_LinuxToRtos_st, locked, m_syncHeader_LinuxToRtos, m_calibDcomResult_LinuxToRtos)

}
}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::ipc::calib::TIPCSyncRtosToLinuxHeader, StdLayoutTag>
{
    using ValueType = ::zos::ipc::calib::TIPCSyncRtosToLinuxHeader;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::calib::TIPCSyncRtosToLinuxHeader, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::calib::TIPCSyncRtosToLinuxHeader;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::calib::IpcContainer_RtosToLinux_st, StdLayoutTag>
{
    using ValueType = ::zos::ipc::calib::IpcContainer_RtosToLinux_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::calib::IpcContainer_RtosToLinux_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::calib::IpcContainer_RtosToLinux_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::calib::TIPCSyncLinuxToRtosHeader, StdLayoutTag>
{
    using ValueType = ::zos::ipc::calib::TIPCSyncLinuxToRtosHeader;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::calib::TIPCSyncLinuxToRtosHeader, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::calib::TIPCSyncLinuxToRtosHeader;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::calib::IpcContainer_LinuxToRtos_st, StdLayoutTag>
{
    using ValueType = ::zos::ipc::calib::IpcContainer_LinuxToRtos_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::calib::IpcContainer_LinuxToRtos_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::calib::IpcContainer_LinuxToRtos_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
