#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>
#include <basic_types/container/array.hpp>

#include <zos_msg/common/common.hpp>
#include <zos_msg/common/timestamp.hpp>
namespace zos
{
namespace ipc
{
enum class EIhuIPC_LanguageMode
{
    IHUIPC_LANGUAGEMODE_CHINESE = 0,
    IHUIPC_LANGUAGEMODE_ENGLISH = 1,
    IHUIPC_LANGUAGEMODE_RUSSIAN = 2,
    IHUIPC_LANGUAGEMODE_SPANISH = 3,
    IHUIPC_LANGUAGEMODE_ARABIC = 4,
    IHUIPC_LANGUAGEMODE_FRENCH = 5,
    IHUIPC_LANGUAGEMODE_PORTUGUESE = 6,
    IHUIPC_LANGUAGEMODE_FARSI = 7,
    IHUIPC_LANGUAGEMODE_ITALIAN = 8,
    IHUIPC_LANGUAGEMODE_TURKISH = 9,
    IHUIPC_LANGUAGEMODE_VIETNAMESE = 10,
    IHUIPC_LANGUAGEMODE_GERMAN = 11,
    IHUIPC_LANGUAGEMODE_KAZAKH = 12,
    IHUIPC_LANGUAGEMODE_RESERVED4 = 13,
    IHUIPC_LANGUAGEMODE_RESERVED5 = 14,
    IHUIPC_LANGUAGEMODE_RESERVED6 = 15,
    IHUIPC_LANGUAGEMODE_RESERVED7 = 16,
    IHUIPC_LANGUAGEMODE_RESERVED8 = 17,
    IHUIPC_LANGUAGEMODE_RESERVED9 = 18,
    IHUIPC_LANGUAGEMODE_RESERVED10 = 19,
    IHUIPC_LANGUAGEMODE_RESERVED11 = 20,
    IHUIPC_LANGUAGEMODE_RESERVED12 = 21,
    IHUIPC_LANGUAGEMODE_RESERVED13 = 22,
    IHUIPC_LANGUAGEMODE_RESERVED14 = 23,
    IHUIPC_LANGUAGEMODE_RESERVED15 = 24,
    IHUIPC_LANGUAGEMODE_RESERVED16 = 25,
    IHUIPC_LANGUAGEMODE_RESERVED17 = 26,
    IHUIPC_LANGUAGEMODE_RESERVED18 = 27,
    IHUIPC_LANGUAGEMODE_RESERVED19 = 28,
    IHUIPC_LANGUAGEMODE_RESERVED20 = 29,
    IHUIPC_LANGUAGEMODE_RESERVED21 = 30,
    IHUIPC_LANGUAGEMODE_RESERVED22 = 31,
    IHUIPC_LANGUAGEMODE_RESERVED23 = 32,
    IHUIPC_LANGUAGEMODE_RESERVED24 = 33,
    IHUIPC_LANGUAGEMODE_RESERVED25 = 34,
    IHUIPC_LANGUAGEMODE_RESERVED26 = 35,
    IHUIPC_LANGUAGEMODE_RESERVED27 = 36,
    IHUIPC_LANGUAGEMODE_RESERVED28 = 37,
    IHUIPC_LANGUAGEMODE_RESERVED29 = 38,
    IHUIPC_LANGUAGEMODE_RESERVED30 = 39,
    IHUIPC_LANGUAGEMODE_RESERVED = 40,
};
enum class EIhu_Setting
{
    IHU_SETTING_STATUS_OFF = 0,
    IHU_SETTING_STATUS_ON = 1,
    IHU_SETTING_STATUS_RESERVED = 2,
    IHU_SETTING_STATUS_NO_CHANGE = 3,
};
enum class EIhuHMI_AVM_TouchEventType
{
    IHUHMI_AVM_TOUCHEVENTTYPE_NOT_TOUCH = 0,
    IHUHMI_AVM_TOUCHEVENTTYPE_PEN_DOWN = 1,
    IHUHMI_AVM_TOUCHEVENTTYPE_PEN_UP = 2,
    IHUHMI_AVM_TOUCHEVENTTYPE_PEN_MOVE = 3,
};
enum class EIhuIHU_CurrentTheme
{
    IhuIHU_CurrentTheme_1 = 0,
    IhuIHU_CurrentTheme_2 = 1,
    IhuIHU_CurrentTheme_3 = 2,
};
enum class EIhuIHU_CurrentThemeMode
{
    IhuIHU_CurrentThemeMode_None = 0,
    IhuIHU_CurrentThemeMode_Day = 1,
    IhuIHU_CurrentThemeMode_Night = 2,
    IhuIHU_CurrentThemeMode_RESERVED = 3,
};
enum class EMirrorStatus
{
    MIRROR_STATUS_INVALID = 0,
    MIRROR_STATUS_FOLD = 1,
    MIRROR_STATUS_UNFOLD = 2,
    MIRROR_STATUS_FAULT = 3,
};
enum class EDoorLockStatus
{
    LOCK_STATUS_ALL_DOOR_UNLOCKED = 0,
    LOCK_STATUS_DRIVE_DOOR_UNLOCKED_OTHER_DOOR_LOCKED = 1,
    LOCK_STATUS_ALL_DOOR_LOCKED = 2,
    LOCK_STATUS_RESERVED = 3,
};
enum class ECdcuXpuDisplayMode
{
    DISPLAY_MODE_OPERATION = 0,
    DISPLAY_MODE_AVM2D_FRONT_SINGLE_IMAGE = 1,
    DISPLAY_MODE_AVM2D_REAR_SINGLE_IMAGE = 2,
    DISPLAY_MODE_AVM2D_LEFT_SINGLE_IMAGE = 3,
    DISPLAY_MODE_AVM2D_RIGHT_SINGLE_IMAGE = 4,
    DISPLAY_MODE_3D_FRONT = 5,
    DISPLAY_MODE_3D_REAR = 6,
    DISPLAY_MODE_3D_LEFT = 7,
    DISPLAY_MODE_3D_RIGHT = 8,
    DISPLAY_MODE_4CAMERA_IMAGE_VIEW = 9,
    DISPLAY_MODE_RESERVED = 10,
    DISPLAY_MODE_NRA_VIEW = 11,
    DISPLAY_MODE_BEV_2D_FRONT = 12,
    DISPLAY_MODE_BEV_2D_REAR = 13,
    DISPLAY_MODE_BEV_2D_LEFT = 14,
    DISPLAY_MODE_BEV_2D_RIGHT = 15,
    DISPLAY_MODE_OFF = 16,
    DISPLAY_MODE_2D_FRONT_CAMERA = 17,
    DISPLAY_MODE_2D_REAR_CAMERA = 18,
    DISPLAY_MODE_2D_LEFT_CAMERA = 19,
    DISPLAY_MODE_2D_RIGHT_CAMERA = 20,
    DISPLAY_MODE_2DAVM_LR_IMAGEUSS = 21,
    DISPLAY_MODE_2DAVM_LR_IMAGE = 22,
    DISPLAY_MODE_360DEG_3D = 23,
    DISPLAY_MODE_4CAMERA_IMAGE_VIEW_RAW = 24,
    DISPLAY_MODE_4WHEEL_HUB = 25,
    DISPLAY_MODE_6WHEEL_HUB = 26,
    DISPLAY_MODE_BEV_2D_FRONT_FRWHEEL = 27,
    DISPLAY_MODE_BEV_2D_FRONT_REWHEEL = 28,
    DISPLAY_MODE_BEV_2D_REAR_FRWHEEL = 29,
    DISPLAY_MODE_BEV_2D_REAR_REWHEEL = 30,
    DISPLAY_MODE_APA_DEBUG_MODE = 31,
};
enum class ECdcuXpuTranspChassisWorkSt
{
    TRANSP_CHASSIS_NO_OPERATION = 0,
    TRANSP_CHASSIS_OFF = 1,
    TRANSP_CHASSIS_ON = 2,
    TRANSP_CHASSIS_RESERVED = 3,
};
enum class ECdcuXpuTranBody
{
    TRAN_BODY_INVALID = 0,
    TRAN_BODY_OPEN = 1,
    TRAN_BODY_CLOSE = 2,
    TRAN_BODY_RESERVED = 3,
};
enum class ECdcuXpuNRABtn
{
    NRAB_TN_NOT_AVAILABE = 0,
    NRAB_TN_CLOSE = 1,
    NRAB_TN_OPEN = 2,
    NRAB_TN_RESERVED = 3,
};
enum class EEMU3ChrgDchaConnSts
{
    EMU3_CHRG_INVALID = 0,
    EMU3_CHRG_HALF_CONNECT = 1,
    EMU3_CHRG_COMPLETE_CONNECT = 2,
    EMU3_CHRG_CONNECT_ERROR = 3,
    EMU3_CHRG_NO_CONNECT = 4,
};
struct CCpjIHU
{
    zos::uint16_t REQ_AVMTouchPanelX_1;
    zos::uint16_t REQ_AVMTouchPanelY_1;
    EIhuIPC_LanguageMode IPC_LanguageMode;
    zos::bool_t AVM_ExitRequest;
    zos::bool_t AVMCalibrationReq;
    zos::bool_t AVMSoftKeyRequest;
    EIhu_Setting MEB_Setting;
    EIhu_Setting PDCmute_Setting;
    EIhu_Setting AVM_Setting;
    EIhu_Setting DOW_Setting;
    EIhu_Setting BSD_Setting;
    EIhu_Setting RCW_Setting;
    EIhu_Setting RCTA_Setting;
    EIhu_Setting FCTA_Setting;
    EIhuHMI_AVM_TouchEventType HMI_AVM_TouchEventType;
    EIhu_Setting FAPA_Setting;
    EIhu_Setting MOD_Setting;
    zos::bool_t HPA_Voice_FuctionSwitch_display;
    zos::bool_t HPA_Voice_Confirmed;
    EIhuIHU_CurrentTheme IHU_CurrentTheme;
    EIhuIHU_CurrentThemeMode IHU_CurrentThemeMode;
    zos::bool_t APASoftKeyRequest;
    zos::bool_t NOASoftKeyRequest;
    EIhu_Setting SET_BSV_Enable;
    zos::bool_t AVMHardKeyRequest;
    EMirrorStatus Left_Mirror_Status;
    EMirrorStatus Right_Mirror_Status;
    zos::bool_t BMS_ThermPrpgtonRequest;
    EDoorLockStatus Door_Lock_Status;
    ECdcuXpuDisplayMode CDCU_XPU_DisplayMode;
    ECdcuXpuTranspChassisWorkSt CDCU_XPU_TranspChassisWorkSt;
    ECdcuXpuTranBody CDCU_XPU_TranBody;
    zos::int16_t CDCU_3603Dangle;
    ECdcuXpuNRABtn CDCU_XPU_NRABtn;
    zos::bool_t CDCU_WashCarSt;
    zos::bool_t CDCU_ESP_EPBReqVD;
    EEMU3ChrgDchaConnSts EMU3ChrgDchaConnSts;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CCpjIHU, REQ_AVMTouchPanelX_1, REQ_AVMTouchPanelY_1, IPC_LanguageMode, AVM_ExitRequest, AVMCalibrationReq, AVMSoftKeyRequest, MEB_Setting, PDCmute_Setting, AVM_Setting, DOW_Setting, BSD_Setting, RCW_Setting, RCTA_Setting, FCTA_Setting, HMI_AVM_TouchEventType, FAPA_Setting, MOD_Setting, HPA_Voice_FuctionSwitch_display, HPA_Voice_Confirmed, IHU_CurrentTheme, IHU_CurrentThemeMode, APASoftKeyRequest, NOASoftKeyRequest, SET_BSV_Enable, AVMHardKeyRequest, Left_Mirror_Status, Right_Mirror_Status, BMS_ThermPrpgtonRequest, Door_Lock_Status, CDCU_XPU_DisplayMode, CDCU_XPU_TranspChassisWorkSt, CDCU_XPU_TranBody, CDCU_3603Dangle, CDCU_XPU_NRABtn, CDCU_WashCarSt, CDCU_ESP_EPBReqVD, EMU3ChrgDchaConnSts)

enum class ETelPhoneConnectSt
{
    TELPHONECONNECTST_DEFAULT = 0,
    TELPHONECONNECTST_DISCONNECTING = 1,
    TELPHONECONNECTST_PAIR_NOT_AUTH = 2,
    TELPHONECONNECTST_PAIR_AND_AUTH = 3,
    TELPHONECONNECTST_PAIR_AND_AUTH_AND_RPA = 4,
    TELPHONECONNECTST_BUTTON_LONG_PRESSED = 5,
};
enum class EKeySt
{
    KEYST_IN_THE_CAR = 0,
    KEYST_RANGE_TOO_CLOSE = 1,
    KEYST_NORMALLY_RANGE = 2,
    KEYST_RANGE_TOO_FAR = 3,
};
enum class ETelRpaDeviceSt
{
    TELRPADEVICEST_RD_INACTIVE = 0,
    TELRPADEVICEST_RD_READY = 1,
    TELRPADEVICEST_RD_ACTIVE = 2,
};
enum class ETelRpaOutDirSel
{
    ETELRPAOUTDIRSEL_NOPARKSLOUT = 0,
    ETELRPAOUTDIRSEL_CROSSFRONTLEFTOUT = 1,
    ETELRPAOUTDIRSEL_CROSSFRONTRIGHTOUT = 2,
    ETELRPAOUTDIRSEL_FRONTOUT = 3,
    ETELRPAOUTDIRSEL_CROSSREARLEFTOUT = 4,
    ETELRPAOUTDIRSEL_CROSSREARRIGHTOUT = 5,
    ETELRPAOUTDIRSEL_PARALLELFRONTLEFTOUT = 6,
    ETELRPAOUTDIRSEL_PARALLELFRONTRIGHTOUT = 7,
    ETELRPAOUTDIRSEL_REAROUT = 8,
};
enum class ETelRpaDeviceFailReason
{
    ETELRPADEVICEFAILREASON_RD_NOFAILURE = 0,
    ETELRPADEVICEFAILREASON_RD_OFFLINE = 1,
    ETELRPADEVICEFAILREASON_RD_OUTOFRANGE = 2,
    ETELRPADEVICEFAILREASON_RD_LOWBATT = 3,
    ETELRPADEVICEFAILREASON_OTHER_FAILURES = 4,
};
enum class ETelIdcAppParkingStResp
{
    ETELIDCAPPPARKINGSTRESP_STANDBY = 0,
    ETELIDCAPPPARKINGSTRESP_RCONDITION_CHECKING = 1,
    ETELIDCAPPPARKINGSTRESP_WAIT_TO_START = 2,
    ETELIDCAPPPARKINGSTRESP_GUIDANCE_ACTIVE = 3,
    ETELIDCAPPPARKINGSTRESP_COMPLETED = 4,
    ETELIDCAPPPARKINGSTRESP_SUSPENDED = 5,
    ETELIDCAPPPARKINGSTRESP_TERMINATED = 6,
    ETELIDCAPPPARKINGSTRESP_PARKING_FAILED = 7,
    ETELIDCAPPPARKINGSTRESP_UNDO_TERMINATED = 8,
    ETELIDCAPPPARKINGSTRESP_UNDO_SUSPENED = 9,
    ETELIDCAPPPARKINGSTRESP_UNDO_COMPELED = 10,
    ETELIDCAPPPARKINGSTRESP_UNDO_PARKING_FAILED = 11,
    ETELIDCAPPPARKINGSTRESP_VEHICLE_STARTING = 12,
    ETELIDCAPPPARKINGSTRESP_PARKING_DIRECTION_SELECTION = 13,
    ETELIDCAPPPARKINGSTRESP_ERROR = 14,
    ETELIDCAPPPARKINGSTRESP_CANCEL = 15,
    ETELIDCAPPPARKINGSTRESP_TIMEOUT = 16,
};
enum class EXpaAuthSt
{
    XPA_UNKNOWN = 0,
    XPA_NOPASS = 1,
    XPA_PASS = 2,
};
enum class ETelRpaFunction
{
    RPA_INVALID_VALUE = 0,
    RPA_PARKOUT_CROSS = 1,
    RPA_PARKOUT_PARALLEL = 2,
    RPA_PARKIN = 3,
};
enum class ETelRpaDirSel
{
    RPA_NONE = 0,
    RPA_LEFT_OUT = 1,
    RPA_RIGHT_OUT = 2,
    RPA_START_PARKIN = 3,
    RPA_CONTINUE = 4,
    RPA_SUSPEND = 5,
    RPA_FORWARD = 6,
    RPA_BACKWARD = 7,
};
using control_responseArray=zos::Array<zos::uint16_t, 3>;
struct EIdcServiceResp_st
{
    control_responseArray control_response;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(EIdcServiceResp_st, control_response)

struct CCpjTel
{
    ETelPhoneConnectSt m_TEL_Phone_ConnectSt;
    EKeySt m_TEL_RPAKeyPstSt;
    ETelRpaDeviceSt m_TEL_RPADeviceSt;
    zos::bool_t m_TEL_RPACheckReq;
    zos::bool_t m_TEL_RPABtnStPressed;
    zos::int8_t m_TEL_PhoneBattSOC;
    ETelRpaOutDirSel m_TEL_RPAOutDirSel;
    ETelRpaDeviceFailReason m_TEL_RPADeviceFailReason;
    zos::bool_t m_TEL_RPACancelBtnStPressed;
    zos::uint8_t m_TEL_DrvGestureStOk;
    zos::bool_t m_TEL_RPAUndoBtnStPressed;
    ETelIdcAppParkingStResp m_TEL_IDC_AppParkingStResp;
    EXpaAuthSt m_TEL_APA_AuthSt;
    EXpaAuthSt m_TEL_RPA_AuthSt;
    EXpaAuthSt m_TEL_HPA_AuthSt;
    zos::uint8_t m_TEL_BTCountSt_u8;
    ETelRpaFunction m_TEL_RPAFunction_en;
    ETelRpaDirSel m_TEL_RPADirSel_en;
    EIdcServiceResp_st m_TEL_RPAServiceResp_st;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CCpjTel, m_TEL_Phone_ConnectSt, m_TEL_RPAKeyPstSt, m_TEL_RPADeviceSt, m_TEL_RPACheckReq, m_TEL_RPABtnStPressed, m_TEL_PhoneBattSOC, m_TEL_RPAOutDirSel, m_TEL_RPADeviceFailReason, m_TEL_RPACancelBtnStPressed, m_TEL_DrvGestureStOk, m_TEL_RPAUndoBtnStPressed, m_TEL_IDC_AppParkingStResp, m_TEL_APA_AuthSt, m_TEL_RPA_AuthSt, m_TEL_HPA_AuthSt, m_TEL_BTCountSt_u8, m_TEL_RPAFunction_en, m_TEL_RPADirSel_en, m_TEL_RPAServiceResp_st)

enum class ESTAT_EPSCtrlAvailable
{
    STAT_EPS_CTRL_AVAILABLE_STANDBY = 0,
    STAT_EPS_CTRL_AVAILABLE_ACTIVE = 1,
    STAT_EPS_CTRL_AVAILABLE_ANGLE_CONTROL_ACTIVE = 2,
    STAT_EPS_CTRL_AVAILABLE_ERROR = 3,
};
enum class ESTAT_EPSAPAAbort
{
    STAT_EPS_APA_ABORT_NO_ABORT = 0,
    STAT_EPS_APA_ABORT_OVERSPEED = 1,
    STAT_EPS_APA_ABORT_OVERTORQUE = 2,
    STAT_EPS_APA_ABORT_OVER_ANGLE_DIFFERENCE = 3,
    STAT_EPS_APA_ABORT_WRONG_STATE = 4,
    STAT_EPS_APA_ABORT_HIGH_VEHICLE_SPEED = 5,
    STAT_EPS_APA_ABORT_HANDS_ON_DETECTION = 6,
    STAT_EPS_APA_ABORT_HIGH_REQUEST_ANGLE = 7,
    STAT_EPS_APA_ABORT_HIGH_ANGLE_GRADIENT = 8,
    STAT_EPS_APA_ABORT_HIGH_ANGLE_DIFFERENCE = 9,
    STAT_EPS_APA_ABORT_INVALID_INPUTS = 10,
    STAT_EPS_APA_ABORT_OVERHEAT = 11,
    STAT_EPS_APA_ABORT_APA_NOT_ENABLE = 12,
};
enum class EEpsSystemstate
{
    EPS_STATE_ERROR = 0,
    EPS_STATE_OFF = 1,
    EPS_STATE_DRIVE_UP = 2,
    EPS_STATE_DRIVE_DOWN = 3,
};
enum class EEpsAngleCtrlMode
{
    EPS_CTL_MODE_INIT = 0,
    EPS_CTL_MODE_APA = 1,
    EPS_CTL_MODE_RPA = 2,
    EPS_CTL_MODE_LCC = 3,
    EPS_CTL_MODE_ELK = 4,
    EPS_CTL_MODE_SCPA = 5,
    EPS_CTL_MODE_RESERVED1 = 6,
    EPS_CTL_MODE_RESERVED2 = 7,
};
enum class EEpsAngleCtrlSt
{
    CTL_ST_NO_REQ = 0,
    CTL_ST_REQ_HONORED = 1,
    CTL_ST_CTL_REQ_NOT_ALLOWED = 2,
    CTL_ST_PARTIAL_ASSIST = 3,
    CTL_ST_RESERVED1 = 4,
    CTL_ST_RESERVED2 = 5,
};
enum class EEpsEndAngleCtrlReason
{
    ANG_CTL_END_RSN_INVALID = 0,
    ANG_CTL_END_RSN_NORMAL_OPERATION = 1,
    ANG_CTL_END_RSN_OVER_SPEED = 2,
    ANG_CTL_END_RSN_DRIVER_INTERFERENCE = 3,
    ANG_CTL_END_RSN_ABNORMAL_CAN_INPUT = 4,
    ANG_CTL_END_RSN_EXCESS_ANG_DEVIATION = 5,
    ANG_CTL_END_RSN_EPS_FAILURE = 6,
    ANG_CTL_END_RSN_EXCESS_TARGET_ANG_AREA = 7,
    ANG_CTL_END_RSN_EXCESS_LIMIT_ANG_GRADIENT = 8,
    ANG_CTL_END_RSN_RESERVED1 = 9,
};
struct CCpjEps
{
    zos::bool_t m_STAT_EPSAPAErr;
    ESTAT_EPSCtrlAvailable m_STAT_EPSCtrlAvailable;
    ESTAT_EPSAPAAbort m_STAT_EPSAPAAbort;
    zos::float32_t m_EPSTorqueFeedBack_f32;
    EEpsSystemstate m_EPS_Systemstate;
    EEpsAngleCtrlMode m_EPS_AngleCtrlMode;
    zos::bool_t m_EPS_SASFault_E2E;
    zos::bool_t m_EPS_SASCalibratedSt_E2E;
    EEpsAngleCtrlSt m_EPS_AngleCtrlSt;
    EEpsEndAngleCtrlReason m_EPS_EndAngleCtrlReason;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CCpjEps, m_STAT_EPSAPAErr, m_STAT_EPSCtrlAvailable, m_STAT_EPSAPAAbort, m_EPSTorqueFeedBack_f32, m_EPS_Systemstate, m_EPS_AngleCtrlMode, m_EPS_SASFault_E2E, m_EPS_SASCalibratedSt_E2E, m_EPS_AngleCtrlSt, m_EPS_EndAngleCtrlReason)

enum class EEscLcFailureSts
{
    ESC_LC_FAILURE_STS_NO_ERROR = 0,
    ESC_LC_FAILURE_STS_VEHICLE_BLOCKED = 1,
    ESC_LC_FAILURE_STS_UNEXPECTED_GEARPOSITION = 2,
    ESC_LC_FAILURE_STS_UNEXPECTED_EPB_ACTION = 3,
    ESC_LC_FAILURE_STS_RESERVED = 4,
    ESC_LC_FAILURE_STS_UNEXPECTED_GEARINTERVENTION = 5,
    ESC_LC_FAILURE_STS_RESERVED2 = 6,
    ESC_LC_FAILURE_STS_ERROR = 7,
};
enum class EEscLcStatus
{
    EHB_APA_LONG_CONTR_ST_OFF = 0,
    EHB_APA_LONG_CONTR_ST_STANDBY = 1,
    EHB_APA_LONG_CONTR_ST_ACTIVE_AUTOMATICPARK = 4,
    EHB_APA_LONG_CONTR_ST_ERROR = 10,
};
enum class EEscLcAvailability
{
    ESC_LC_AVAILABILITY_NOT_AVAILABLE = 0,
    ESC_LC_AVAILABILITY_AVAILABLE = 1,
};
struct CCpjLongCtrl
{
    EEscLcFailureSts m_ESC_LC_FailureSts;
    EEscLcStatus m_ESC_LC_Status;
    EEscLcAvailability m_ESC_LC_Availability;
    zos::bool_t m_Active_Vehicle_Hold;
    zos::bool_t m_ESC_Switch_b;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CCpjLongCtrl, m_ESC_LC_FailureSts, m_ESC_LC_Status, m_ESC_LC_Availability, m_Active_Vehicle_Hold, m_ESC_Switch_b)

enum class ECtaWarningState
{
    CTA_WARN_NONE = 0,
    CTA_WARN_LEVEL1 = 1,
    CTA_WARN_LEVEL2 = 2,
    CTA_WARN_RESERVERD = 3,
};
struct CCpjRctaWarning
{
    ECtaWarningState m_rctaWarningLH;
    ECtaWarningState m_rctaWarningRH;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CCpjRctaWarning, m_rctaWarningLH, m_rctaWarningRH)

struct CCpjFctaWarning
{
    ECtaWarningState m_fctaWarningLH;
    ECtaWarningState m_fctaWarningRH;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CCpjFctaWarning, m_fctaWarningLH, m_fctaWarningRH)

enum class StatRCTB
{
    RCTB_DISABLE = 0,
    RCTB_INITIAL = 1,
    RCTB_STANDBY = 2,
    RCTB_ACTIVE = 3,
    RCTB_FAULTY = 4,
    RCTB_BLOCKAGE = 5,
    RCTB_RESERVED = 6,
};
struct CCpjVehInfo
{
    zos::bool_t m_AccModeIsActive;
    zos::bool_t m_VehIsCrashed;
    zos::bool_t m_AEBDecCtrlIsReq;
    zos::bool_t m_EBDDecCtrlIsReq;
    zos::bool_t m_AssociatedSysFctActive_b;
    zos::bool_t m_ActSafeFctActive_b;
    zos::bool_t m_RCTBDecCtrlReq;
    StatRCTB m_Stat_RCTB;
    zos::float32_t m_RCTBTgtDecel;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CCpjVehInfo, m_AccModeIsActive, m_VehIsCrashed, m_AEBDecCtrlIsReq, m_EBDDecCtrlIsReq, m_AssociatedSysFctActive_b, m_ActSafeFctActive_b, m_RCTBDecCtrlReq, m_Stat_RCTB, m_RCTBTgtDecel)

enum class ETdcuXpuFunctionMode
{
    FUNCTION_MODE_PASSIVE = 0,
    FUNCTION_MODE_RMPA = 1,
};
enum class ETdcuXpuAngCtrlReq
{
    ANGCTRL_REQ_NO_REQUEST = 0,
    ANGCTRL_REQ_REQUEST = 1,
    ANGCTRL_REQ_RESERVED = 2,
};
enum class ETdcuXpuEPBReq
{
    EPB_REQ_NO_REQUEST = 0,
    EPB_REQ_RELEASE_BRAKE_REQUEST = 1,
    EPB_REQ_LOCK_BRAKE_REQUEST = 2,
    EPB_REQ_NOT_USED = 3,
};
enum class ETdcuSepAndComSt
{
    SEP_AND_COM_ST_INVALID = 0,
    SEP_AND_COM_ST_SEPARATED = 1,
    SEP_AND_COM_ST_COMBINED = 2,
    SEP_AND_COM_ST_SEPARATING = 3,
    SEP_AND_COM_ST_COMBING = 4,
    SEP_AND_COM_ST_PAUSE = 5,
    SEP_AND_COM_ST_RESERVED = 6,
};
enum class ETdcuSNCParkReq
{
    SNC_PARK_REQ_NO_REQ = 0,
    SNC_PARK_REQ_APPROACHING_PARKING = 1,
    SNC_PARK_REQ_REPEAT_PARKING = 2,
    SNC_PARK_REQ_LINEAR_DRIVING = 3,
    SNC_PARK_REQ_PARKING_EXECUTE = 4,
    SNC_PARK_REQ_RESERVED = 5,
};
enum class EWVoteNavHorPosType
{
    W_NONE = 0,
    W_INVALID = 1,
    W_INS_DR = 2,
    W_INS_VIO = 3,
    W_INS_GNSS = 4,
    W_INS_GNSS_VIO = 5,
    W_INS_RTK = 6,
};
enum class ETdcuDistanceXLVD
{
    TDCU_L_VD_INVALID = 0,
    TDCU_L_VD_VALID = 1,
};
enum class ETdcuDistanceXRVD
{
    TDCU_R_VD_OFF = 0,
    TDCU_R_VD_ON = 1,
    TDCU_R_VD_PENDING = 2,
    TDCU_R_VD_ERROR = 3,
    TDCU_R_VD_STABLE = 4,
    TDCU_R_VD_NOT_STABLE = 5,
};
struct CCpjTDCU
{
    ETdcuXpuFunctionMode m_TDCU_XPU_FunctionMode;
    zos::float32_t m_TDCU_XPU_SteerAngleReqVal;
    zos::bool_t m_TDCU_XPU_SteerAngleReqValVD;
    ETdcuXpuAngCtrlReq m_TDCU_XPU_AngCtrlReq;
    ETdcuXpuEPBReq m_TDCU_XPU_EPBReq;
    zos::bool_t m_TDCU_XPU_EPBReqVD;
    zos::int8_t m_TdcuAsHgtFLmm;
    zos::int8_t m_TdcuAsHgtFRmm;
    zos::int8_t m_TdcuAsHgtMLmm;
    zos::int8_t m_TdcuAsHgtMRmm;
    zos::int8_t m_TdcuAsHgtRLmm;
    zos::int8_t m_TdcuAsHgtRRmm;
    zos::bool_t m_TDCU_SepComRKEEnable;
    ETdcuSepAndComSt m_TdcuSepAndComSt;
    ETdcuSNCParkReq m_TdcuSNCParkReq;
    zos::bool_t m_TdcuSNCParkPauseReq;
    EWVoteNavHorPosType m_W_VoteNavHorPosType;
    zos::float32_t m_TDCU_YAWRate;
    zos::float32_t m_TdcuDistanceX_L;
    zos::float32_t m_TdcuDistanceX_R;
    zos::float32_t m_TdcuLADistanceX;
    ETdcuDistanceXLVD m_TdcuDistanceX_L_VD;
    ETdcuDistanceXRVD m_TdcuDistanceX_R_VD;
    zos::bool_t m_TdcuLADistanceX_VD;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CCpjTDCU, m_TDCU_XPU_FunctionMode, m_TDCU_XPU_SteerAngleReqVal, m_TDCU_XPU_SteerAngleReqValVD, m_TDCU_XPU_AngCtrlReq, m_TDCU_XPU_EPBReq, m_TDCU_XPU_EPBReqVD, m_TdcuAsHgtFLmm, m_TdcuAsHgtFRmm, m_TdcuAsHgtMLmm, m_TdcuAsHgtMRmm, m_TdcuAsHgtRLmm, m_TdcuAsHgtRRmm, m_TDCU_SepComRKEEnable, m_TdcuSepAndComSt, m_TdcuSNCParkReq, m_TdcuSNCParkPauseReq, m_W_VoteNavHorPosType, m_TDCU_YAWRate, m_TdcuDistanceX_L, m_TdcuDistanceX_R, m_TdcuLADistanceX, m_TdcuDistanceX_L_VD, m_TdcuDistanceX_R_VD, m_TdcuLADistanceX_VD)

enum class ELdcuXpuChargerSt
{
    CHARGT_ST_NOT_CONNECT = 0,
    CHARGT_ST_AC_CONNECTED = 1,
    CHARGT_ST_DC_CONNECTED = 2,
    CHARGT_ST_AD_AND_DC_CONNECTED = 3,
};
enum class ELdcuRodSignals
{
    ROD_SIGNALS_NO_REQUEST = 0,
    ROD_SIGNALS_XPILOT_REQUEST = 1,
    ROD_SIGNALS_QUIT_REQUEST = 2,
    ROD_SIGNALS_ERROR_STATUS = 3,
    ROD_SIGNALS_RESERVED = 4,
};
enum class ELdcuDriveModeFeedback
{
    DRIVE_MODE_MANUAL = 0,
    DRIVE_MODE_ACC = 1,
    DRIVE_MODE_RPA = 2,
    DRIVE_MODE_APA = 3,
    DRIVE_MODE_SCPA = 4,
    DRIVE_MODE_RESERVED = 5,
};
enum class ELdcuXpuAvailStFeedback
{
    AVAIL_ST_ATOT_DRIVE_AVAILABLE = 0,
    AVAIL_ST_VEHICLE_NOT_READY = 1,
    AVAIL_ST_ACCPEDAL_FAULT = 2,
    AVAIL_ST_IBOOSTER_FAULT = 3,
    AVAIL_ST_GEARLEV_SENSOR_FAULT = 4,
    AVAIL_ST_INHIBIT_DRIVING = 5,
    AVAIL_ST_INHIBIT_READY = 6,
};
enum class ELdcuAPBWarningLamp
{
    APB_WARNING_LAMP_LAMPOFF = 0,
    APB_WARNING_LAMP_LAMPFLASH = 1,
    APB_WARNING_LAMP_LAMPON = 2,
    APB_WARNING_LAMP_NOTUSED = 3,
};
enum class ELdcuAPBSysSt
{
    APB_SYS_ST_UNDEFINED = 0,
    APB_SYS_ST_RELEASED = 1,
    APB_SYS_ST_APPLIED = 2,
    APB_SYS_ST_RELEASING = 3,
    APB_SYS_ST_APPLYING = 4,
    APB_SYS_ST_DISENGAGED = 5,
    APB_SYS_ST_RESERVED = 6,
};
enum class ELDCUOffroadDrvMod
{
    OFFROAD_DRV_MOD_NO_COMMAND = 0,
    OFFROAD_DRV_MOD_STONE = 1,
    OFFROAD_DRV_MOD_ANOW_OR_GRASS = 2,
    OFFROAD_DRV_MOD_SAND = 3,
    OFFROAD_DRV_MOD_WADING = 4,
    OFFROAD_DRV_MOD_MUD = 5,
    OFFROAD_DRV_MOD_RESERVED = 6,
};
enum class ELdcuBodySysSt1
{
    BODY_SYS_ST1_OFF = 0,
    BODY_SYS_ST1_LOCAL_ON = 1,
    BODY_SYS_ST1_REMOTE_ON = 2,
    BODY_SYS_ST1_RESERVED = 3,
};
enum class ELdcuEVSysReadyS
{
    EV_SYS_READY_ST_NOT_READY = 0,
    EV_SYS_READY_ST_HIGH_VOLTAGE_ON = 1,
    EV_SYS_READY_ST_READY = 2,
};
enum class ELdcuTgatePosSts
{
    TARGET_POS_STS_UNKNOWN = 0,
    TARGET_POS_STS_FULL_CLOSE = 1,
    TARGET_POS_STS_HALF_CLOSE = 2,
    TARGET_POS_STS_OPENED = 3,
    TARGET_POS_STS_FULL_OPENED = 4,
};
enum class ELdcuTgateMvtSts
{
    TARGET_MVT_STS_NO_MOVEMENT = 0,
    TARGET_MVT_STS_OPENING = 1,
    TARGET_MVT_STS_CLOSING = 2,
    TARGET_MVT_STS_ANTI_PINCH_REVERSAL = 3,
    TARGET_MVT_STS_MANUAL_MOVING_OPEN = 4,
    TARGET_MVT_STS_MANUAL_MOVING_CLOSE = 5,
};
enum class ELdcuCapPosSts
{
    CAP_POS_STS_INIT = 0,
    CAP_POS_STS_CLOSED = 1,
    CAP_POS_STS_TAGATE_OPEN_REQ_POS = 2,
    CAP_POS_STS_OPEN = 3,
};
enum class ELdcuCapMvtSts
{
    CAP_MVT_STS_NO_MOVEMENT = 0,
    CAP_MVT_STS_OPENING = 1,
    CAP_MVT_STS_CLOSING = 2,
    CAP_MVT_STS_ANTI_PINCH_REVERSAL = 3,
    CAP_MVT_STS_MANUAL_MOVING_OPEN = 4,
    CAP_MVT_STS_MANUAL_MOVING_CLOSE = 5,
};
enum class ELdcuTPMSPrWarn
{
    TPMSPR_WARN_INVALID = 0,
    TPMSPR_WARN_NO_WARNING = 1,
    TPMSPR_WARN_LOW_PRESSURE_WARNING_LV1 = 2,
    TPMSPR_WARN_LOW_PRESSURE_WARNING_LV2 = 3,
    TPMSPR_WARN_HIGH_PRESSURE_WARNING = 4,
    TPMSPR_WARN_RESERVED = 5,
};
enum class ELdcuXpuInteractSt
{
    INTERACT_ST_NORMAL = 0,
    INTERACT_ST_CONFIG_ABNORMAL_RESERVED = 1,
    INTERACT_ST_VEHSPD_INVALID = 2,
    INTERACT_ST_GEARLEV_NOT_IN_P_FOR_RPA_SCPA = 3,
    INTERACT_ST_ACCPOS_TOO_DEEP = 4,
    INTERACT_ST_VEHSPD_TOO_HIGH = 5,
    INTERACT_ST_CHARGE_CONNECTED = 6,
    INTERACT_ST_DRIVER_INTERVENE = 7,
    INTERACT_ST_UNABLE_TO_SHIFT_EXECTED_GEAR = 8,
    INTERACT_ST_RESERVED = 9,
};
struct CCpjLDCU
{
    ELdcuXpuChargerSt m_LDCU_XPU_ChargerSt;
    zos::bool_t m_LDCU_XPU_AvailableSt;
    zos::bool_t m_LDCU_XPU_StopAutoDrive;
    ELdcuRodSignals m_LDCU_RodSignals;
    ELdcuDriveModeFeedback m_LDCU_DriveModeFeedback;
    zos::bool_t m_LDCU_OverRideSt;
    ELdcuXpuAvailStFeedback m_LDCU_XPU_AvailStFeedback;
    zos::int16_t m_LDCU_RWheelDriverTorqReq;
    ELdcuAPBWarningLamp m_LDCU_APBWarningLamp;
    ELdcuAPBSysSt m_LDCU_APBSysSt;
    ELDCUOffroadDrvMod m_LDCUOffroadDrvMod;
    zos::int16_t m_LDCU_FWheelPeakTorqMin;
    zos::int16_t m_LDCU_FWheelPeakTorqMax;
    zos::int16_t m_LDCU_FWheelCurTorq;
    zos::float32_t m_LDCUHIPUDesTorq;
    zos::uint16_t m_LDCU_FMotorSpd;
    zos::bool_t m_LDCU_FMotorSpdDirection;
    zos::uint16_t m_LDCU_MMotorSpd;
    zos::bool_t m_LDCU_MMotorSpdDirection;
    zos::uint16_t m_LDCU_RMotorSpd;
    zos::bool_t m_LDCU_RMotorSpdDirection;
    zos::float32_t m_LDCUIPUFDesTorq;
    zos::float32_t m_LDCUIPURDesTorq;
    zos::int16_t m_LDCU_MWheelPeakTorqMin;
    zos::int16_t m_LDCU_MWheelPeakTorqMax;
    zos::int16_t m_LDCU_MWheelCurTorq;
    ELdcuBodySysSt1 m_LDCU_BodySysSt1;
    zos::bool_t m_LDCU_DriverDoorLockSt;
    zos::int16_t m_LDCU_RWheelPeakTorqMin;
    zos::int16_t m_LDCU_RWheelPeakTorqMax;
    zos::int16_t m_LDCU_RWheelCurTorq;
    zos::bool_t m_LDCU_AccPedalSigVD;
    ELdcuEVSysReadyS m_LDCU_EVSysReadySt;
    ELdcuTgatePosSts m_LDCU_TgatePosSts;
    ELdcuTgateMvtSts m_LDCU_TgateMvtSts;
    ELdcuCapPosSts m_LDCU_CapPosSts;
    ELdcuCapMvtSts m_LDCU_CapMvtSts;
    ELdcuTPMSPrWarn m_LDCU_TPMSPrWarnFL;
    ELdcuTPMSPrWarn m_LDCU_TPMSPrWarnFR;
    ELdcuTPMSPrWarn m_LDCU_TPMSPrWarnRL;
    ELdcuTPMSPrWarn m_LDCU_TPMSPrWarnRR;
    ELdcuTPMSPrWarn m_LDCU_TPMSPrWarn3rdL;
    ELdcuTPMSPrWarn m_LDCU_TPMSPrWarn3rdR;
    ELdcuXpuInteractSt m_LDCU_XPU_InteractSt;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CCpjLDCU, m_LDCU_XPU_ChargerSt, m_LDCU_XPU_AvailableSt, m_LDCU_XPU_StopAutoDrive, m_LDCU_RodSignals, m_LDCU_DriveModeFeedback, m_LDCU_OverRideSt, m_LDCU_XPU_AvailStFeedback, m_LDCU_RWheelDriverTorqReq, m_LDCU_APBWarningLamp, m_LDCU_APBSysSt, m_LDCUOffroadDrvMod, m_LDCU_FWheelPeakTorqMin, m_LDCU_FWheelPeakTorqMax, m_LDCU_FWheelCurTorq, m_LDCUHIPUDesTorq, m_LDCU_FMotorSpd, m_LDCU_FMotorSpdDirection, m_LDCU_MMotorSpd, m_LDCU_MMotorSpdDirection, m_LDCU_RMotorSpd, m_LDCU_RMotorSpdDirection, m_LDCUIPUFDesTorq, m_LDCUIPURDesTorq, m_LDCU_MWheelPeakTorqMin, m_LDCU_MWheelPeakTorqMax, m_LDCU_MWheelCurTorq, m_LDCU_BodySysSt1, m_LDCU_DriverDoorLockSt, m_LDCU_RWheelPeakTorqMin, m_LDCU_RWheelPeakTorqMax, m_LDCU_RWheelCurTorq, m_LDCU_AccPedalSigVD, m_LDCU_EVSysReadySt, m_LDCU_TgatePosSts, m_LDCU_TgateMvtSts, m_LDCU_CapPosSts, m_LDCU_CapMvtSts, m_LDCU_TPMSPrWarnFL, m_LDCU_TPMSPrWarnFR, m_LDCU_TPMSPrWarnRL, m_LDCU_TPMSPrWarnRR, m_LDCU_TPMSPrWarn3rdL, m_LDCU_TPMSPrWarn3rdR, m_LDCU_XPU_InteractSt)

enum class EVoteNavHorPosType
{
    TYPE_NONE = 0,
    TYPE_INVALID = 1,
    TYPE_INS_DR = 2,
    TYPE_INS_VIO = 3,
    TYPE_INS_GNSS = 4,
    TYPE_INS_GNSS_VIO = 5,
    TYPE_INS_RTK = 6,
};
enum class ENavpDevErr
{
    NAVP_TYPE_NORMAL = 0,
    NAVP_TYPE_FAULT = 1,
    NAVP_TYPE_WARNING = 2,
    NAVP_TYPE_RESERVED = 3,
};
enum class ENavsDevErr
{
    NAVS_TYPE_NORMAL = 0,
    NAVS_TYPE_FAULT = 1,
    NAVS_TYPE_WARNING = 2,
    NAVS_TYPE_RESERVED = 3,
};
enum class EAhrsDevErr
{
    AHRS_TYPE_NORMAL = 0,
    AHRS_TYPE_FAULT = 1,
    AHRS_TYPE_WARNING = 2,
    AHRS_TYPE_RESERVED = 3,
};
struct CCPJFCU
{
    zos::int16_t m_Ob5Weekseconds;
    zos::float32_t m_Longitude;
    zos::float32_t m_Latitude;
    zos::float32_t m_Wgs84Hgt;
    zos::float32_t m_MagYaw;
    EVoteNavHorPosType m_VoteNavHorPosType;
    ENavpDevErr m_NavpDevErr;
    ENavsDevErr m_NavsDevErr;
    EAhrsDevErr m_AhrsDevErr;
    zos::float32_t m_Ob2Weekseconds;
    zos::float32_t m_Quaternion_q0;
    zos::float32_t m_Quaternion_q1;
    zos::float32_t m_Quaternion_q2;
    zos::float32_t m_Quaternion_q3;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CCPJFCU, m_Ob5Weekseconds, m_Longitude, m_Latitude, m_Wgs84Hgt, m_MagYaw, m_VoteNavHorPosType, m_NavpDevErr, m_NavsDevErr, m_AhrsDevErr, m_Ob2Weekseconds, m_Quaternion_q0, m_Quaternion_q1, m_Quaternion_q2, m_Quaternion_q3)

enum class EIpbXpuParkFailureSts
{
    PARK_FAILURE_STS_NO_ERROR = 0,
    PARK_FAILURE_STS_LOST_COMMUNCATION_WITH_XPU = 1,
    PARK_FAILURE_STS_APA_FAILURE = 2,
    PARK_FAILURE_STS_TOO_HIGH_SPEED = 3,
    PARK_FAILURE_STS_BRAKE_FUN_MODE_JUMP = 4,
    PARK_FAILURE_STS_ACCEXIT_DUE_TO_AX_UNAVAILABLE = 5,
    PARK_FAILURE_STS_ERROR = 6,
    PARK_FAILURE_STS_RPA_TRIGGER_WITHOUT_SECURE = 7,
};
enum class EIpbStandStillSt
{
    STANDSTILL_ST_NO_STANDSTILL = 0,
    STANDSTILL_ST_STANDSTILL = 1,
    STANDSTILL_ST_INVALID = 2,
    STANDSTILL_ST_RESERVED = 3,
};
enum class EIpbAPAActiveStateE2E
{
    APA_ACTIVE_STS_STANDBY = 0,
    APA_ACTIVE_STS_ACTIVE_APA = 1,
};
enum class EIPBMiddleWheelRotatedDirection
{
    MID_WHEEL_DRV_DIR_NOT_VALID = 0,
    MID_WHEEL_DRV_DIR_FWD = 1,
    MID_WHEEL_DRV_DIR_BWD = 2,
    MID_WHEEL_DRV_DIR_STOP = 3,
};
struct CCPJIPB
{
    zos::bool_t m_IPB_VehVertAccelVD;
    zos::float32_t m_IPB_VehVertAccel;
    EIpbXpuParkFailureSts m_IPB_XPU_ParkFailureSts;
    zos::bool_t m_IPB_XPU_ParkDrvBrkOverrideFlg;
    zos::uint16_t m_IPB_MLWheelSpdEdgesSum;
    zos::uint16_t m_IPB_MRWheelSpdEdgesSum;
    zos::bool_t m_IPB_MLWheelSpdVD_E2E;
    zos::bool_t m_IPB_MRWheelSpdVD_E2E;
    zos::float32_t m_IPB_MRWheelSpd_E2E;
    zos::float32_t m_IPB_MLWheelSpd_E2E;
    EIpbStandStillSt m_IPB_StandStillSt;
    zos::bool_t m_IPB_ESPFault;
    zos::uint16_t m_IPB_CDD_Current_BrkTq;
    zos::bool_t m_IPB_CDD_Current_BrkTqVD;
    zos::bool_t m_IPB_BrkPedalStVD_E2E;
    EIpbAPAActiveStateE2E m_IPB_APAActiveState_E2E;
    EIPBMiddleWheelRotatedDirection m_IPB_MLWheelRotatedDirection;
    EIPBMiddleWheelRotatedDirection m_IPB_MRWheelRotatedDirection;
    zos::float32_t m_IPB_PitchRate;
    zos::float32_t m_IPB_RollRate;
    zos::float32_t m_IPB_YAWRate_E2E;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CCPJIPB, m_IPB_VehVertAccelVD, m_IPB_VehVertAccel, m_IPB_XPU_ParkFailureSts, m_IPB_XPU_ParkDrvBrkOverrideFlg, m_IPB_MLWheelSpdEdgesSum, m_IPB_MRWheelSpdEdgesSum, m_IPB_MLWheelSpdVD_E2E, m_IPB_MRWheelSpdVD_E2E, m_IPB_MRWheelSpd_E2E, m_IPB_MLWheelSpd_E2E, m_IPB_StandStillSt, m_IPB_ESPFault, m_IPB_CDD_Current_BrkTq, m_IPB_CDD_Current_BrkTqVD, m_IPB_BrkPedalStVD_E2E, m_IPB_APAActiveState_E2E, m_IPB_MLWheelRotatedDirection, m_IPB_MRWheelRotatedDirection, m_IPB_PitchRate, m_IPB_RollRate, m_IPB_YAWRate_E2E)

enum class ELA1ErrorState
{
    LA1_ERROR_STS_NOERROR = 0,
    LA1_ERROR_STS_LASERDIRTY = 1,
    LA1_ERROR_STS_COMMUNICATION_ERROR = 2,
    LA1_ERROR_STS_TRANSIMITER_ERROR = 3,
    LA1_ERROR_STS_RECEIVER_ERROR = 4,
    LA1_ERROR_STS_MCU_ERROR = 5,
    LA1_ERROR_STS_RESERVED1 = 6,
};
struct CCPJLA1
{
    zos::uint16_t m_LA1_Distance_mm;
    zos::bool_t m_LA1_Unstable;
    ELA1ErrorState m_LA1_ErrorState;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CCPJLA1, m_LA1_Distance_mm, m_LA1_Unstable, m_LA1_ErrorState)

enum class ELA2ErrorState
{
    LA2_ERROR_STS_NOERROR = 0,
    LA2_ERROR_STS_LASERDIRTY = 1,
    LA2_ERROR_STS_COMMUNICATION_ERROR = 2,
    LA2_ERROR_STS_TRANSIMITER_ERROR = 3,
    LA2_ERROR_STS_RECEIVER_ERROR = 4,
    LA2_ERROR_STS_MCU_ERROR = 5,
    LA2_ERROR_STS_RESERVED1 = 6,
};
struct CCPJLA2
{
    zos::uint16_t m_LA2_Distance_mm;
    zos::bool_t m_LA2_Unstable;
    ELA2ErrorState m_LA2_ErrorState;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CCPJLA2, m_LA2_Distance_mm, m_LA2_Unstable, m_LA2_ErrorState)

enum class ERdcuBonnetAjarSt
{
    BONNET_AJAR_ST_CLOSED = 0,
    BONNET_AJAR_ST_OPEN = 1,
    BONNET_AJAR_ST_COLLIDE = 2,
    BONNET_AJAR_ST_OPEN_CIRCUIT = 3,
};
enum class ERdcuAPBActSt
{
    IPB_APB_ROPP_APPLIED = 0,
    IPB_APB_ROPP_RELEASED = 1,
    IPB_APB_ROPP_APPLYING = 2,
    IPB_APB_ROPP_RELEASING = 3,
    IPB_APB_ROPP_UNKNOW = 4,
    IPB_APB_ROPP_HOLD_APPLIIED = 5,
    IPB_APB_ROPP_COMPETELY_RELEASED = 6,
    IPB_APB_ROPP_HAP_PREPARED = 7,
};
enum class ERDCUPsngrDoorLockOutput
{
    RDCU_PSNGR_NO_OUTPUT = 0,
    RDCU_PSNGR_LOCK = 1,
    RDCU_PSNGR_UNLOCK = 2,
};
enum class ERDCURRDoorLockOutput
{
    RDCU_RRDOOR_NO_OUTPUT = 0,
    RDCU_RRDOOR_LOCK = 1,
    RDCU_RRDOOR_UNLOCK = 2,
};
struct CCPJRDCU
{
    ERdcuBonnetAjarSt m_RDCU_BonnetAjarSt;
    ERdcuAPBActSt m_RDCU_APBActSt;
    zos::uint16_t m_RDCU_RLSBrightnessFW;
    zos::bool_t m_RDCU_FliiLampOutputSt;
    zos::bool_t m_RDCU_ChrgPortLockSt;
    ERDCUPsngrDoorLockOutput m_RDCU_PsngrDoorLockOutput;
    ERDCURRDoorLockOutput m_RDCU_RRDoorLockOutput;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CCPJRDCU, m_RDCU_BonnetAjarSt, m_RDCU_APBActSt, m_RDCU_RLSBrightnessFW, m_RDCU_FliiLampOutputSt, m_RDCU_ChrgPortLockSt, m_RDCU_PsngrDoorLockOutput, m_RDCU_RRDoorLockOutput)

enum class ERwsSysWarning
{
    RWS_WARNING_NORMAL = 0,
    RWS_WARNING_CRITICALF_FAILURE = 1,
    RWS_WARNING_GENERAL_FAILURE = 2,
    RWS_WARNING_RESERVED = 3,
};
struct CCPJRWS
{
    ERwsSysWarning m_RWS_SysWarning;
    zos::float32_t m_RWS_RLWheelangle;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CCPJRWS, m_RWS_SysWarning, m_RWS_RLWheelangle)

struct CCPJSRS
{
    zos::bool_t m_SRS_CrashOutputSt;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CCPJSRS, m_SRS_CrashOutputSt)

struct CCPJTBOX
{
    zos::int32_t m_TBOX_GPSLatitude;
    zos::int32_t m_TBOX_GPSLongitude;
    zos::uint16_t m_TBOX_GPSAccuracy;
    zos::float32_t m_TBOX_GPSBearing;
    zos::float32_t m_TBOX_GPSAltitude;
    zos::int16_t m_TBOX_GPSSpeed;
    zos::uint32_t m_TBOX_GPSTime;
    zos::bool_t m_TBOX_FixFlag;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CCPJTBOX, m_TBOX_GPSLatitude, m_TBOX_GPSLongitude, m_TBOX_GPSAccuracy, m_TBOX_GPSBearing, m_TBOX_GPSAltitude, m_TBOX_GPSSpeed, m_TBOX_GPSTime, m_TBOX_FixFlag)

enum class EXpuACCSt
{
    PU_ACC_ST_OFF = 0,
    PU_ACC_ST_PASSIVE = 1,
    PU_ACC_ST_STANDBY = 2,
    PU_ACC_ST_ACTIVE = 3,
    PU_ACC_ST_BRAKE_ONLY = 4,
    PU_ACC_ST_OVERRIDE = 5,
    PU_ACC_ST_STANDACTIVE = 6,
    PU_ACC_ST_STANDWAIT = 7,
    PU_ACC_ST_FAILURE = 8,
};
struct CCPJXPU
{
    EXpuACCSt m_XPU_ACCSt;
    zos::bool_t m_XPU_IPB_ABAReq;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CCPJXPU, m_XPU_ACCSt, m_XPU_IPB_ABAReq)

enum class EPosFix
{
    NO_FIX = 0,
    FIX_2D = 1,
    FIX_3D = 2,
};
struct CPfGnssLocalization
{
    zos::float32_t m_gnssAltitude;
    zos::float32_t m_gnssHeading;
    zos::float32_t m_gnssLongitude;
    zos::float32_t m_gnssLatitude;
    zos::float32_t m_gnssLongitudeDeadReck;
    zos::float32_t m_gnssLatitudeDeadReck;
    zos::float32_t m_gnssVelOvrGrnd;
    zos::int32_t m_gnssSampleRate;
    zos::uint8_t m_gnssVsblSat;
    zos::uint8_t m_gnssTrackedSat;
    zos::float32_t m_gnssVdop;
    zos::float32_t m_gnssHdop;
    zos::float32_t m_gnssPdop;
    EPosFix m_gnssPosFix;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CPfGnssLocalization, m_gnssAltitude, m_gnssHeading, m_gnssLongitude, m_gnssLatitude, m_gnssLongitudeDeadReck, m_gnssLatitudeDeadReck, m_gnssVelOvrGrnd, m_gnssSampleRate, m_gnssVsblSat, m_gnssTrackedSat, m_gnssVdop, m_gnssHdop, m_gnssPdop, m_gnssPosFix)

struct CPfGnssDateTime
{
    zos::float32_t m_gnssTmStmp;
    zos::uint16_t m_gnssDateTimeMilliSecond;
    zos::uint16_t m_gnssDateTimeYear;
    zos::uint8_t m_gnssDateTimeSecond;
    zos::uint8_t m_gnssDateTimeMinute;
    zos::uint8_t m_gnssDateTimeMonth;
    zos::uint8_t m_gnssDateTimeDay;
    zos::uint8_t m_gnssDateTimeHour;
    zos::uint8_t m_gnssMasterCount1;
    zos::uint8_t m_gnssMasterCount2;
    zos::uint8_t m_gnssMasterCount3;
    zos::uint8_t m_gnssMasterCount4;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CPfGnssDateTime, m_gnssTmStmp, m_gnssDateTimeMilliSecond, m_gnssDateTimeYear, m_gnssDateTimeSecond, m_gnssDateTimeMinute, m_gnssDateTimeMonth, m_gnssDateTimeDay, m_gnssDateTimeHour, m_gnssMasterCount1, m_gnssMasterCount2, m_gnssMasterCount3, m_gnssMasterCount4)

struct ValInOutputCpj
{
    CCpjIHU m_IHU;
    CCpjTel m_TEL;
    CCpjEps m_Eps;
    CCpjLongCtrl m_LongCtrl;
    CCpjRctaWarning m_rctaWarning;
    CCpjFctaWarning m_fctaWarning;
    CCpjVehInfo m_vehInfo;
    CCpjTDCU m_TDCU;
    CCpjLDCU m_LDCU;
    CCPJFCU m_FCU;
    CCPJIPB m_IPB;
    CCPJLA1 m_LA1;
    CCPJLA2 m_LA2;
    CCPJRDCU m_RDCU;
    CCPJRWS m_RWS;
    CCPJSRS m_SRS;
    CCPJTBOX m_TBOX;
    CCPJXPU m_XPU;
    CPfGnssLocalization m_pfGnssLocalization;
    CPfGnssDateTime m_pfGnssDateTime;
    zos::common::Timestamp m_senderCallTimestamp;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(ValInOutputCpj, m_IHU, m_TEL, m_Eps, m_LongCtrl, m_rctaWarning, m_fctaWarning, m_vehInfo, m_TDCU, m_LDCU, m_FCU, m_IPB, m_LA1, m_LA2, m_RDCU, m_RWS, m_SRS, m_TBOX, m_XPU, m_pfGnssLocalization, m_pfGnssDateTime, m_senderCallTimestamp)

}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::ipc::CCpjIHU, StdLayoutTag>
{
    using ValueType = ::zos::ipc::CCpjIHU;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CCpjIHU, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::CCpjIHU;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::EIdcServiceResp_st, StdLayoutTag>
{
    using ValueType = ::zos::ipc::EIdcServiceResp_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::EIdcServiceResp_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::EIdcServiceResp_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CCpjTel, StdLayoutTag>
{
    using ValueType = ::zos::ipc::CCpjTel;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CCpjTel, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::CCpjTel;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CCpjEps, StdLayoutTag>
{
    using ValueType = ::zos::ipc::CCpjEps;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CCpjEps, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::CCpjEps;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CCpjLongCtrl, StdLayoutTag>
{
    using ValueType = ::zos::ipc::CCpjLongCtrl;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CCpjLongCtrl, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::CCpjLongCtrl;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CCpjRctaWarning, StdLayoutTag>
{
    using ValueType = ::zos::ipc::CCpjRctaWarning;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CCpjRctaWarning, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::CCpjRctaWarning;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CCpjFctaWarning, StdLayoutTag>
{
    using ValueType = ::zos::ipc::CCpjFctaWarning;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CCpjFctaWarning, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::CCpjFctaWarning;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CCpjVehInfo, StdLayoutTag>
{
    using ValueType = ::zos::ipc::CCpjVehInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CCpjVehInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::CCpjVehInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CCpjTDCU, StdLayoutTag>
{
    using ValueType = ::zos::ipc::CCpjTDCU;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CCpjTDCU, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::CCpjTDCU;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CCpjLDCU, StdLayoutTag>
{
    using ValueType = ::zos::ipc::CCpjLDCU;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CCpjLDCU, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::CCpjLDCU;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CCPJFCU, StdLayoutTag>
{
    using ValueType = ::zos::ipc::CCPJFCU;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CCPJFCU, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::CCPJFCU;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CCPJIPB, StdLayoutTag>
{
    using ValueType = ::zos::ipc::CCPJIPB;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CCPJIPB, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::CCPJIPB;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CCPJLA1, StdLayoutTag>
{
    using ValueType = ::zos::ipc::CCPJLA1;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CCPJLA1, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::CCPJLA1;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CCPJLA2, StdLayoutTag>
{
    using ValueType = ::zos::ipc::CCPJLA2;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CCPJLA2, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::CCPJLA2;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CCPJRDCU, StdLayoutTag>
{
    using ValueType = ::zos::ipc::CCPJRDCU;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CCPJRDCU, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::CCPJRDCU;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CCPJRWS, StdLayoutTag>
{
    using ValueType = ::zos::ipc::CCPJRWS;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CCPJRWS, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::CCPJRWS;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CCPJSRS, StdLayoutTag>
{
    using ValueType = ::zos::ipc::CCPJSRS;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CCPJSRS, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::CCPJSRS;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CCPJTBOX, StdLayoutTag>
{
    using ValueType = ::zos::ipc::CCPJTBOX;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CCPJTBOX, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::CCPJTBOX;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CCPJXPU, StdLayoutTag>
{
    using ValueType = ::zos::ipc::CCPJXPU;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CCPJXPU, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::CCPJXPU;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CPfGnssLocalization, StdLayoutTag>
{
    using ValueType = ::zos::ipc::CPfGnssLocalization;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CPfGnssLocalization, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::CPfGnssLocalization;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CPfGnssDateTime, StdLayoutTag>
{
    using ValueType = ::zos::ipc::CPfGnssDateTime;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CPfGnssDateTime, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::CPfGnssDateTime;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::ValInOutputCpj, StdLayoutTag>
{
    using ValueType = ::zos::ipc::ValInOutputCpj;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::ValInOutputCpj, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::ValInOutputCpj;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
