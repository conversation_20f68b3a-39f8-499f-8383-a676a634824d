#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>

#include <zos_msg/common/common.hpp>
#include <zos_msg/common/timestamp.hpp>
namespace zos
{
namespace ipc
{
enum class ELdwSwitchState
{
    LDW_SWITCH_NOREQ = 0,
    LDW_SWITCH_ON = 1,
    LDW_SWITCH_OFF = 2,
};
enum class ELdpSwitchState
{
    LDP_SWITCH_NOREQ = 0,
    LDP_SWITCH_ON = 1,
    LDP_SWITCH_OFF = 2,
};
enum class ELksSwitchState
{
    LKS_SWITCH_NOREQ = 0,
    LKS_SWITCH_ON = 1,
    LKS_SWITCH_OFF = 2,
};
enum class EElkSwitchState
{
    ELK_SWITCH_NOREQ = 0,
    ELK_SWITCH_ON = 1,
    ELK_SWITCH_OFF = 2,
};
enum class ELbsSwitchState
{
    LBS_SWITCH_NOREQ = 0,
    LBS_SWITCH_ON = 1,
    LBS_SWITCH_OFF = 2,
};
enum class EIsaSwitchState
{
    ISA_SWITCH_NOREQ = 0,
    ISA_SWITCH_ON = 1,
    ISA_SWITCH_OFF = 2,
};
enum class EAlcSwitchState
{
    ALC_SWITCH_NOREQ = 0,
    ALC_SWITCH_ON = 1,
    ALC_SWITCH_OFF = 2,
};
enum class EDAISwitchState
{
    DAI_SWITCH_NOREQ = 0,
    DAI_SWITCH_ON = 1,
    DAI_SWITCH_OFF = 2,
};
enum class EAlcDriverConfirm
{
    ALC_DRIVER_CONFIRM_NOREQ = 0,
    ALC_DRIVER_CONFIRM_ON = 1,
    ALC_DRIVER_CONFIRM_OFF = 2,
};
enum class ENoaSwitchState
{
    NOA_SWITCH_NOREQ = 0,
    NOA_SWITCH_ON = 1,
    NOA_SWITCH_OFF = 2,
};
enum class ENoaActivationType
{
    NOA_ACTIVATION_NOREQ = 0,
    NOA_ACTIVATION_NOCONFIRM = 1,
    NOA_ACTIVATION_CONFIRM = 2,
};
enum class ENoaOvertakeStyle
{
    NOA_OVERTAKE_NOREQ = 0,
    NOA_OVERTAKE_STABLE = 1,
    NOA_OVERTAKE_STANDARD = 2,
    NOA_OVERTAKE_AGGRESSIVE = 3,
};
enum class ENoaActivateConfirm
{
    NOA_ACTIVATE_CONFIRM_NOREQ = 0,
    NOA_ACTIVATE_CONFIRM_ACCEPT = 1,
    NOA_ACTIVATE_CONFIRM_REFUSE = 2,
};
enum class ELdaSensitivityReq
{
    LDA_SENSITIVITY_NOREQ = 0,
    LDA_SENSITIVITY_LOW = 1,
    LDA_SENSITIVITY_MEDIUM = 2,
    LDA_SENSITIVITY_HIGH = 3,
};
enum class ELdwWarningTypeReq
{
    LDW_WARNING_AUDIO = 0,
    LDW_WARNING_TACTILE = 1,
    LDW_WARNING_AUDIO_TACTILE = 2,
};
enum class EAccTypeReq
{
    LONG_CTRL_TYPE_NO_REQ = 0,
    LONG_CTRL_TYPE_CC = 1,
    LONG_CTRL_TYPE_ACC = 2,
    LONG_CTRL_TYPE_ICC = 3,
};
enum class ELeverReqSt
{
    LEVER_NO_REQUEST = 0,
    LEVER_RIGHT_LANE_CHANGE = 1,
    LEVER_TURN_RIGHT = 2,
    LEVER_LEFT_LANE_CHANGE = 3,
    LEVER_TURN_LEFT = 4,
    LEVER_ERROR = 5,
};
enum class EShiftLeverPosition
{
    SHIFT_LEVER_INITIAL_POSITION = 0,
    SHIFT_LEVER_X1_POSITION = 1,
    SHIFT_LEVER_Y1_POSITION = 2,
    SHIFT_LEVER_X2_POSITION = 3,
    SHIFT_LEVER_Y2_POSITION = 4,
};
enum class EStsRmdSet
{
    STSRMDSET_NO_REQUEST = 0,
    STSRMDSET_NO_REMIND = 1,
    STSRMDSET_WORD = 2,
    STSRMDSET_WORD_SOUND = 3,
};
enum class EElkSet
{
    ELKSET_NO_REQUEST = 0,
    ELKSET_CLOSE = 1,
    ELKSET_WARNING = 2,
    ELKSET_WARNING_INTERVENTION = 3,
};
enum class EAccSpdAdjSet
{
    ACCSPDADJSET_NO_REQUEST = 0,
    ACCSPDADJSET_SHORTPRESS_1_LONGPRESS_5 = 1,
    ACCSPDADJSET_SHORTPRESS_5_LONGPRESS_1 = 2,
    ACCSPDADJSET_RESERVE = 3,
};
enum class EIfcStatus
{
    IFCSTATUS_NO_ERROR = 0,
    IFCSTATUS_INTERNAL_ERROR = 1,
    IFCSTATUS_FAILSAFE_BLOCK = 2,
    IFCSTATUS_NO_CALIBRATION = 3,
};
enum class EIhbcOnOffSwitchState
{
    IHBC_SWITCH_NOREQ = 0,
    IHBC_SWITCH_ON = 1,
    IHBC_SWITCH_OFF = 2,
};
enum class EHighBeamSwitchState
{
    IHBCHIGHBEAM_SWITCH_OFF = 0,
    IHBCHIGHBEAM_SWITCH_HIGHBEAM = 1,
    IHBCHIGHBEAM_SWITCH_FLASHBEAM = 2,
};
enum class EHeaderLampSwitchState
{
    IHBCHEADERLAMP_SWITCH_OFF = 0,
    IHBCHEADERLAMP_SWITCH_POSITIONLAMP = 1,
    IHBCHEADERLAMP_SWITCH_LOWBEAM = 2,
    IHBCHEADERLAMP_SWITCH_AUTO = 3,
};
enum class EAEBOnOffSwitchState
{
    AEB_SWITCH_NOREQ = 0,
    AEB_SWITCH_ON = 1,
    AEB_SWITCH_OFF = 2,
};
enum class EOneClickSwitchState
{
    ONECLICK_SWITCH_NOREQ = 0,
    ONECLICK_SWITCH_OFF = 1,
    ONECLICK_SWITCH_ON = 2,
};
enum class ETsrMainSwitchState
{
    TSRMAIN_SWITCH_NOREQ = 0,
    TSRMAIN_SWITCH_OFF = 1,
    TSRMAIN_SWITCH_ON = 2,
};
enum class EOverSpdSwitchState
{
    OVERSPD_SWITCH_NOREQ = 0,
    OVERSPD_SWITCH_OFF = 1,
    OVERSPD_SWITCH_ON = 2,
};
enum class EISLCSwitchState
{
    ISLC_SWITCH_NOREQ = 0,
    ISLC_SWITCH_DISPLAY = 1,
    ISLC_SWITCH_WARNING = 2,
    ISLC_SWITCH_CONTROL = 3,
};
enum class EAEBSenSwitchState
{
    AEBSEN_SWITCH_NOREQ = 0,
    AEBSEN_SWITCH_MEDIUM = 1,
    AEBSEN_SWITCH_LOW = 2,
    AEBSEN_SWITCH_HIGH = 3,
};
struct CHmiHaptic
{
    ELdwSwitchState m_LdwSwitchState;
    ELdpSwitchState m_LdpSwitchState;
    ELksSwitchState m_LksSwitchState;
    EElkSwitchState m_ElkSwitchState;
    ELbsSwitchState m_LbsSwitchState;
    EIsaSwitchState m_IsaSwitchState;
    EAlcSwitchState m_alcSwtichState;
    EDAISwitchState m_DaiSwtichState;
    EAlcDriverConfirm m_alcDriverConfirm;
    ENoaSwitchState m_noaSwtichState;
    ENoaActivationType m_noaActivationType;
    ENoaOvertakeStyle m_noaOvertakeStyle;
    ENoaActivateConfirm m_noaActivateConfirm;
    ELdaSensitivityReq m_ldaSensitivityReq;
    ELdwWarningTypeReq m_ldwWarningTypeReq;
    EAccTypeReq m_accTypeReq;
    ELeverReqSt m_turnLeverReq;
    EShiftLeverPosition m_shiftLeverPosition;
    zos::bool_t m_isLksSwitchOn;
    zos::bool_t m_isAccCancelSwitchPress;
    zos::bool_t m_isAccMainSwitchPress;
    zos::bool_t m_isAccResumeSwitchPress;
    zos::bool_t m_isAccSetSwitchPress;
    zos::bool_t m_isAccTimegapMinusPress;
    zos::bool_t m_isAccTimegapPlusPress;
    zos::uint8_t m_pdmAccTimegap;
    zos::bool_t m_isAccVSetMinusPress;
    zos::bool_t m_isAccVSetPlusPress;
    zos::bool_t m_isDirIndicatorLeftOn;
    zos::bool_t m_isDirIndicatorRightOn;
    zos::bool_t m_isHazardLightOn;
    zos::bool_t m_isStopLightOn;
    zos::bool_t m_isTurnLeverLeftOn;
    zos::bool_t m_isTurnLeverRightOn;
    zos::bool_t m_isHazardSwitchOn;
    EStsRmdSet m_StsRmdSet;
    EElkSet m_ElkSet;
    EAccSpdAdjSet m_AccSpeedAdjustSet;
    zos::uint8_t m_aebStatus;
    zos::uint8_t m_targetSpdLimit;
    zos::uint8_t m_ihbcErrFlg;
    zos::uint8_t m_tsrStaus;
    zos::uint8_t m_highBeamOnOff;
    zos::uint8_t m_spdLimRmn;
    zos::uint8_t m_tsrSetResp;
    zos::uint8_t m_spdRmnSetResp;
    zos::uint8_t m_spdAstSetResp;
    zos::uint8_t m_aebSetResp;
    zos::uint8_t m_aebLvlSetResp;
    zos::uint8_t m_ifcError;
    EIfcStatus m_ifcStatus;
    zos::uint8_t m_dowSetResp;
    zos::uint8_t m_bsdSetResp;
    zos::uint8_t m_rcwSetResp;
    zos::uint8_t m_rctaSetResp;
    EIhbcOnOffSwitchState m_IhbcOnOffSwitchState;
    EHighBeamSwitchState m_HighBeamSwitchState;
    EHeaderLampSwitchState m_HeaderLampSwitchState;
    EAEBOnOffSwitchState m_aebOnOffSwitchState;
    zos::bool_t m_isTurnLampFault;
    EOneClickSwitchState m_adasOneClickSwitch;
    zos::uint8_t m_scrollWheelCounter;
    ETsrMainSwitchState m_tsrMainSwitchState;
    zos::bool_t m_tsrMainSwitchValid;
    EOverSpdSwitchState m_tsrOverSpdWarnSwitchState;
    EOverSpdSwitchState m_tsrOverSpdVoiceSwitchState;
    zos::uint8_t m_tsrISLCSpdRange;
    zos::uint8_t m_tsrOverSpdSensitivity;
    EISLCSwitchState m_tsrISLCSwitchState;
    EAEBSenSwitchState m_aebSensitivitySwitchState;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CHmiHaptic, m_LdwSwitchState, m_LdpSwitchState, m_LksSwitchState, m_ElkSwitchState, m_LbsSwitchState, m_IsaSwitchState, m_alcSwtichState, m_DaiSwtichState, m_alcDriverConfirm, m_noaSwtichState, m_noaActivationType, m_noaOvertakeStyle, m_noaActivateConfirm, m_ldaSensitivityReq, m_ldwWarningTypeReq, m_accTypeReq, m_turnLeverReq, m_shiftLeverPosition, m_isLksSwitchOn, m_isAccCancelSwitchPress, m_isAccMainSwitchPress, m_isAccResumeSwitchPress, m_isAccSetSwitchPress, m_isAccTimegapMinusPress, m_isAccTimegapPlusPress, m_pdmAccTimegap, m_isAccVSetMinusPress, m_isAccVSetPlusPress, m_isDirIndicatorLeftOn, m_isDirIndicatorRightOn, m_isHazardLightOn, m_isStopLightOn, m_isTurnLeverLeftOn, m_isTurnLeverRightOn, m_isHazardSwitchOn, m_StsRmdSet, m_ElkSet, m_AccSpeedAdjustSet, m_aebStatus, m_targetSpdLimit, m_ihbcErrFlg, m_tsrStaus, m_highBeamOnOff, m_spdLimRmn, m_tsrSetResp, m_spdRmnSetResp, m_spdAstSetResp, m_aebSetResp, m_aebLvlSetResp, m_ifcError, m_ifcStatus, m_dowSetResp, m_bsdSetResp, m_rcwSetResp, m_rctaSetResp, m_IhbcOnOffSwitchState, m_HighBeamSwitchState, m_HeaderLampSwitchState, m_aebOnOffSwitchState, m_isTurnLampFault, m_adasOneClickSwitch, m_scrollWheelCounter, m_tsrMainSwitchState, m_tsrMainSwitchValid, m_tsrOverSpdWarnSwitchState, m_tsrOverSpdVoiceSwitchState, m_tsrISLCSpdRange, m_tsrOverSpdSensitivity, m_tsrISLCSwitchState, m_aebSensitivitySwitchState)

enum class EDisplayGearPositon
{
    DISPLAY_GEAR_NEUTRAL = 0,
    DISPLAY_GEAR_PARKING = 1,
    DISPLAY_GEAR_REVERSE = 2,
    DISPLAY_GEAR_DRIVING = 3,
    DISPLAY_GEAR_MANNUAL = 4,
};
enum class EVehicleDriveMode
{
    VEH_DRIVE_MODE_NORMAL = 0,
    VEH_DRIVE_MODE_ECO = 1,
    VEH_DRIVE_MODE_SPORT = 2,
    VEH_DRIVE_MODE_NO_REQ = 3,
    VEH_DRIVE_MODE_INDIVIDUAL = 4,
    VEH_DRIVE_MODE_SLIPPERY = 5,
    VEH_DRIVE_MODE_OFFROAD = 6,
};
enum class EWiperStatus
{
    WIPER_STATUS_OFF = 0,
    WIPER_STATUS_AUTO = 1,
    WIPER_STATUS_LOW = 2,
    WIPER_STATUS_MEDIUM = 3,
    WIPER_STATUS_HIGH = 4,
};
enum class EVehSpdUnit
{
    VEH_SPD_UNIT_KPH = 0,
    VEH_SPD_UNIT_MPH = 1,
};
enum class EBsdWarning
{
    BSD_NO_WARNING = 0,
    BSD_WARNING_LEFT = 1,
    BSD_WARNING_RIGHT = 2,
    BSD_WARNING_BOTH = 3,
};
enum class ERctaWarning
{
    RCTA_NO_WARNING = 0,
    RCTA_WARNING_LEFT = 1,
    RCTA_WARNING_RIGHT = 2,
    RCTA_WARNING_BOTH = 3,
};
enum class EFctaWarning
{
    FCTA_NO_WARNING = 0,
    FCTA_WARNING_LEFT = 1,
    FCTA_WARNING_RIGHT = 2,
    FCTA_WARNING_BOTH = 3,
};
enum class ERainIntensity
{
    RAIN_NO = 0,
    RAIN_LIGHT = 1,
    RAIN_MIDDLE = 2,
    RAIN_HEAVY = 3,
};
struct CVehicleInfo
{
    EDisplayGearPositon m_displayGearPosition;
    EVehicleDriveMode m_vehicleDriveMode;
    zos::bool_t m_isAbsActive;
    zos::bool_t m_isBrakePedalPressed;
    zos::bool_t m_isCddActive;
    zos::bool_t m_isCddUnAvailable;
    zos::bool_t m_isPrefillActive;
    zos::bool_t m_isBrakeJerkActive;
    zos::bool_t m_isEbdCtlActive;
    zos::bool_t m_isDtcActive;
    zos::bool_t m_isEpbActive;
    zos::bool_t m_isEspActive;
    zos::bool_t m_isEspClosed;
    zos::bool_t m_isEspFailed;
    zos::bool_t m_isEspNoBrakeForce;
    zos::bool_t m_isHdcActive;
    zos::bool_t m_isTscActive;
    zos::float32_t m_wheelBrakeForce;
    zos::bool_t m_isLimphome;
    zos::bool_t m_isAvhActive;
    zos::bool_t m_isBrakeDiskOverheat;
    zos::bool_t m_isCdpActive;
    zos::float32_t m_expectedBrakeTorqueSum;
    zos::float32_t m_fourWheelBrakeTorqueSum;
    zos::bool_t m_isAebActive;
    zos::bool_t m_CrashStsActive;
    zos::bool_t m_isAebAvailable;
    zos::bool_t m_isPrefillAvaialble;
    zos::bool_t m_isBrakeJerkAvailable;
    zos::bool_t m_isHbaAvailable;
    zos::bool_t m_isTCSFault;
    zos::bool_t m_isEbdFault;
    zos::bool_t m_isDriverBeltOpen;
    zos::bool_t m_isPassengerBeltOpen;
    zos::bool_t m_isDriverOverride;
    zos::bool_t m_accFunctionAllow;
    zos::bool_t m_isFLDoorOpen;
    zos::bool_t m_RearViewFold;
    zos::bool_t m_isFRDoorOpen;
    zos::bool_t m_isRLDoorOpen;
    zos::bool_t m_isRRDoorOpen;
    zos::bool_t m_isHoodOpen;
    zos::bool_t m_isTrunkAjarOpen;
    zos::float32_t m_vehicleDisplaySpeed;
    zos::float32_t m_icDisplaySpeed;
    zos::uint32_t m_icmTotalMileage;
    EWiperStatus m_wiperStatus;
    zos::bool_t m_isfrontFogLampOn;
    zos::bool_t m_isAirBagstate;
    EVehSpdUnit m_vehSpeedUnit;
    zos::float32_t m_vlcInternalTargAccel;
    EBsdWarning m_bsdWarning;
    ERctaWarning m_rctaWarning;
    EFctaWarning m_fctaWarning;
    ERainIntensity m_rainIntensity;
    zos::bool_t m_isLowBeamLampOn;
    zos::bool_t m_isHighBeamLampOn;
    zos::bool_t m_isvehPwrMdRun;
    zos::bool_t m_isHVbatteryFault;
    zos::bool_t m_isPwrLimitFault;
    zos::bool_t m_isVdcActive;
    zos::bool_t m_isMotTrqValid;
    zos::bool_t m_isMotSpdValid;
    zos::float32_t m_vehicleDisplaySpeed2Vlc;
    zos::bool_t m_isTirePresWarning;
    zos::bool_t m_isTirePresAbnormal;
    zos::bool_t m_isEpbWarning;
    zos::bool_t m_isHandsOffValid;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CVehicleInfo, m_displayGearPosition, m_vehicleDriveMode, m_isAbsActive, m_isBrakePedalPressed, m_isCddActive, m_isCddUnAvailable, m_isPrefillActive, m_isBrakeJerkActive, m_isEbdCtlActive, m_isDtcActive, m_isEpbActive, m_isEspActive, m_isEspClosed, m_isEspFailed, m_isEspNoBrakeForce, m_isHdcActive, m_isTscActive, m_wheelBrakeForce, m_isLimphome, m_isAvhActive, m_isBrakeDiskOverheat, m_isCdpActive, m_expectedBrakeTorqueSum, m_fourWheelBrakeTorqueSum, m_isAebActive, m_CrashStsActive, m_isAebAvailable, m_isPrefillAvaialble, m_isBrakeJerkAvailable, m_isHbaAvailable, m_isTCSFault, m_isEbdFault, m_isDriverBeltOpen, m_isPassengerBeltOpen, m_isDriverOverride, m_accFunctionAllow, m_isFLDoorOpen, m_RearViewFold, m_isFRDoorOpen, m_isRLDoorOpen, m_isRRDoorOpen, m_isHoodOpen, m_isTrunkAjarOpen, m_vehicleDisplaySpeed, m_icDisplaySpeed, m_icmTotalMileage, m_wiperStatus, m_isfrontFogLampOn, m_isAirBagstate, m_vehSpeedUnit, m_vlcInternalTargAccel, m_bsdWarning, m_rctaWarning, m_fctaWarning, m_rainIntensity, m_isLowBeamLampOn, m_isHighBeamLampOn, m_isvehPwrMdRun, m_isHVbatteryFault, m_isPwrLimitFault, m_isVdcActive, m_isMotTrqValid, m_isMotSpdValid, m_vehicleDisplaySpeed2Vlc, m_isTirePresWarning, m_isTirePresAbnormal, m_isEpbWarning, m_isHandsOffValid)

struct valInPilotVehicleInfo
{
    zos::common::Timestamp m_timestampMs;
    CHmiHaptic m_hmiHaptic;
    CVehicleInfo m_vehicleInfo;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(valInPilotVehicleInfo, m_timestampMs, m_hmiHaptic, m_vehicleInfo)

}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::ipc::CHmiHaptic, StdLayoutTag>
{
    using ValueType = ::zos::ipc::CHmiHaptic;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CHmiHaptic, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::CHmiHaptic;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CVehicleInfo, StdLayoutTag>
{
    using ValueType = ::zos::ipc::CVehicleInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::CVehicleInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::CVehicleInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::valInPilotVehicleInfo, StdLayoutTag>
{
    using ValueType = ::zos::ipc::valInPilotVehicleInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::valInPilotVehicleInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::valInPilotVehicleInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
