#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>

#include <zos_msg/common/common.hpp>
#include <zos_msg/geometry/point.hpp>
#include <zos_msg/common/timestamp.hpp>
namespace zos
{
namespace ipc
{
enum class EGearPositon
{
    GEAR_NEUTRAL = 0,
    GEAR_PARKING = 1,
    GEAR_REVERSE = 2,
    GEAR_DRIVE_1 = 3,
    GEAR_DRIVE_2 = 4,
    GEAR_DRIVE_3 = 5,
    GEAR_DRIVE_4 = 6,
    GEAR_DRIVE_5 = 7,
    GEAR_DRIVE_6 = 8,
    GEAR_DRIVE_7 = 9,
    GEAR_DRIVE_8 = 10,
    GEAR_DRIVE_9 = 11,
    <PERSON>AR_DRIVE_10 = 12,
};
enum class EEmsEngineStartStopState
{
    ENGINE_RESET = 0,
    ENGINE_STANDBY = 1,
    ENGINE_STARTER_RESTART = 2,
    ENGINE_RESTART = 3,
    ENGINE_OPERATION = 4,
    ENGINE_AUTO_STOPPING = 5,
};
enum class EEmsEngineState
{
    ENGINE_STOP = 0,
    ENGINE_CRANKING = 1,
    ENGINE_STOPPING_ONLY_FOR_PHEV = 2,
    ENGINE_RUNNING = 3,
    ENGINE_INVALID = 4,
};
enum class EEpsControlStatus
{
    EPS_CONTROL_NOT_READY = 0,
    EPS_CONTROL_READY = 1,
    EPS_CONTROL_ACTIVE = 2,
    EPS_TEMP_FAILURE = 3,
    EPS_PERM_FAILURE = 4,
};
enum class EWheelSpeedDirection
{
    WHEEL_SPEED_INVALID = 0,
    WHEEL_SPEED_FORWARD = 1,
    WHEEL_SPEED_BACKWARD = 2,
    WHEEL_SPEED_STANDSTILL = 3,
};
enum class EEspControlStatus
{
    ESP_CONTROL_NOT_READY = 0,
    ESP_CONTROL_READY = 1,
    ESP_CONTROL_ACTIVE = 2,
    ESP_TEMP_FAILURE = 3,
    ESP_PERM_FAILURE = 4,
};
struct valInPilotChassisInfo
{
    zos::common::Timestamp m_timestampMs;
    EGearPositon m_actualGearPositon;
    zos::float32_t m_gearBoxRatio;
    zos::bool_t m_isActualGearValid;
    EGearPositon m_targetGearPosition;
    zos::bool_t m_isTargetGearValid;
    zos::bool_t m_isShiftInProgress;
    zos::float32_t m_torqueMotActualWheel;
    zos::float32_t m_torqueMotDrvReqWheel;
    zos::float32_t m_torqueMotMaxWheel;
    zos::float32_t m_torqueMotMinWheel;
    zos::float32_t m_torqueMotMinwFuelCutOffWheel;
    zos::float32_t m_torqueMotorActual;
    zos::bool_t m_isDASTorReqResp;
    zos::float32_t m_motRpm;
    zos::bool_t m_isVcuReady;
    zos::bool_t m_isAccPedalValid;
    zos::bool_t m_isBpedalValid;
    zos::bool_t m_isEngineRunning;
    EEmsEngineStartStopState m_emsStartStopState;
    EEmsEngineState m_emsEngineState;
    zos::float32_t m_gasPedalPosition;
    zos::float32_t m_brakePedalPosition;
    zos::float32_t m_BMS2SOC;
    zos::float32_t m_epsTorsionBarTorque;
    zos::bool_t m_isEpsHandoffState;
    zos::bool_t m_isEpsFailed;
    EEpsControlStatus m_epsControlStatus;
    zos::float32_t m_steeringWheelAngle;
    zos::float32_t m_steeringWheelAngleSpeed;
    zos::bool_t m_steeringWheelAngleValid;
    zos::bool_t m_steeringWheelAngleSpeedValid;
    zos::bool_t m_isEspAxvValid;
    zos::float32_t m_espAxvSensor;
    zos::float32_t m_espAxvFeedback;
    zos::bool_t m_isEspAyvValid;
    zos::float32_t m_espAyvSensor;
    zos::bool_t m_isEspYawRateValid;
    zos::float32_t m_espYawRate;
    zos::bool_t m_isVehSpeedValid;
    zos::float32_t m_espVehicleSpeed;
    EWheelSpeedDirection m_espFLWheelSpeedDirection;
    EWheelSpeedDirection m_espFRWheelSpeedDirection;
    EWheelSpeedDirection m_espRLWheelSpeedDirection;
    EWheelSpeedDirection m_espRRWheelSpeedDirection;
    zos::float32_t m_espFLWheelSpeed;
    zos::float32_t m_espFRWheelSpeed;
    zos::float32_t m_espRLWheelSpeed;
    zos::float32_t m_espRRWheelSpeed;
    zos::bool_t m_espFLWheelSpeedValid;
    zos::bool_t m_espFRWheelSpeedValid;
    zos::bool_t m_espRLWheelSpeedValid;
    zos::bool_t m_espRRWheelSpeedValid;
    zos::bool_t m_isESPStandStill;
    EEspControlStatus m_espControlStatus;
    zos::float32_t m_masterCylinderPressure;
    zos::float32_t m_boosterCylinderPressure;
    zos::bool_t m_isEPSSystemAvalible;
    zos::bool_t m_epsControlMode;
    zos::uint32_t m_espFLWheelPulseCounter;
    zos::uint32_t m_espFRWheelPulseCounter;
    zos::uint32_t m_espRLWheelPulseCounter;
    zos::uint32_t m_espRRWheelPulseCounter;
    zos::uint32_t m_espFLWheelPulseCounterValid;
    zos::uint32_t m_espFRWheelPulseCounterValid;
    zos::uint32_t m_espRLWheelPulseCounterValid;
    zos::uint32_t m_espRRWheelPulseCounterValid;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(valInPilotChassisInfo, m_timestampMs, m_actualGearPositon, m_gearBoxRatio, m_isActualGearValid, m_targetGearPosition, m_isTargetGearValid, m_isShiftInProgress, m_torqueMotActualWheel, m_torqueMotDrvReqWheel, m_torqueMotMaxWheel, m_torqueMotMinWheel, m_torqueMotMinwFuelCutOffWheel, m_torqueMotorActual, m_isDASTorReqResp, m_motRpm, m_isVcuReady, m_isAccPedalValid, m_isBpedalValid, m_isEngineRunning, m_emsStartStopState, m_emsEngineState, m_gasPedalPosition, m_brakePedalPosition, m_BMS2SOC, m_epsTorsionBarTorque, m_isEpsHandoffState, m_isEpsFailed, m_epsControlStatus, m_steeringWheelAngle, m_steeringWheelAngleSpeed, m_steeringWheelAngleValid, m_steeringWheelAngleSpeedValid, m_isEspAxvValid, m_espAxvSensor, m_espAxvFeedback, m_isEspAyvValid, m_espAyvSensor, m_isEspYawRateValid, m_espYawRate, m_isVehSpeedValid, m_espVehicleSpeed, m_espFLWheelSpeedDirection, m_espFRWheelSpeedDirection, m_espRLWheelSpeedDirection, m_espRRWheelSpeedDirection, m_espFLWheelSpeed, m_espFRWheelSpeed, m_espRLWheelSpeed, m_espRRWheelSpeed, m_espFLWheelSpeedValid, m_espFRWheelSpeedValid, m_espRLWheelSpeedValid, m_espRRWheelSpeedValid, m_isESPStandStill, m_espControlStatus, m_masterCylinderPressure, m_boosterCylinderPressure, m_isEPSSystemAvalible, m_epsControlMode, m_espFLWheelPulseCounter, m_espFRWheelPulseCounter, m_espRLWheelPulseCounter, m_espRRWheelPulseCounter, m_espFLWheelPulseCounterValid, m_espFRWheelPulseCounterValid, m_espRLWheelPulseCounterValid, m_espRRWheelPulseCounterValid)

}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::ipc::valInPilotChassisInfo, StdLayoutTag>
{
    using ValueType = ::zos::ipc::valInPilotChassisInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::valInPilotChassisInfo, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::valInPilotChassisInfo;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
