#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>

#include <zos_msg/common/common.hpp>
#include <zos_msg/common/timestamp.hpp>
namespace zos
{
namespace ipc
{
enum class EDrivingDirection
{
    Forward = 0,
    Backward = 1,
    Unknow = 2,
    Standstill = 3,
};
enum class EVhmUpdateState_en
{
    Init = 0,
    Updated = 1,
    NotUpdated = 2,
    ReInit = 3,
};
struct Cpose
{
    zos::float64_t PositionX;
    zos::float64_t PositionY;
    zos::float64_t PositionZ;
    zos::float64_t YawAngle;
    zos::float64_t RollAngle;
    zos::float64_t PitchAngle;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(Cpose, PositionX, PositionY, PositionZ, YawAngle, RollAngle, PitchAngle)

struct vhmAbst2d
{
    Cpose m_vehPose;
    zos::common::Timestamp m_Timestamp;
    zos::float32_t VehicleLongitudinalAcceleration;
    zos::float32_t VehLateralAcceleration;
    zos::float32_t YawAngleRate;
    zos::float32_t Kappa;
    zos::float32_t Velocity;
    zos::float32_t VelocityX;
    zos::float32_t VelocityY;
    zos::float64_t DrivenDist;
    EDrivingDirection VehMovingDirection;
    EDrivingDirection VehDrivingDirection;
    EVhmUpdateState_en DRUpdateState;
    zos::uint16_t ErrCode;
    Cpose reinitPose;
    zos::float32_t reserve1;
    zos::float32_t reserve2;
    zos::uint32_t reserve3;
    zos::uint32_t reserve4;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(vhmAbst2d, m_vehPose, m_Timestamp, VehicleLongitudinalAcceleration, VehLateralAcceleration, YawAngleRate, Kappa, Velocity, VelocityX, VelocityY, DrivenDist, VehMovingDirection, VehDrivingDirection, DRUpdateState, ErrCode, reinitPose, reserve1, reserve2, reserve3, reserve4)

}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::ipc::Cpose, StdLayoutTag>
{
    using ValueType = ::zos::ipc::Cpose;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::Cpose, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::Cpose;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::vhmAbst2d, StdLayoutTag>
{
    using ValueType = ::zos::ipc::vhmAbst2d;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ipc::vhmAbst2d, StdLayoutTruncTag>
{
    using ValueType = ::zos::ipc::vhmAbst2d;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
