#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>
#include <basic_types/container/vector.hpp>

#include <zos_msg/common/common.hpp>
namespace zos
{
namespace ccw
{
namespace codingmgr
{
enum class EVehicleVariants : zos::uint32_t
{
    VEH_default = 0,
    VEH_Chery_M36T = 1,
    VEH_Chery_M32T = 2,
    VEH_Chery_M36T_International = 3,
    VEH_Chery_M36T_NewFormerFace = 4,
    VEH_Chery_M32T_International = 5,
    VEH_Chery_M36T_PHEV_HIGH_domestic = 6,
    VEH_Chery_M36T_PHEV_HIGH_International = 7,
    VEH_Chery_M36T_ICE_HIGH_domestic = 8,
    VEH_Chery_M36T_ICE_HIGH_International = 9,
    VEH_Chery_M32T_PHEV_HIGH_domestic = 16,
    VEH_Chery_M32T_PHEV_HIGH_International = 17,
    VEH_Chery_M32T_ICE_HIGH_domestic = 18,
    VEH_Chery_M32T_ICE_HIGH_International = 19,
    VEH_Chery_M36T_PHEV_HIGH_domestic_POC = 20,
    VEH_Chery_T22 = 257,
    VEH_Chery_T26 = 258,
    VEH_Chery_T28 = 259,
    VEH_Chery_T22_PHEV = 260,
    VEH_Chery_T26_PHEV = 261,
    VEH_Chery_T22_POC = 262,
    VEH_Chery_E03_BEV = 513,
    VEH_Chery_E0Y_BEV = 514,
    VEH_Chery_E03_REV = 515,
    VEH_Chery_E0Y_REV = 516,
    VEH_Chery_E03_HBEV = 517,
    VEH_Chery_E0Y_HBEV = 518,
    VEH_Chery_E03_HREV = 519,
    VEH_Chery_E0Y_HREV = 520,
    VEH_Chery_V23 = 769,
    VEH_Chery_PF1 = 770,
    VEH_Chery_CYBER = 771,
    VEH_Chery_V23SEA = 772,
    VEH_Chery_V23SEACYBER = 773,
    VEH_Chery_T1GC = 272,
    VEH_Chery_T1GC_PHEV = 273,
    VEH_Chery_T1J = 288,
    VEH_Chery_T1J_PHEV = 289,
    VEH_XPHT_X3 = 1281,
    VEH_DFLZM_M6 = 4097,
    VEH_DFLZM_M6_MasterStyle = 4098,
    VEH_DFLZM_S50EVK = 4353,
    VEH_EEZI_UMO = 8193,
    VEH_EEZI_VC = 8194,
    VEH_ZEEKER_Z001 = 12289,
    VEH_BYD_HAN_DMP = 16385,
    VEH_JXEV_C5 = 20481,
    VEH_JXEV_PF = 20482,
    VEH_VINFAST_VF08 = 24577,
    VEH_VINFAST_VF09 = 24578,
    VEH_All = 24579,
};
enum class EVehicleColor : zos::uint32_t
{
    EVEHICLE_COLOR_DEFAULT = 0,
    EVEHICLE_COLOR_RED = 1,
    EVEHICLE_COLOR_GREEN = 2,
    EVEHICLE_COLOR_WHITE = 3,
    EVEHICLE_COLOR_YELLOW = 4,
    EVEHICLE_COLOR_SILVERY = 5,
    EVEHICLE_COLOR_GRAY = 6,
    EVEHICLE_COLOR_BLUE = 7,
    EVEHICLE_COLOR_BLACK = 8,
    EVEHICLE_COLOR_PINK = 9,
    EVEHICLE_COLOR_GOLDEN = 10,
    EVEHICLE_COLOR_ORANGE = 11,
    EVEHICLE_COLOR_SPECIAL4 = 12,
    EVEHICLE_COLOR_SPECIAL5 = 13,
    EVEHICLE_COLOR_All = 14,
};
enum class EVehicleDriveModeEnum : zos::uint32_t
{
    EVehicleDriveModeEnum_DEFAULT = 0,
    EVehicleDriveModeEnum_FR = 1,
    EVehicleDriveModeEnum_FF = 2,
    EVehicleDriveModeEnum_RR = 3,
    EVehicleDriveModeEnum_MR = 4,
    EVehicleDriveModeEnum_4WD = 5,
    EVehicleDriveModeEnum_FT4WD = 6,
    EVehicleDriveModeEnum_RT4WD = 7,
    EVehicleDriveModeEnum_PT4WD = 8,
    EVehicleDriveModeEnum_FT6WD = 9,
    EVehicleDriveModeEnum_All = 10,
};
enum class EVehicleRudderTypeEnum : zos::uint32_t
{
    EVehicleRudderTypeEnum_LEFT = 0,
    EVehicleRudderTypeEnum_RIGHT = 1,
    EVehicleRudderTypeEnum_All = 2,
};
enum class EVehicleTransTypeEnum : zos::uint32_t
{
    EVehicleTransTypeEnum_7DCT = 0,
    EVehicleTransTypeEnum_CVT = 1,
    EVehicleTransTypeEnum_6DCT = 2,
    EVehicleTransTypeEnum_7HDT = 3,
    EVehicleTransTypeEnum_8AT = 4,
    EVehicleTransTypeEnum_3DHT = 5,
    EVehicleTransTypeEnum_1DHT = 6,
    EVehicleTransTypeEnum_All = 7,
};
enum class EVehicleEngineModelEnum : zos::uint32_t
{
    EVehicleEngineModelEnum_FUEL = 0,
    EVehicleEngineModelEnum_PHEV = 1,
    EVehicleEngineModelEnum_EV = 2,
    EVehicleEngineModelEnum_All = 3,
};
enum class EVehicleAirSupplyUnitEnum : zos::uint32_t
{
    EVehicleAirSupplyUnitEnum_NO_PRESENT = 0,
    EVehicleAirSupplyUnitEnum_PRESENT = 1,
    EVehicleAirSupplyUnitEnum_All = 2,
};
enum class ESysNMEnum : zos::uint32_t
{
    ESysNMEnum_NONE = 0,
    ESysNMEnum_Autosar = 1,
    ESysNMEnum_OSEK = 2,
};
enum class ESysVideoEnum : zos::uint32_t
{
    ESysVideoEnum_1920_1280 = 0,
    ESysVideoEnum_1920_720 = 1,
    ESysVideoEnum_1280_720 = 2,
    ESysVideoEnum_2560_1440 = 3,
};
enum class ESysUSSEnum : zos::uint32_t
{
    ESysUSSEnum_NONE = 0,
    ESysUSSEnum_AK1 = 1,
    ESysUSSEnum_AK2 = 2,
};
enum class ESysETHEnum : zos::uint32_t
{
    ESysETHEnum_NONE = 0,
    ESysETHEnum_100BASET1 = 1,
    ESysETHEnum_1000BASET1 = 2,
};
enum class ESysCANEnum : zos::uint32_t
{
    ESysCANEnum_NONE = 0,
    ESysCANEnum_CAN = 1,
    ESysCANEnum_CANFD = 2,
};
enum class EVehicleTouchSignalEnum : zos::uint32_t
{
    EVehicleTouchSignalEnum_UART = 0,
    EVehicleTouchSignalEnum_CAN = 1,
};
enum class EVehicleTyreEnum : zos::uint8_t
{
    EVehicleTyreEnum_215_65_R16 = 0,
    EVehicleTyreEnum_215_60_R17 = 1,
    EVehicleTyreEnum_215_55_R18 = 2,
    EVehicleTyreEnum_225_60_R18 = 3,
    EVehicleTyreEnum_225_55_R19 = 4,
    EVehicleTyreEnum_235_60_R17 = 5,
    EVehicleTyreEnum_235_55_R18 = 6,
    EVehicleTyreEnum_235_50_R19 = 7,
    EVehicleTyreEnum_235_60_R18 = 8,
    EVehicleTyreEnum_245_55_R19 = 9,
    EVehicleTyreEnum_245_50_R20 = 10,
    EVehicleTyreEnum_205_60_R16 = 11,
    EVehicleTyreEnum_215_50_R17 = 12,
    EVehicleTyreEnum_225_45_R18 = 13,
    EVehicleTyreEnum_235_45_R18 = 14,
    EVehicleTyreEnum_245_45_R21 = 15,
    EVehicleTyreEnum_235_55_R19 = 16,
    EVehicleTyreEnum_245_45_R20 = 17,
    EVehicleTyreEnum_225_55_R18 = 18,
    EVehicleTyreEnum_215_55_R17 = 19,
    EVehicleTyreEnum_255_55_R19 = 20,
    EVehicleTyreEnum_255_50_R20 = 21,
    EVehicleTyreEnum_265_45_R21 = 22,
    EVehicleTyreEnum_245_55_R18 = 23,
    EVehicleTyreEnum_245_50_R19 = 24,
    EVehicleTyreEnum_255_40_R21 = 25,
    EVehicleTyreEnum_255_65_R17 = 26,
};
enum class EVehicleBrakeModelEnum : zos::uint8_t
{
    EVehicleBrakeModelEnum_ABS_EBD = 0,
    EVehicleBrakeModelEnum_ESP = 1,
    EVehicleBrakeModelEnum_ESP_HDC = 2,
    EVehicleBrakeModelEnum_Onebox = 3,
};
enum class EVehicleCountryEnum : zos::uint8_t
{
    EVehicleCountryEnum_China = 0,
    EVehicleCountryEnum_England = 1,
    EVehicleCountryEnum_Iraq = 2,
    EVehicleCountryEnum_Argentina = 3,
    EVehicleCountryEnum_Chile = 4,
    EVehicleCountryEnum_Indonesia = 5,
    EVehicleCountryEnum_SouthAfrican = 6,
    EVehicleCountryEnum_Malaysia = 7,
    EVehicleCountryEnum_Algeria = 8,
    EVehicleCountryEnum_Brazil = 9,
    EVehicleCountryEnum_CentralAsia = 10,
    EVehicleCountryEnum_Uruguay = 11,
    EVehicleCountryEnum_Columbia = 12,
    EVehicleCountryEnum_Peru = 13,
    EVehicleCountryEnum_gulf = 14,
    EVehicleCountryEnum_Russia = 15,
    EVehicleCountryEnum_Egypt = 16,
    EVehicleCountryEnum_Italy = 17,
    EVehicleCountryEnum_Azerbaijan = 18,
    EVehicleCountryEnum_Ukraine = 19,
    EVehicleCountryEnum_Ecuador = 20,
    EVehicleCountryEnum_Mexico = 21,
    EVehicleCountryEnum_Senegal = 22,
    EVehicleCountryEnum_Libya = 23,
    EVehicleCountryEnum_Nigeria = 24,
    EVehicleCountryEnum_Syria = 25,
    EVehicleCountryEnum_Bolivia = 26,
    EVehicleCountryEnum_Norway = 27,
    EVehicleCountryEnum_philippine = 28,
    EVehicleCountryEnum_SaudiArabia = 29,
    EVehicleCountryEnum_America = 30,
    EVehicleCountryEnum_Pakistan = 31,
    EVehicleCountryEnum_Thailand = 32,
    EVehicleCountryEnum_sultan = 33,
    EVehicleCountryEnum_Kazakhstan = 34,
    EVehicleCountryEnum_India = 35,
    EVehicleCountryEnum_NewZealand = 36,
    EVehicleCountryEnum_Australia = 37,
    EVehicleCountryEnum_Myanmar = 38,
    EVehicleCountryEnum_Turkey = 39,
    EVehicleCountryEnum_Israel = 40,
    EVehicleCountryEnum_serbia = 41,
    EVehicleCountryEnum_Ethiopia = 42,
    EVehicleCountryEnum_Vietnam = 43,
    EVehicleCountryEnum_NorthAmerica = 44,
    EVehicleCountryEnum_Tunisia = 45,
    EVehicleCountryEnum_Uzbekistan = 46,
    EVehicleCountryEnum_Nepal = 47,
    EVehicleCountryEnum_Bangladesh = 48,
    EVehicleCountryEnum_UnitedArabEmirates = 49,
    EVehicleCountryEnum_Spain = 50,
    EVehicleCountryEnum_CostaRica = 51,
    EVehicleCountryEnum_Venezuela = 52,
    EVehicleCountryEnum_Germany = 53,
    EVehicleCountryEnum_Poland = 54,
    EVehicleCountryEnum_SriLanka = 55,
    EVehicleCountryEnum_Singapore = 56,
    EVehicleCountryEnum_HongKong = 57,
    EVehicleCountryEnum_AoMen = 58,
    EVehicleCountryEnum_TaiWan = 59,
    EVehicleCountryEnum_Jordan = 60,
    EVehicleCountryEnum_Paraguay = 61,
    EVehicleCountryEnum_Morocco = 62,
    EVehicleCountryEnum_Katar = 63,
    EVehicleCountryEnum_Kuwait = 64,
    EVehicleCountryEnum_Bahrain = 65,
    EVehicleCountryEnum_France = 66,
    EVehicleCountryEnum_Belgium = 67,
    EVehicleCountryEnum_CotedIvoire = 68,
};
enum class EVehicleInteriorColorEnum : zos::uint8_t
{
    EVehicleInteriorColorEnum_DEFAULT = 0,
    EVehicleInteriorColorEnum_RED = 1,
    EVehicleInteriorColorEnum_GREEN = 2,
    EVehicleInteriorColorEnum_WHITE = 3,
    EVehicleInteriorColorEnum_YELLOW = 4,
    EVehicleInteriorColorEnum_SILVERY = 5,
    EVehicleInteriorColorEnum_GRAY = 6,
    EVehicleInteriorColorEnum_BLUE = 7,
    EVehicleInteriorColorEnum_BLACK = 8,
    EVehicleInteriorColorEnum_PINK = 9,
    EVehicleInteriorColorEnum_GOLDEN = 10,
    EVehicleInteriorColorEnum_ORANGE = 11,
    EVehicleInteriorColorEnum_SPECIAL4 = 12,
    EVehicleInteriorColorEnum_SPECIAL5 = 13,
    EVehicleInteriorColorEnum_All = 14,
};
enum class EVehicleFrontSpringEnum : zos::uint8_t
{
    EVehicleFrontSpringEnum_DEFAULT = 0,
    EVehicleFrontSpringEnum_COIL = 1,
    EVehicleFrontSpringEnum_AIR = 2,
    EVehicleFrontSpringEnum_All = 3,
};
enum class EVehicleRearSpringEnum : zos::uint8_t
{
    EVehicleRearSpringEnum_DEFAULT = 0,
    EVehicleRearSpringEnum_COIL = 1,
    EVehicleRearSpringEnum_AIR = 2,
    EVehicleRearSpringEnum_All = 3,
};
enum class EVehiclePlatformEnum : zos::uint8_t
{
    EVehiclePlatformType_5x = 0,
    EVehiclePlatformType_4x = 1,
};
enum class EVehicleHREVMileageEnum : zos::uint8_t
{
    EVehicleHREVMileage_200 = 0,
    EVehicleHREVMileage_300 = 1,
    EVehicleHREVMileage_250 = 2,
};
enum class EVehicleExteriorStyleEnum : zos::uint8_t
{
    EVehicleExteriorStyle_Old = 0,
    EVehicleExteriorStyle_International_Old = 1,
    EVehicleExteriorStyle_International_New = 2,
};
enum class EVehicleFcmEnum : zos::uint8_t
{
    EVehicleFcmLow = 0,
    EVehicleFcmHigh = 1,
};
enum class EVehicleRegulatoryLevelEnum : zos::uint8_t
{
    EVehicleRegulatoryLevelLow = 0,
    EVehicleRegulatoryLevelHigh = 1,
};
struct CLoggingSetting
{
    zos::uint8_t m_logging_Cpf;
    zos::uint8_t m_logging_Ipark;
    zos::uint8_t m_logging_Ipilot;
    zos::uint8_t m_logging_Iwalle;
    zos::uint8_t m_logging_Iuni;
    zos::uint8_t m_logging_Viper;
    zos::uint8_t m_logging_Vial;
    zos::uint8_t m_logging_DaloSer;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CLoggingSetting, m_logging_Cpf, m_logging_Ipark, m_logging_Ipilot, m_logging_Iwalle, m_logging_Iuni, m_logging_Viper, m_logging_Vial, m_logging_DaloSer)

struct CCodingFunctional
{
    zos::uint8_t m_APAEnable_u8;
    zos::uint8_t m_HPAEnable_u8;
    zos::uint8_t m_RPAEnable_u8;
    zos::uint8_t m_SVSEnable_u8;
    zos::uint8_t m_TRAEnable_u8;
    zos::uint8_t m_ACCEnable_u8;
    zos::uint8_t m_LKAEnable_u8;
    zos::uint8_t m_TJAEnable_u8;
    zos::uint8_t m_AEBEnable_u8;
    zos::uint8_t m_LDWEnable_u8;
    zos::uint8_t m_FCWEnable_u8;
    zos::uint8_t m_TSREnable_u8;
    zos::uint8_t m_LDPEnable_u8;
    zos::uint8_t m_NOAEnable_u8;
    zos::uint8_t m_AHLEnable_u8;
    zos::uint8_t m_TLAEnable_u8;
    zos::uint8_t m_ILAEnable_u8;
    zos::uint8_t m_FUSIONEnable_u8;
    zos::uint8_t m_CVEnable_u8;
    zos::uint8_t m_FCTAEnable_u8;
    zos::uint8_t m_RCTAEnable_u8;
    zos::uint8_t m_CTAEnableDuringAVP_u8;
    zos::uint8_t m_MSPEnableDuringGuidance_u8;
    zos::uint8_t m_MSPEnableOutsideGuidance_u8;
    zos::uint8_t m_MSPEnableDuringAVP_u8;
    zos::uint8_t m_MSPEnableFront_u8;
    zos::uint8_t m_AVPEnable_u8;
    zos::uint8_t m_PTSEnable_u8;
    zos::uint8_t m_PTSEnableSide_u8;
    zos::uint8_t m_DAAEnable_u8;
    zos::uint8_t m_PSIEnable_u8;
    zos::uint8_t m_AAAEnable_u8;
    zos::uint8_t m_CBPEnable_u8;
    zos::uint8_t m_HITEnable_u8;
    zos::uint8_t m_Dev_VideoOutMode_u8;
    zos::uint8_t m_FDDEnable_u8;
    zos::uint8_t m_VHM2Enable_u8;
    zos::uint8_t m_PDDEnable_u8;
    zos::uint8_t m_FrontCamKinematicAvailable_u8;
    zos::uint8_t m_CamouflageEnable_u8;
    zos::uint8_t m_NoSleep_u8;
    zos::uint8_t m_Lidar_u8;
    zos::uint8_t m_FUSALimiter_u8;
    zos::uint8_t m_AutoDetectionSVSBox_u8;
    zos::uint8_t m_DDCEnable_u8;
    zos::uint8_t m_MEBEnableFront_u8;
    zos::uint8_t m_MEBEnableRear_u8;
    zos::uint8_t m_IHCEnable_u8;
    zos::uint8_t m_ELKEnable_u8;
    zos::uint8_t m_HWAEnable_u8;
    zos::uint8_t m_BUTEnable_u8;
    zos::uint8_t m_CPAEnable_u8;
    zos::uint8_t m_SDSEnable_u8;
    zos::uint8_t m_EXPIMDEnable_u8;
    zos::uint8_t m_TTAEnable_u8;
    zos::uint8_t m_SDWEnable_u8;
    zos::uint8_t m_PDCEnableFront_u8;
    zos::uint8_t m_PDCEnableRear_u8;
    zos::uint8_t m_AVMEnable_u8;
    zos::uint8_t m_ARHMIEnable_u8;
    zos::uint8_t m_DVREnable_u8;
    zos::uint8_t m_EDREnable_u8;
    zos::uint8_t m_REMEnable_u8;
    zos::uint8_t m_FCTBEnable_u8;
    zos::uint8_t m_RCTBEnable_u8;
    zos::uint8_t m_ISAEnable_u8;
    zos::uint8_t m_TCPEnable_u8;
    zos::uint8_t m_RCWEnable_u8;
    zos::uint8_t m_BSDEnable_u8;
    zos::uint8_t m_DOWEnable_u8;
    zos::uint8_t m_LCAEnable_u8;
    zos::uint8_t m_Function9Enable_u8;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CCodingFunctional, m_APAEnable_u8, m_HPAEnable_u8, m_RPAEnable_u8, m_SVSEnable_u8, m_TRAEnable_u8, m_ACCEnable_u8, m_LKAEnable_u8, m_TJAEnable_u8, m_AEBEnable_u8, m_LDWEnable_u8, m_FCWEnable_u8, m_TSREnable_u8, m_LDPEnable_u8, m_NOAEnable_u8, m_AHLEnable_u8, m_TLAEnable_u8, m_ILAEnable_u8, m_FUSIONEnable_u8, m_CVEnable_u8, m_FCTAEnable_u8, m_RCTAEnable_u8, m_CTAEnableDuringAVP_u8, m_MSPEnableDuringGuidance_u8, m_MSPEnableOutsideGuidance_u8, m_MSPEnableDuringAVP_u8, m_MSPEnableFront_u8, m_AVPEnable_u8, m_PTSEnable_u8, m_PTSEnableSide_u8, m_DAAEnable_u8, m_PSIEnable_u8, m_AAAEnable_u8, m_CBPEnable_u8, m_HITEnable_u8, m_Dev_VideoOutMode_u8, m_FDDEnable_u8, m_VHM2Enable_u8, m_PDDEnable_u8, m_FrontCamKinematicAvailable_u8, m_CamouflageEnable_u8, m_NoSleep_u8, m_Lidar_u8, m_FUSALimiter_u8, m_AutoDetectionSVSBox_u8, m_DDCEnable_u8, m_MEBEnableFront_u8, m_MEBEnableRear_u8, m_IHCEnable_u8, m_ELKEnable_u8, m_HWAEnable_u8, m_BUTEnable_u8, m_CPAEnable_u8, m_SDSEnable_u8, m_EXPIMDEnable_u8, m_TTAEnable_u8, m_SDWEnable_u8, m_PDCEnableFront_u8, m_PDCEnableRear_u8, m_AVMEnable_u8, m_ARHMIEnable_u8, m_DVREnable_u8, m_EDREnable_u8, m_REMEnable_u8, m_FCTBEnable_u8, m_RCTBEnable_u8, m_ISAEnable_u8, m_TCPEnable_u8, m_RCWEnable_u8, m_BSDEnable_u8, m_DOWEnable_u8, m_LCAEnable_u8, m_Function9Enable_u8)

using VehicleReserved=zos::FixedVector<zos::uint8_t, 5>;
struct CCodingVariantOutput
{
    zos::uint32_t m_validFlag;
    zos::uint32_t m_ccwType;
    zos::uint32_t m_ccwdataLength;
    CCodingFunctional m_functionsVariant;
    ESysNMEnum m_sysNM;
    ESysVideoEnum m_sysVideo;
    ESysUSSEnum m_sysUSS;
    ESysETHEnum m_sysETH0;
    ESysETHEnum m_sysETH1;
    ESysCANEnum m_sysCAN1;
    ESysCANEnum m_sysCAN2;
    ESysCANEnum m_sysCAN3;
    ESysCANEnum m_sysCAN4;
    ESysCANEnum m_sysCAN5;
    ESysCANEnum m_sysCAN6;
    zos::uint32_t m_sysEcuConfig1;
    zos::uint32_t m_sysEcuConfig2;
    zos::uint32_t m_sysEcuConfig3;
    zos::uint32_t m_sysEcuConfig4;
    zos::uint32_t m_sysEcuConfig5;
    zos::uint32_t m_sysEcuConfig6;
    EVehicleVariants m_vehicleVariant;
    EVehicleEngineModelEnum m_vehicleEngineMode;
    EVehicleTransTypeEnum m_vehicleTransType;
    EVehicleDriveModeEnum m_vehicleDriveMode;
    EVehicleAirSupplyUnitEnum m_vehicleAirSupplyUnit;
    EVehicleRudderTypeEnum m_vehicleRudderType;
    EVehicleColor m_vehicleColor;
    EVehicleTouchSignalEnum m_vehicleTouchSignal;
    zos::uint32_t m_riseCoding;
    EVehicleTyreEnum m_vehicleTyre;
    EVehicleBrakeModelEnum m_vehicleBrakeModel;
    EVehicleCountryEnum m_vehicleCountry;
    EVehicleInteriorColorEnum m_vehicleInteriorColor;
    EVehicleFrontSpringEnum m_vehicleFrontSpring;
    EVehicleRearSpringEnum m_vehicleRearSpring;
    EVehiclePlatformEnum m_vehiclePlatform;
    EVehicleHREVMileageEnum m_vehicleHREVMileage;
    EVehicleExteriorStyleEnum m_vehicleExteriorStyle;
    EVehicleFcmEnum m_vehicleFcm;
    CLoggingSetting m_loggingSetting;
    EVehicleRegulatoryLevelEnum m_vehicleRegulatoryLevel;
    VehicleReserved m_vehicleReserved;
    zos::uint32_t m_ccwCheckSum;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CCodingVariantOutput, m_validFlag, m_ccwType, m_ccwdataLength, m_functionsVariant, m_sysNM, m_sysVideo, m_sysUSS, m_sysETH0, m_sysETH1, m_sysCAN1, m_sysCAN2, m_sysCAN3, m_sysCAN4, m_sysCAN5, m_sysCAN6, m_sysEcuConfig1, m_sysEcuConfig2, m_sysEcuConfig3, m_sysEcuConfig4, m_sysEcuConfig5, m_sysEcuConfig6, m_vehicleVariant, m_vehicleEngineMode, m_vehicleTransType, m_vehicleDriveMode, m_vehicleAirSupplyUnit, m_vehicleRudderType, m_vehicleColor, m_vehicleTouchSignal, m_riseCoding, m_vehicleTyre, m_vehicleBrakeModel, m_vehicleCountry, m_vehicleInteriorColor, m_vehicleFrontSpring, m_vehicleRearSpring, m_vehiclePlatform, m_vehicleHREVMileage, m_vehicleExteriorStyle, m_vehicleFcm, m_loggingSetting, m_vehicleRegulatoryLevel, m_vehicleReserved, m_ccwCheckSum)

}
}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::ccw::codingmgr::CLoggingSetting, StdLayoutTag>
{
    using ValueType = ::zos::ccw::codingmgr::CLoggingSetting;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ccw::codingmgr::CLoggingSetting, StdLayoutTruncTag>
{
    using ValueType = ::zos::ccw::codingmgr::CLoggingSetting;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ccw::codingmgr::CCodingFunctional, StdLayoutTag>
{
    using ValueType = ::zos::ccw::codingmgr::CCodingFunctional;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ccw::codingmgr::CCodingFunctional, StdLayoutTruncTag>
{
    using ValueType = ::zos::ccw::codingmgr::CCodingFunctional;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ccw::codingmgr::CCodingVariantOutput, StdLayoutTag>
{
    using ValueType = ::zos::ccw::codingmgr::CCodingVariantOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::ccw::codingmgr::CCodingVariantOutput, StdLayoutTruncTag>
{
    using ValueType = ::zos::ccw::codingmgr::CCodingVariantOutput;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
