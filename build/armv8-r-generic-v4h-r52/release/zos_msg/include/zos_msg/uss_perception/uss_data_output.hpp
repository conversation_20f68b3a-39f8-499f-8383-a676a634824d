#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>
#include <basic_types/container/array.hpp>

namespace zos
{
namespace uss_perception
{
enum class USS_DepthRefType_en
{
    uss_DepthVirtual_enm = 0,
    uss_DepthLow_enm = 1,
    uss_DepthHigh_enm = 2,
    uss_DepthCurb_enm = 3,
    uss_DepthWall_enm = 4,
    uss_DepthVideoLine_enm = 5,
    uss_DepthUnknown_enm = 6,
};
enum class USS_RefType_en
{
    uss_RefVirtual_enm = 0,
    uss_RefReal_enm = 1,
    uss_RefShort_enm = 2,
    uss_RefPillar_enm = 3,
};
enum class USS_Side_en
{
    uss_PS_LeftSide_enm = 0,
    uss_PS_RightSide_enm = 1,
    uss_PS_BothSides_enm = 2,
};
enum class USS_PSType_en
{
    uss_PS_None_enm = 0,
    uss_PS_Parallel_enm = 1,
    uss_PS_Cross_enm = 2,
    uss_PS_Diagonal_enm = 3,
};
struct USS_PointFl_st
{
    zos::float32_t X_f32;
    zos::float32_t Y_f32;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(USS_PointFl_st, X_f32, Y_f32)

struct USS_LineSegmentFl_st
{
    USS_PointFl_st P1_st;
    USS_PointFl_st P2_st;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(USS_LineSegmentFl_st, P1_st, P2_st)

struct USS_DepthRef_st
{
    USS_LineSegmentFl_st LineSegment_st;
    USS_DepthRefType_en DepthRefType_en;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(USS_DepthRef_st, LineSegment_st, DepthRefType_en)

struct USS_Obj_st
{
    USS_PointFl_st ObjCorner_st;
    zos::float32_t ObjLenght_f32;
    USS_RefType_en ObjType_en;
    zos::int16_t Orientation_s16;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(USS_Obj_st, ObjCorner_st, ObjLenght_f32, ObjType_en, Orientation_s16)

struct USS_PSObjInfo_st
{
    USS_DepthRef_st DepthRef1_st;
    USS_DepthRef_st DepthRef2_st;
    USS_DepthRef_st DepthRef3_st;
    USS_DepthRef_st DepthRef4_st;
    USS_Obj_st Obj1_st;
    USS_Obj_st Obj2_st;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(USS_PSObjInfo_st, DepthRef1_st, DepthRef2_st, DepthRef3_st, DepthRef4_st, Obj1_st, Obj2_st)

struct USS_PSInfo_st
{
    USS_PSObjInfo_st PSObj_st;
    zos::uint16_t Length_u16;
    zos::int16_t StreetOrient_s16;
    zos::uint8_t ID_u8;
    zos::uint64_t ID_u64;
    zos::int16_t Angle_s16;
    USS_Side_en Side_en;
    USS_PSType_en PSType_en;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(USS_PSInfo_st, PSObj_st, Length_u16, StreetOrient_s16, ID_u8, ID_u64, Angle_s16, Side_en, PSType_en)

using PSArray=zos::Array<zos::Array<USS_PSInfo_st, 2>, 2>;
struct USS_ParkingSpace_st
{
    PSArray PSInfo_pst;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(USS_ParkingSpace_st, PSInfo_pst)

struct USS_PDCOutput_st
{
    zos::uint16_t IDM_PDC_RHSRSensorDis_Dis_u16;
    zos::uint16_t IDM_PDC_LHSRSensorDis_Dis_u16;
    zos::uint16_t IDM_PDC_RHSFSensorDis_Dis_u16;
    zos::uint16_t IDM_PDC_LHSFSensorDis_Dis_u16;
    zos::uint16_t IDM_PDC_RHRSSensorDis_Dis_u16;
    zos::uint16_t IDM_PDC_LHRSSensorDis_Dis_u16;
    zos::uint16_t IDM_PDC_RHFSSensorDis_Dis_u16;
    zos::uint16_t IDM_PDC_LHFSSensorDis_Dis_u16;
    zos::uint16_t IDM_PDC_RHMRSensorDis_Dis_u16;
    zos::uint16_t IDM_PDC_LHMRSensorDis_Dis_u16;
    zos::uint16_t IDM_PDC_RHMFSensorDis_Dis_u16;
    zos::uint16_t IDM_PDC_LHMFSensorDis_Dis_u16;
    zos::uint8_t IDM_PDC_WarnBpRate_R_u8;
    zos::uint8_t IDM_PDC_WarnBpRate_F_u8;
    zos::uint8_t IDM_PDC_SysSts_u8;
    zos::uint8_t IDM_PDC_DetecSts_u8;
    zos::uint32_t IDM_PDC_Reserve1;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(USS_PDCOutput_st, IDM_PDC_RHSRSensorDis_Dis_u16, IDM_PDC_LHSRSensorDis_Dis_u16, IDM_PDC_RHSFSensorDis_Dis_u16, IDM_PDC_LHSFSensorDis_Dis_u16, IDM_PDC_RHRSSensorDis_Dis_u16, IDM_PDC_LHRSSensorDis_Dis_u16, IDM_PDC_RHFSSensorDis_Dis_u16, IDM_PDC_LHFSSensorDis_Dis_u16, IDM_PDC_RHMRSensorDis_Dis_u16, IDM_PDC_LHMRSensorDis_Dis_u16, IDM_PDC_RHMFSensorDis_Dis_u16, IDM_PDC_LHMFSensorDis_Dis_u16, IDM_PDC_WarnBpRate_R_u8, IDM_PDC_WarnBpRate_F_u8, IDM_PDC_SysSts_u8, IDM_PDC_DetecSts_u8, IDM_PDC_Reserve1)

enum class USS_RODObjType_en
{
    uss_ROD_NONE_enm = 0,
    uss_ROD_DE_enm = 1,
    uss_ROD_DE_DE_enm = 2,
    uss_ROD_CE_enm = 3,
    uss_ROD_DE_CE_enm = 4,
    uss_ROD_ROUND_POST_enm = 5,
    uss_ROD_WALL_enm = 6,
};
struct USS_Point_st
{
    zos::int16_t X_s16;
    zos::int16_t Y_s16;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(USS_Point_st, X_s16, Y_s16)

struct USS_LineSegment_st
{
    USS_Point_st P1_st;
    USS_Point_st P2_st;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(USS_LineSegment_st, P1_st, P2_st)

struct USS_ROD_SensorSource_st
{
    zos::uint8_t TxSensorID_u8;
    zos::uint8_t RxSensorID_u8;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(USS_ROD_SensorSource_st, TxSensorID_u8, RxSensorID_u8)

struct USS_ROD_measObjectInfo_st
{
    USS_LineSegment_st Coords_st;
    zos::uint16_t DETx_u16;
    zos::uint16_t DERx_u16;
    zos::uint16_t CE_u16;
    USS_ROD_SensorSource_st SensorSource_st;
    USS_RODObjType_en measObjType_en;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(USS_ROD_measObjectInfo_st, Coords_st, DETx_u16, DERx_u16, CE_u16, SensorSource_st, measObjType_en)

struct USS_ROD_measObjHeightInfo_st
{
    zos::uint8_t Probability_u8;
    zos::uint8_t Certainty_u8;
    zos::uint8_t Estimation_u8;
    zos::uint8_t MaxError_u8;
    zos::uint8_t CeilingBeamProb_u8;
    zos::uint8_t CeilingBeamProbUncertain_b;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(USS_ROD_measObjHeightInfo_st, Probability_u8, Certainty_u8, Estimation_u8, MaxError_u8, CeilingBeamProb_u8, CeilingBeamProbUncertain_b)

struct USS_ROD_ObiectPostionFlags_st
{
    zos::uint8_t reserved_u8;
    zos::uint8_t VehInside_u8;
    zos::uint8_t VehRight_u8;
    zos::uint8_t VehLeft_u8;
    zos::uint8_t VehRear_u8;
    zos::uint8_t VehFront_u8;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(USS_ROD_ObiectPostionFlags_st, reserved_u8, VehInside_u8, VehRight_u8, VehLeft_u8, VehRear_u8, VehFront_u8)

struct USS_ROD_measObject_st
{
    USS_ROD_measObjectInfo_st measObjectInfo_st;
    USS_ROD_measObjHeightInfo_st HeightInfo_st;
    zos::uint32_t ECUTimeL_u32;
    zos::uint32_t ECUTimeH_u32;
    zos::uint16_t SensDist_u16;
    zos::uint16_t ObjLength_u16;
    USS_ROD_ObiectPostionFlags_st ObjPosFlags_st;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(USS_ROD_measObject_st, measObjectInfo_st, HeightInfo_st, ECUTimeL_u32, ECUTimeH_u32, SensDist_u16, ObjLength_u16, ObjPosFlags_st)

struct USS_VehPos16_st
{
    zos::int16_t DrivenDistanceCM_s16;
    zos::int16_t XPositionCM_s16;
    zos::int16_t YPositionCM_s16;
    zos::uint16_t YawAngleRAD_F10_u16;
    zos::int16_t YawAngleRAD_F12_s16;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(USS_VehPos16_st, DrivenDistanceCM_s16, XPositionCM_s16, YPositionCM_s16, YawAngleRAD_F10_u16, YawAngleRAD_F12_s16)

using RODObjsArray=zos::Array<USS_ROD_measObject_st, 17>;
struct USS_ROD_measObjMonitor_st
{
    RODObjsArray MeasObjList_pst;
    zos::uint16_t numValidObjs_u16;
    USS_VehPos16_st refVehPos_st;
    zos::uint32_t Reserve1;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(USS_ROD_measObjMonitor_st, MeasObjList_pst, numValidObjs_u16, refVehPos_st, Reserve1)

enum class USS_EchoType_FreqMode_en
{
    uss_EchoType_None_enm = 0,
    uss_EchoType_Fixed_enm = 1,
    uss_EchoType_Chirp_enm = 2,
    uss_EchoType_Max_enm = 3,
};
enum class USS_EchoTransmit_Comb_en
{
    uss_EchoTransmit_None_enm = 0,
    uss_EchoTransmit_Normal_enm = 1,
    uss_EchoTransmit_Parallel_enm = 2,
    uss_EchoTransmit_Max_enm = 3,
};
struct USS_EchoInfo_st
{
    zos::uint16_t EchoDist_u16;
    zos::uint8_t EchoAmp_u8;
    zos::uint8_t EchoProb_u8;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(USS_EchoInfo_st, EchoDist_u16, EchoAmp_u8, EchoProb_u8)

using DEArray=zos::Array<zos::Array<USS_EchoInfo_st, 8>, 12>;
using CE_leftArray=zos::Array<zos::Array<USS_EchoInfo_st, 8>, 12>;
using CE_rightArray=zos::Array<zos::Array<USS_EchoInfo_st, 8>, 12>;
struct USS_ECHO_measObjMonitor_st
{
    USS_EchoType_FreqMode_en EchoType_FreqMode_en;
    USS_EchoTransmit_Comb_en EchoTransmit_Comb_en;
    zos::uint32_t TransmitStartTime_u32;
    zos::uint64_t TransmitStartTime_u64;
    zos::uint16_t mpTransmitter_u16;
    zos::uint16_t mpReceiver_u16;
    DEArray DE_pst;
    CE_leftArray CE_left_pst;
    CE_rightArray CE_right_pst;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(USS_ECHO_measObjMonitor_st, EchoType_FreqMode_en, EchoTransmit_Comb_en, TransmitStartTime_u32, TransmitStartTime_u64, mpTransmitter_u16, mpReceiver_u16, DE_pst, CE_left_pst, CE_right_pst)

enum class USS_HeightStatus_en
{
    uss_HS_Traversable_enm = 0,
    uss_HS_Low_enm = 1,
    uss_HS_Unknown_enm = 2,
    uss_HS_High_enm = 3,
    uss_HS_BodyTraversable_enm = 4,
};
enum class USS_MAPObjType_en
{
    uss_MAP_None_enm = 0,
    uss_MAP_Point_enm = 1,
    uss_MAP_Straight_0Corner_enm = 2,
    uss_MAP_Straight_1Corner_enm = 3,
    uss_MAP_Straight_2Corner_enm = 4,
    uss_MAP_Curbstone_enm = 5,
    uss_MAP_Blind_enm = 6,
    uss_MAP_RoadMarking_enm = 7,
    uss_MAP_Obstacle_enm = 8,
};
struct USS_MAP_Object_st
{
    USS_LineSegment_st Coords_st;
    zos::uint16_t Length_u16;
    zos::uint16_t ObjID_u16;
    zos::uint8_t HeightProb_u8;
    USS_HeightStatus_en HeightStatus_en;
    USS_MAPObjType_en ObjType_en;
    zos::uint8_t ExistProb_u8;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(USS_MAP_Object_st, Coords_st, Length_u16, ObjID_u16, HeightProb_u8, HeightStatus_en, ObjType_en, ExistProb_u8)

using MapMeasObjectArray=zos::Array<USS_MAP_Object_st, 80>;
struct USS_MAP_measObjMonitor_st
{
    MapMeasObjectArray MapObjs_pst;
    zos::uint16_t numMapObjs_u16;
    zos::uint32_t ECUTimeL_u32;
    zos::uint32_t ECUTimeH_u32;
    zos::uint32_t Map_Reserve1_u32;
    zos::uint32_t Map_Reserve2_u32;
    zos::uint32_t Map_Reserve3_u32;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(USS_MAP_measObjMonitor_st, MapObjs_pst, numMapObjs_u16, ECUTimeL_u32, ECUTimeH_u32, Map_Reserve1_u32, Map_Reserve2_u32, Map_Reserve3_u32)

using UssReservedArray=zos::Array<zos::uint32_t, 50>;
struct USS_UssAppOutput_st
{
    USS_ParkingSpace_st ParkingSpace_st;
    USS_PDCOutput_st PDCOutput_st;
    USS_ROD_measObjMonitor_st ROD_measObjMonitor_st;
    USS_ECHO_measObjMonitor_st ECHO_measObjMonitor_st;
    USS_MAP_measObjMonitor_st MAP_measObjMonitor_st;
    UssReservedArray Reserved_pu32;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(USS_UssAppOutput_st, ParkingSpace_st, PDCOutput_st, ROD_measObjMonitor_st, ECHO_measObjMonitor_st, MAP_measObjMonitor_st, Reserved_pu32)

}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::uss_perception::USS_PointFl_st, StdLayoutTag>
{
    using ValueType = ::zos::uss_perception::USS_PointFl_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::USS_PointFl_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::uss_perception::USS_PointFl_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::USS_LineSegmentFl_st, StdLayoutTag>
{
    using ValueType = ::zos::uss_perception::USS_LineSegmentFl_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::USS_LineSegmentFl_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::uss_perception::USS_LineSegmentFl_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::USS_DepthRef_st, StdLayoutTag>
{
    using ValueType = ::zos::uss_perception::USS_DepthRef_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::USS_DepthRef_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::uss_perception::USS_DepthRef_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::USS_Obj_st, StdLayoutTag>
{
    using ValueType = ::zos::uss_perception::USS_Obj_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::USS_Obj_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::uss_perception::USS_Obj_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::USS_PSObjInfo_st, StdLayoutTag>
{
    using ValueType = ::zos::uss_perception::USS_PSObjInfo_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::USS_PSObjInfo_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::uss_perception::USS_PSObjInfo_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::USS_PSInfo_st, StdLayoutTag>
{
    using ValueType = ::zos::uss_perception::USS_PSInfo_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::USS_PSInfo_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::uss_perception::USS_PSInfo_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::USS_ParkingSpace_st, StdLayoutTag>
{
    using ValueType = ::zos::uss_perception::USS_ParkingSpace_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::USS_ParkingSpace_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::uss_perception::USS_ParkingSpace_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::USS_PDCOutput_st, StdLayoutTag>
{
    using ValueType = ::zos::uss_perception::USS_PDCOutput_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::USS_PDCOutput_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::uss_perception::USS_PDCOutput_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::USS_Point_st, StdLayoutTag>
{
    using ValueType = ::zos::uss_perception::USS_Point_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::USS_Point_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::uss_perception::USS_Point_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::USS_LineSegment_st, StdLayoutTag>
{
    using ValueType = ::zos::uss_perception::USS_LineSegment_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::USS_LineSegment_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::uss_perception::USS_LineSegment_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::USS_ROD_SensorSource_st, StdLayoutTag>
{
    using ValueType = ::zos::uss_perception::USS_ROD_SensorSource_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::USS_ROD_SensorSource_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::uss_perception::USS_ROD_SensorSource_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::USS_ROD_measObjectInfo_st, StdLayoutTag>
{
    using ValueType = ::zos::uss_perception::USS_ROD_measObjectInfo_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::USS_ROD_measObjectInfo_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::uss_perception::USS_ROD_measObjectInfo_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::USS_ROD_measObjHeightInfo_st, StdLayoutTag>
{
    using ValueType = ::zos::uss_perception::USS_ROD_measObjHeightInfo_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::USS_ROD_measObjHeightInfo_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::uss_perception::USS_ROD_measObjHeightInfo_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::USS_ROD_ObiectPostionFlags_st, StdLayoutTag>
{
    using ValueType = ::zos::uss_perception::USS_ROD_ObiectPostionFlags_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::USS_ROD_ObiectPostionFlags_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::uss_perception::USS_ROD_ObiectPostionFlags_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::USS_ROD_measObject_st, StdLayoutTag>
{
    using ValueType = ::zos::uss_perception::USS_ROD_measObject_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::USS_ROD_measObject_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::uss_perception::USS_ROD_measObject_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::USS_VehPos16_st, StdLayoutTag>
{
    using ValueType = ::zos::uss_perception::USS_VehPos16_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::USS_VehPos16_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::uss_perception::USS_VehPos16_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::USS_ROD_measObjMonitor_st, StdLayoutTag>
{
    using ValueType = ::zos::uss_perception::USS_ROD_measObjMonitor_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::USS_ROD_measObjMonitor_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::uss_perception::USS_ROD_measObjMonitor_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::USS_EchoInfo_st, StdLayoutTag>
{
    using ValueType = ::zos::uss_perception::USS_EchoInfo_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::USS_EchoInfo_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::uss_perception::USS_EchoInfo_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::USS_ECHO_measObjMonitor_st, StdLayoutTag>
{
    using ValueType = ::zos::uss_perception::USS_ECHO_measObjMonitor_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::USS_ECHO_measObjMonitor_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::uss_perception::USS_ECHO_measObjMonitor_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::USS_MAP_Object_st, StdLayoutTag>
{
    using ValueType = ::zos::uss_perception::USS_MAP_Object_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::USS_MAP_Object_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::uss_perception::USS_MAP_Object_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::USS_MAP_measObjMonitor_st, StdLayoutTag>
{
    using ValueType = ::zos::uss_perception::USS_MAP_measObjMonitor_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::USS_MAP_measObjMonitor_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::uss_perception::USS_MAP_measObjMonitor_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::USS_UssAppOutput_st, StdLayoutTag>
{
    using ValueType = ::zos::uss_perception::USS_UssAppOutput_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::USS_UssAppOutput_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::uss_perception::USS_UssAppOutput_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
