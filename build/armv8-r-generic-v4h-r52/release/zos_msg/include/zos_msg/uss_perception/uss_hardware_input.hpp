#pragma once
#include <basic_types/serialization/serialization_traits.hpp>
#include <basic_types/types.hpp>

namespace zos
{
namespace uss_perception
{
struct CUssHardInfo_st
{
    zos::uint16_t m_KL30PowerVoltage;
    zos::uint16_t m_ussPowerOut1Voltage;
    zos::uint16_t m_ussPowerOut2Voltage;
    zos::uint16_t m_ussPowerOut3Voltage;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(CUssHardInfo_st, m_KL30PowerVoltage, m_ussPowerOut1Voltage, m_ussPowerOut2Voltage, m_ussPowerOut3Voltage)

}
}
namespace zos
{
namespace serialization
{
template<>
struct SerializationTraits<::zos::uss_perception::CUssHardInfo_st, StdLayoutTag>
{
    using ValueType = ::zos::uss_perception::CUssHardInfo_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        if (buffer == nullptr)
        {
            return sizeof(ValueType);
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), GetSerializedSize(sample));
                return GetSerializedSize(sample);
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        if (std::addressof(sample) == nullptr)
        {
            return 0;
        }
        else
        {
            if (size < sizeof(ValueType))
            {
                return 0;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, sizeof(ValueType));
                return sizeof(ValueType);
            }
        }
    }
};
template<>
struct SerializationTraits<::zos::uss_perception::CUssHardInfo_st, StdLayoutTruncTag>
{
    using ValueType = ::zos::uss_perception::CUssHardInfo_st;
    static size_t GetSerializedSize(ValueType const& sample)
    {
        (void)sample;
        return sizeof(ValueType);
    }
    static size_t Serialize(ValueType const& sample, uint8_t* buffer, size_t size)
    {
        size_t ser_size = GetSerializedSize(sample);
        if (buffer == nullptr)
        {
            return ser_size;
        }
        else
        {
            if (size < GetSerializedSize(sample))
            {
                return 0;
            }
            else
            {
                std::memcpy(buffer, std::addressof(sample), ser_size);
                return ser_size;
            }
        }
    }
    static size_t Deserialize(uint8_t const* buffer, size_t size, ValueType& sample)
    {
        uint32_t des_size = sizeof(ValueType);
        if (size < des_size)
        {
            return 0;
        }
        else
        {
            if (std::addressof(sample) == nullptr)
            {
                return des_size;
            }
            else
            {
                std::memcpy(reinterpret_cast<void*>(std::addressof(sample)), buffer, des_size);
                return des_size;
            }
        }
    }
};
}
}
