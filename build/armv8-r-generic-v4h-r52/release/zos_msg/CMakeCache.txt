# This is the CMakeCache file.
# For build in directory: /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg
# It was generated by CMake: /home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//No help, variable specified on the command line.
BUILD_SHARED_LIBS:UNINITIALIZED=ON

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-addr2line

//Choose the type of build, options are: None Debug Release RelWithDebInfo
// MinSizeRel ...
CMAKE_BUILD_TYPE:STRING=RELEASE

//Enable/Disable color output during build.
CMAKE_COLOR_MAKEFILE:BOOL=ON

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_AR:FILEPATH=/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-gcc-ar

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_RANLIB:FILEPATH=/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-gcc-ranlib

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=-I/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/include -marm -mcpu=cortex-r52 -gdwarf-2 -std=c++14 -fverbose-asm -fno-exceptions -fno-threadsafe-statics -fno-rtti -Wno-reorder -O3 -std=c++14 -Werror=return-type -fno-omit-frame-pointer -fexceptions

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-g

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_AR:FILEPATH=/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-gcc-ar

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_RANLIB:FILEPATH=/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-gcc-ranlib

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=-I/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/include -marm -mcpu=cortex-r52 -gdwarf-2 -std=c99 -fverbose-asm -O3 -DNDEBUG -Werror=return-type -fno-omit-frame-pointer

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=-g

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=CMAKE_DLLTOOL-NOTFOUND

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=-Wl,-rpath-link,/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/3rdparty/lib:/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/tmp/lib:/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/lib -L/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/lib -Wl,-rpath-link,/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/lib -Wl,--entry=Start -Wl,--gc-sections -Wl,--cref

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//No help, variable specified on the command line.
CMAKE_EXPORT_COMPILE_COMMANDS:UNINITIALIZED=ON

//No help, variable specified on the command line.
CMAKE_EXPORT_NO_PACKAGE_REGISTRY:UNINITIALIZED=ON

//No help, variable specified on the command line.
CMAKE_FIND_PACKAGE_NO_PACKAGE_REGISTRY:UNINITIALIZED=ON

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/CMakeFiles/pkgRedirects

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target

//No help, variable specified on the command line.
CMAKE_INSTALL_RPATH:UNINITIALIZED=$ORIGIN/../lib:$ORIGIN/../3rdparty/lib:$ORIGIN/../../../lib:$ORIGIN/../../../3rdparty/lib

//Path to a program.
CMAKE_MAKE_PROGRAM:FILEPATH=/usr/bin/gmake

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=-Wl,-rpath-link,/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/3rdparty/lib:/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/tmp/lib:/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/lib -L/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/lib -Wl,-rpath-link,/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/lib -Wl,--entry=Start -Wl,--gc-sections -Wl,--cref

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-objcopy

//No help, variable specified on the command line.
CMAKE_POSITION_INDEPENDENT_CODE:UNINITIALIZED=ON

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=zos_msg

//Value Computed by CMake
CMAKE_PROJECT_VERSION:STATIC=1.0.0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MAJOR:STATIC=1

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MINOR:STATIC=0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_PATCH:STATIC=0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_TWEAK:STATIC=

//Path to a program.
CMAKE_READELF:FILEPATH=/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-readelf

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=-Wl,-rpath-link,/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/3rdparty/lib:/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/tmp/lib:/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/lib -L/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/lib -Wl,-rpath-link,/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/lib -Wl,--entry=Start -Wl,--gc-sections -Wl,--cref

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_TAPI:FILEPATH=CMAKE_TAPI-NOTFOUND

//No help, variable specified on the command line.
CMAKE_TOOLCHAIN_FILE:UNINITIALIZED=/home/<USER>/code/mcu_app_YT_workspace/crosstool/package/tools/cmake/toolchain_files/armv8-r-generic-gcc.cmake

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=OFF

//Path to a library.
GMOCK_LIBRARY:FILEPATH=GMOCK_LIBRARY-NOTFOUND

//Path to a library.
GMOCK_LIBRARY_DEBUG:FILEPATH=GMOCK_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
GMOCK_MAIN_LIBRARY:FILEPATH=GMOCK_MAIN_LIBRARY-NOTFOUND

//Path to a library.
GMOCK_MAIN_LIBRARY_DEBUG:FILEPATH=GMOCK_MAIN_LIBRARY_DEBUG-NOTFOUND

//Path to a file.
GTEST_INCLUDE_DIR:PATH=GTEST_INCLUDE_DIR-NOTFOUND

//Path to a library.
GTEST_LIBRARY:FILEPATH=GTEST_LIBRARY-NOTFOUND

//Path to a library.
GTEST_LIBRARY_DEBUG:FILEPATH=GTEST_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
GTEST_MAIN_LIBRARY:FILEPATH=GTEST_MAIN_LIBRARY-NOTFOUND

//Path to a library.
GTEST_MAIN_LIBRARY_DEBUG:FILEPATH=GTEST_MAIN_LIBRARY_DEBUG-NOTFOUND

//The directory containing a CMake configuration file for GTest.
GTest_DIR:PATH=GTest_DIR-NOTFOUND

//No help, variable specified on the command line.
IMOBUILDER_BUILD_ID:UNINITIALIZED=

//No help, variable specified on the command line.
IMOBUILDER_PLATFORM:UNINITIALIZED=armv8-r-generic-v4h-r52

//No help, variable specified on the command line.
IMOBUILDER_PRODUCT:UNINITIALIZED=Chery/D055/develop

//Path to a program.
ZOSIDL_PROGRAM:FILEPATH=/home/<USER>/.local/bin/zosidl

//Building product code
ZX_PRJ_CPJ_VAR:STRING=ZX_PRJ_CPJ_ICAR_05

//No help, variable specified on the command line.
ZX_SOC_TOOLCHAIN:UNINITIALIZED=/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10

//The directory containing a CMake configuration file for basic_types.
basic_types_DIR:PATH=/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/lib/cmake/basic_types

//The directory containing a CMake configuration file for nlohmann_json.
nlohmann_json_DIR:PATH=/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/3rdparty/lib/cmake/nlohmann_json

//The directory containing a CMake configuration file for vfc.
vfc_DIR:PATH=/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/lib/cmake/vfc

//Value Computed by CMake
zos_msg_BINARY_DIR:STATIC=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg

//Value Computed by CMake
zos_msg_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
zos_msg_SOURCE_DIR:STATIC=/home/<USER>/code/mcu_app_YT_workspace/src/zos_msg

//Value Computed by CMake
zos_msg_utils_BINARY_DIR:STATIC=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg/utils

//Value Computed by CMake
zos_msg_utils_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
zos_msg_utils_SOURCE_DIR:STATIC=/home/<USER>/code/mcu_app_YT_workspace/src/zos_msg/utils

//The directory containing a CMake configuration file for zos_scripts.
zos_scripts_DIR:PATH=/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/lib/cmake/zos_scripts


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/zos_msg
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=30
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=5
//ADVANCED property for variable: CMAKE_COLOR_MAKEFILE
CMAKE_COLOR_MAKEFILE-ADVANCED:INTERNAL=1
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/ctest
//ADVANCED property for variable: CMAKE_CXX_COMPILER_AR
CMAKE_CXX_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_RANLIB
CMAKE_CXX_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_AR
CMAKE_C_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_RANLIB
CMAKE_C_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Path to cache edit program executable.
CMAKE_EDIT_COMMAND:INTERNAL=/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/ccmake
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=ELF
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Unix Makefiles
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/home/<USER>/code/mcu_app_YT_workspace/src/zos_msg
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=5
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/share/cmake-3.30
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_TAPI
CMAKE_TAPI-ADVANCED:INTERNAL=1
//uname command
CMAKE_UNAME:INTERNAL=/usr/bin/uname
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//Details about finding Python3
FIND_PACKAGE_MESSAGE_DETAILS_Python3:INTERNAL=[/usr/bin/python3.12][cfound components: Interpreter ][v3.12.3()]
//Details about finding ZOSIDL
FIND_PACKAGE_MESSAGE_DETAILS_ZOSIDL:INTERNAL=[/home/<USER>/.local/bin/zosidl][v()]
//Details about finding nlohmann_json
FIND_PACKAGE_MESSAGE_DETAILS_nlohmann_json:INTERNAL=[/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/3rdparty/lib/cmake/nlohmann_json/nlohmann_jsonConfig.cmake][v3.9.1()]
//ADVANCED property for variable: GMOCK_LIBRARY
GMOCK_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GMOCK_LIBRARY_DEBUG
GMOCK_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GMOCK_MAIN_LIBRARY
GMOCK_MAIN_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GMOCK_MAIN_LIBRARY_DEBUG
GMOCK_MAIN_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GTEST_INCLUDE_DIR
GTEST_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GTEST_LIBRARY
GTEST_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GTEST_LIBRARY_DEBUG
GTEST_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GTEST_MAIN_LIBRARY
GTEST_MAIN_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GTEST_MAIN_LIBRARY_DEBUG
GTEST_MAIN_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//Compiler reason failure
_Python3_Compiler_REASON_FAILURE:INTERNAL=
//Development reason failure
_Python3_Development_REASON_FAILURE:INTERNAL=
//Path to a program.
_Python3_EXECUTABLE:INTERNAL=/usr/bin/python3.12
//Python3 Properties
_Python3_INTERPRETER_PROPERTIES:INTERNAL=Python;3;12;3;32;64;;;abi3;/usr/lib/python3.12;/usr/lib/python3.12;/usr/local/lib/python3.12/dist-packages;/usr/local/lib/python3.12/dist-packages
_Python3_INTERPRETER_SIGNATURE:INTERNAL=0b516266b7ed9a0986c924c82c2c3a08
//NumPy reason failure
_Python3_NumPy_REASON_FAILURE:INTERNAL=

