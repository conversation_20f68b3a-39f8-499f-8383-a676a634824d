/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/lib/libuss_sensl.a
/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/lib/cmake/uss_perception/uss_perceptionTargets.cmake
/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/lib/cmake/uss_perception/uss_perceptionConfigVersion.cmake
/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/lib/cmake/uss_perception/uss_perceptionConfig.cmake
/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/include/uss_perception/USS_PFApp.h
/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/include/uss_perception/USS_PFDiag.h
/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/include/uss_perception/USS_PFEnv.h
/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/include/uss_perception/USS_peripheral_driver.h