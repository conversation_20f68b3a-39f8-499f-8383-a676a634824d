
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/share/cmake-3.30/Modules/CMakeDetermineSystem.cmake:200 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The target system is: Generic -  - armv8-r
      The host system is: Linux - 6.11.0-26-generic - x86_64
  -
    kind: "message-v1"
    backtrace:
      - "/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/share/cmake-3.30/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler: /home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-gcc 
      Build flags: -I/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/include;-marm;-mcpu=cortex-r52;-gdwarf-2;-std=c99;-fverbose-asm;-O3;-DNDEBUG;-Werror=return-type;-fno-omit-frame-pointer
      Id flags:  
      
      The output was:
      1
      /home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/bin/ld: /home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v7/nofp/libg.a(lib_a-exit.o): in function `exit':
      exit.c:(.text.exit+0x1c): undefined reference to `_exit'
      collect2: error: ld returned 1 exit status
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/share/cmake-3.30/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: /home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-gcc 
      Build flags: -I/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/include;-marm;-mcpu=cortex-r52;-gdwarf-2;-std=c99;-fverbose-asm;-O3;-DNDEBUG;-Werror=return-type;-fno-omit-frame-pointer
      Id flags: -c 
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CMakeCCompilerId.o"
      
      The C compiler identification is GNU, found in:
        /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/uss_perception/CMakeFiles/3.30.5/CompilerIdC/CMakeCCompilerId.o
      
  -
    kind: "message-v1"
    backtrace:
      - "/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: /home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-g++ 
      Build flags: -I/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/include;-marm;-mcpu=cortex-r52;-gdwarf-2;-std=c++14;-fverbose-asm;-fno-exceptions;-fno-threadsafe-statics;-fno-rtti;-Wno-reorder;-O3;-std=c++14;-Werror=return-type;-fno-omit-frame-pointer;-fexceptions
      Id flags:  
      
      The output was:
      1
      /home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/bin/ld: /home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v7/nofp/libg.a(lib_a-exit.o): in function `exit':
      exit.c:(.text.exit+0x1c): undefined reference to `_exit'
      collect2: error: ld returned 1 exit status
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: /home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-g++ 
      Build flags: -I/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/include;-marm;-mcpu=cortex-r52;-gdwarf-2;-std=c++14;-fverbose-asm;-fno-exceptions;-fno-threadsafe-statics;-fno-rtti;-Wno-reorder;-O3;-std=c++14;-Werror=return-type;-fno-omit-frame-pointer;-fexceptions
      Id flags: -c 
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.o"
      
      The CXX compiler identification is GNU, found in:
        /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/uss_perception/CMakeFiles/3.30.5/CompilerIdCXX/CMakeCXXCompilerId.o
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/uss_perception/CMakeFiles/CMakeScratch/TryCompile-9SHn4V"
      binary: "/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/uss_perception/CMakeFiles/CMakeScratch/TryCompile-9SHn4V"
    cmakeVariables:
      CMAKE_C_FLAGS: "-I/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/include -marm -mcpu=cortex-r52 -gdwarf-2 -std=c99 -fverbose-asm -O3 -DNDEBUG -fno-omit-frame-pointer"
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: "-Wl,-rpath-link,/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/3rdparty/lib:/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/tmp/lib:/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/lib -L/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/lib -Wl,-rpath-link,/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/lib -Wl,--entry=Start -Wl,--gc-sections -Wl,--cref"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/uss_perception/CMakeFiles/CMakeScratch/TryCompile-9SHn4V'
        
        Run Build Command(s): /home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_c3fcf/fast
        /usr/bin/gmake  -f CMakeFiles/cmTC_c3fcf.dir/build.make CMakeFiles/cmTC_c3fcf.dir/build
        gmake[1]: Entering directory '/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/uss_perception/CMakeFiles/CMakeScratch/TryCompile-9SHn4V'
        Building C object CMakeFiles/cmTC_c3fcf.dir/CMakeCCompilerABI.c.obj
        /home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-gcc   -I/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/include -marm -mcpu=cortex-r52 -gdwarf-2 -std=c99 -fverbose-asm -O3 -DNDEBUG -fno-omit-frame-pointer    -v -o CMakeFiles/cmTC_c3fcf.dir/CMakeCCompilerABI.c.obj -c /home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/share/cmake-3.30/Modules/CMakeCCompilerABI.c
        Using built-in specs.
        COLLECT_GCC=/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-gcc
        Target: arm-none-eabi
        Configured with: /mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/src/gcc/configure --target=arm-none-eabi --prefix=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-native --libexecdir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-native/lib --infodir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-native/share/doc/gcc-arm-none-eabi/info --mandir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-native/share/doc/gcc-arm-none-eabi/man --htmldir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-native/share/doc/gcc-arm-none-eabi/html --pdfdir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-native/share/doc/gcc-arm-none-eabi/pdf --enable-languages=c,c++ --enable-plugins --disable-decimal-float --disable-libffi --disable-libgomp --disable-libmudflap --disable-libquadmath --disable-libssp --disable-libstdcxx-pch --disable-nls --disable-shared --disable-threads --disable-tls --with-gnu-as --with-gnu-ld --with-newlib --with-headers=yes --with-python-dir=share/gcc-arm-none-eabi --with-sysroot=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-native/arm-none-eabi --build=x86_64-linux-gnu --host=x86_64-linux-gnu --with-gmp=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-native/host-libs/usr --with-mpfr=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-native/host-libs/usr --with-mpc=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-native/host-libs/usr --with-isl=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-native/host-libs/usr --with-libelf=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-native/host-libs/usr --with-host-libstdcxx='-static-libgcc -Wl,-Bstatic,-lstdc++,-Bdynamic -lm' --with-pkgversion='GNU Arm Embedded Toolchain 10.3-2021.10' --with-multilib-list=rmprofile,aprofile
        Thread model: single
        Supported LTO compression algorithms: zlib
        gcc version 10.3.1 20210824 (release) (GNU Arm Embedded Toolchain 10.3-2021.10) 
        COLLECT_GCC_OPTIONS='-I' '/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/include' '-marm' '-mcpu=cortex-r52' '-gdwarf-2' '-std=c99' '-fverbose-asm' '-O3' '-D' 'NDEBUG' '-fno-omit-frame-pointer' '-v' '-o' 'CMakeFiles/cmTC_c3fcf.dir/CMakeCCompilerABI.c.obj' '-c' '-mfloat-abi=soft' '-mlibarch=armv8-r+crc' '-march=armv8-r+crc'
         /home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/cc1 -quiet -v -I /home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/include -imultilib thumb/v7/nofp -iprefix /home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/ -isysroot /home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi -D__USES_INITFINI__ -D NDEBUG /home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/share/cmake-3.30/Modules/CMakeCCompilerABI.c -quiet -dumpbase CMakeCCompilerABI.c -marm -mcpu=cortex-r52 -mfloat-abi=soft -mlibarch=armv8-r+crc -march=armv8-r+crc -auxbase-strip CMakeFiles/cmTC_c3fcf.dir/CMakeCCompilerABI.c.obj -gdwarf-2 -O3 -std=c99 -version -fverbose-asm -fno-omit-frame-pointer -o /tmp/ccRCms7y.s
        GNU C99 (GNU Arm Embedded Toolchain 10.3-2021.10) version 10.3.1 20210824 (release) (arm-none-eabi)
        	compiled by GNU C version 4.8.4, GMP version 6.1.0, MPFR version 3.1.4, MPC version 1.0.3, isl version isl-0.18-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring duplicate directory "/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/10.3.1/include"
        ignoring nonexistent directory "/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/usr/local/include"
        ignoring duplicate directory "/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/10.3.1/include-fixed"
        ignoring duplicate directory "/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include"
        ignoring nonexistent directory "/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/usr/include"
        ignoring nonexistent directory "/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/include"
        #include "..." search starts here:
        #include <...> search starts here:
         /home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/include
         /home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/include-fixed
         /home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include
        End of search list.
        GNU C99 (GNU Arm Embedded Toolchain 10.3-2021.10) version 10.3.1 20210824 (release) (arm-none-eabi)
        	compiled by GNU C version 4.8.4, GMP version 6.1.0, MPFR version 3.1.4, MPC version 1.0.3, isl version isl-0.18-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        Compiler executable checksum: ada28fdd13bdd399f195284d34df0081
        COLLECT_GCC_OPTIONS='-I' '/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/include' '-marm' '-mcpu=cortex-r52' '-gdwarf-2' '-std=c99' '-fverbose-asm' '-O3' '-D' 'NDEBUG' '-fno-omit-frame-pointer' '-v' '-o' 'CMakeFiles/cmTC_c3fcf.dir/CMakeCCompilerABI.c.obj' '-c' '-mfloat-abi=soft' '-mlibarch=armv8-r+crc' '-march=armv8-r+crc'
         /home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/bin/as -v -I /home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/include -march=armv8-r+crc -mfloat-abi=soft -meabi=5 -o CMakeFiles/cmTC_c3fcf.dir/CMakeCCompilerABI.c.obj /tmp/ccRCms7y.s
        GNU assembler version 2.36.1 (arm-none-eabi) using BFD version (GNU Arm Embedded Toolchain 10.3-2021.10) 2.36.1.20210621
        COMPILER_PATH=/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/:/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/:/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/bin/
        LIBRARY_PATH=/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v7/nofp/:/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v7/nofp/:/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib/thumb/v7/nofp/:/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/:/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/:/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/:/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib/
        COLLECT_GCC_OPTIONS='-I' '/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/include' '-marm' '-mcpu=cortex-r52' '-gdwarf-2' '-std=c99' '-fverbose-asm' '-O3' '-D' 'NDEBUG' '-fno-omit-frame-pointer' '-v' '-o' 'CMakeFiles/cmTC_c3fcf.dir/CMakeCCompilerABI.c.obj' '-c' '-mfloat-abi=soft' '-mlibarch=armv8-r+crc' '-march=armv8-r+crc'
        Linking C static library libcmTC_c3fcf.a
        /home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -P CMakeFiles/cmTC_c3fcf.dir/cmake_clean_target.cmake
        /home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E cmake_link_script CMakeFiles/cmTC_c3fcf.dir/link.txt --verbose=1
        /home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-ar qc libcmTC_c3fcf.a CMakeFiles/cmTC_c3fcf.dir/CMakeCCompilerABI.c.obj
        /home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-gcc-ranlib libcmTC_c3fcf.a
        gmake[1]: Leaving directory '/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/uss_perception/CMakeFiles/CMakeScratch/TryCompile-9SHn4V'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:182 (message)"
      - "/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/include]
          add: [/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/include-fixed]
          add: [/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include]
        end of search list found
        collapse include dir [/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/include] ==> [/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1/include]
        collapse include dir [/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/include-fixed] ==> [/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1/include-fixed]
        collapse include dir [/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include] ==> [/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/include]
        implicit include dirs: [/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1/include;/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1/include-fixed;/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?))("|,| |$)]
        ignore line: [Change Dir: '/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/uss_perception/CMakeFiles/CMakeScratch/TryCompile-9SHn4V']
        ignore line: []
        ignore line: [Run Build Command(s): /home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_c3fcf/fast]
        ignore line: [/usr/bin/gmake  -f CMakeFiles/cmTC_c3fcf.dir/build.make CMakeFiles/cmTC_c3fcf.dir/build]
        ignore line: [gmake[1]: Entering directory '/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/uss_perception/CMakeFiles/CMakeScratch/TryCompile-9SHn4V']
        ignore line: [Building C object CMakeFiles/cmTC_c3fcf.dir/CMakeCCompilerABI.c.obj]
        ignore line: [/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-gcc   -I/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/include -marm -mcpu=cortex-r52 -gdwarf-2 -std=c99 -fverbose-asm -O3 -DNDEBUG -fno-omit-frame-pointer    -v -o CMakeFiles/cmTC_c3fcf.dir/CMakeCCompilerABI.c.obj -c /home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/share/cmake-3.30/Modules/CMakeCCompilerABI.c]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-gcc]
        ignore line: [Target: arm-none-eabi]
        ignore line: [Configured with: /mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/src/gcc/configure --target=arm-none-eabi --prefix=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-native --libexecdir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-native/lib --infodir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-native/share/doc/gcc-arm-none-eabi/info --mandir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-native/share/doc/gcc-arm-none-eabi/man --htmldir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-native/share/doc/gcc-arm-none-eabi/html --pdfdir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-native/share/doc/gcc-arm-none-eabi/pdf --enable-languages=c,c++ --enable-plugins --disable-decimal-float --disable-libffi --disable-libgomp --disable-libmudflap --disable-libquadmath --disable-libssp --disable-libstdcxx-pch --disable-nls --disable-shared --disable-threads --disable-tls --with-gnu-as --with-gnu-ld --with-newlib --with-headers=yes --with-python-dir=share/gcc-arm-none-eabi --with-sysroot=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-native/arm-none-eabi --build=x86_64-linux-gnu --host=x86_64-linux-gnu --with-gmp=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-native/host-libs/usr --with-mpfr=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-native/host-libs/usr --with-mpc=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-native/host-libs/usr --with-isl=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-native/host-libs/usr --with-libelf=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-native/host-libs/usr --with-host-libstdcxx='-static-libgcc -Wl,-Bstatic,-lstdc++,-Bdynamic -lm' --with-pkgversion='GNU Arm Embedded Toolchain 10.3-2021.10' --with-multilib-list=rmprofile,aprofile]
        ignore line: [Thread model: single]
        ignore line: [Supported LTO compression algorithms: zlib]
        ignore line: [gcc version 10.3.1 20210824 (release) (GNU Arm Embedded Toolchain 10.3-2021.10) ]
        ignore line: [COLLECT_GCC_OPTIONS='-I' '/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/include' '-marm' '-mcpu=cortex-r52' '-gdwarf-2' '-std=c99' '-fverbose-asm' '-O3' '-D' 'NDEBUG' '-fno-omit-frame-pointer' '-v' '-o' 'CMakeFiles/cmTC_c3fcf.dir/CMakeCCompilerABI.c.obj' '-c' '-mfloat-abi=soft' '-mlibarch=armv8-r+crc' '-march=armv8-r+crc']
        ignore line: [ /home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/cc1 -quiet -v -I /home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/include -imultilib thumb/v7/nofp -iprefix /home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/ -isysroot /home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi -D__USES_INITFINI__ -D NDEBUG /home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/share/cmake-3.30/Modules/CMakeCCompilerABI.c -quiet -dumpbase CMakeCCompilerABI.c -marm -mcpu=cortex-r52 -mfloat-abi=soft -mlibarch=armv8-r+crc -march=armv8-r+crc -auxbase-strip CMakeFiles/cmTC_c3fcf.dir/CMakeCCompilerABI.c.obj -gdwarf-2 -O3 -std=c99 -version -fverbose-asm -fno-omit-frame-pointer -o /tmp/ccRCms7y.s]
        ignore line: [GNU C99 (GNU Arm Embedded Toolchain 10.3-2021.10) version 10.3.1 20210824 (release) (arm-none-eabi)]
        ignore line: [	compiled by GNU C version 4.8.4  GMP version 6.1.0  MPFR version 3.1.4  MPC version 1.0.3  isl version isl-0.18-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring duplicate directory "/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/10.3.1/include"]
        ignore line: [ignoring nonexistent directory "/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/usr/local/include"]
        ignore line: [ignoring duplicate directory "/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/10.3.1/include-fixed"]
        ignore line: [ignoring duplicate directory "/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include"]
        ignore line: [ignoring nonexistent directory "/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/usr/include"]
        ignore line: [ignoring nonexistent directory "/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/include]
        ignore line: [ /home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/include-fixed]
        ignore line: [ /home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include]
        ignore line: [End of search list.]
        ignore line: [GNU C99 (GNU Arm Embedded Toolchain 10.3-2021.10) version 10.3.1 20210824 (release) (arm-none-eabi)]
        ignore line: [	compiled by GNU C version 4.8.4  GMP version 6.1.0  MPFR version 3.1.4  MPC version 1.0.3  isl version isl-0.18-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [Compiler executable checksum: ada28fdd13bdd399f195284d34df0081]
        ignore line: [COLLECT_GCC_OPTIONS='-I' '/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/include' '-marm' '-mcpu=cortex-r52' '-gdwarf-2' '-std=c99' '-fverbose-asm' '-O3' '-D' 'NDEBUG' '-fno-omit-frame-pointer' '-v' '-o' 'CMakeFiles/cmTC_c3fcf.dir/CMakeCCompilerABI.c.obj' '-c' '-mfloat-abi=soft' '-mlibarch=armv8-r+crc' '-march=armv8-r+crc']
        ignore line: [ /home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/bin/as -v -I /home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/include -march=armv8-r+crc -mfloat-abi=soft -meabi=5 -o CMakeFiles/cmTC_c3fcf.dir/CMakeCCompilerABI.c.obj /tmp/ccRCms7y.s]
        ignore line: [GNU assembler version 2.36.1 (arm-none-eabi) using BFD version (GNU Arm Embedded Toolchain 10.3-2021.10) 2.36.1.20210621]
        ignore line: [COMPILER_PATH=/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/:/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/:/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/bin/]
        ignore line: [LIBRARY_PATH=/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v7/nofp/:/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v7/nofp/:/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib/thumb/v7/nofp/:/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/:/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/:/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/:/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-I' '/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/include' '-marm' '-mcpu=cortex-r52' '-gdwarf-2' '-std=c99' '-fverbose-asm' '-O3' '-D' 'NDEBUG' '-fno-omit-frame-pointer' '-v' '-o' 'CMakeFiles/cmTC_c3fcf.dir/CMakeCCompilerABI.c.obj' '-c' '-mfloat-abi=soft' '-mlibarch=armv8-r+crc' '-march=armv8-r+crc']
        ignore line: [Linking C static library libcmTC_c3fcf.a]
        ignore line: [/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -P CMakeFiles/cmTC_c3fcf.dir/cmake_clean_target.cmake]
        ignore line: [/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E cmake_link_script CMakeFiles/cmTC_c3fcf.dir/link.txt --verbose=1]
        ignore line: [/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-ar qc libcmTC_c3fcf.a CMakeFiles/cmTC_c3fcf.dir/CMakeCCompilerABI.c.obj]
        ignore line: [/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-gcc-ranlib libcmTC_c3fcf.a]
        ignore line: [gmake[1]: Leaving directory '/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/uss_perception/CMakeFiles/CMakeScratch/TryCompile-9SHn4V']
        ignore line: []
        ignore line: []
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/uss_perception/CMakeFiles/CMakeScratch/TryCompile-kOZlAw"
      binary: "/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/uss_perception/CMakeFiles/CMakeScratch/TryCompile-kOZlAw"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "-I/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/include -marm -mcpu=cortex-r52 -gdwarf-2 -std=c++14 -fverbose-asm -fno-exceptions -fno-threadsafe-statics -fno-rtti -Wno-reorder -O3 -std=c++14 -fno-omit-frame-pointer -fexceptions"
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "-Wl,-rpath-link,/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/3rdparty/lib:/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/tmp/lib:/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/lib -L/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/lib -Wl,-rpath-link,/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/lib -Wl,--entry=Start -Wl,--gc-sections -Wl,--cref"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/uss_perception/CMakeFiles/CMakeScratch/TryCompile-kOZlAw'
        
        Run Build Command(s): /home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_69cb6/fast
        /usr/bin/gmake  -f CMakeFiles/cmTC_69cb6.dir/build.make CMakeFiles/cmTC_69cb6.dir/build
        gmake[1]: Entering directory '/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/uss_perception/CMakeFiles/CMakeScratch/TryCompile-kOZlAw'
        Building CXX object CMakeFiles/cmTC_69cb6.dir/CMakeCXXCompilerABI.cpp.obj
        /home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-g++   -I/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/include -marm -mcpu=cortex-r52 -gdwarf-2 -std=c++14 -fverbose-asm -fno-exceptions -fno-threadsafe-statics -fno-rtti -Wno-reorder -O3 -std=c++14 -fno-omit-frame-pointer -fexceptions    -v -o CMakeFiles/cmTC_69cb6.dir/CMakeCXXCompilerABI.cpp.obj -c /home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/share/cmake-3.30/Modules/CMakeCXXCompilerABI.cpp
        Using built-in specs.
        COLLECT_GCC=/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-g++
        Target: arm-none-eabi
        Configured with: /mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/src/gcc/configure --target=arm-none-eabi --prefix=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-native --libexecdir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-native/lib --infodir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-native/share/doc/gcc-arm-none-eabi/info --mandir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-native/share/doc/gcc-arm-none-eabi/man --htmldir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-native/share/doc/gcc-arm-none-eabi/html --pdfdir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-native/share/doc/gcc-arm-none-eabi/pdf --enable-languages=c,c++ --enable-plugins --disable-decimal-float --disable-libffi --disable-libgomp --disable-libmudflap --disable-libquadmath --disable-libssp --disable-libstdcxx-pch --disable-nls --disable-shared --disable-threads --disable-tls --with-gnu-as --with-gnu-ld --with-newlib --with-headers=yes --with-python-dir=share/gcc-arm-none-eabi --with-sysroot=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-native/arm-none-eabi --build=x86_64-linux-gnu --host=x86_64-linux-gnu --with-gmp=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-native/host-libs/usr --with-mpfr=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-native/host-libs/usr --with-mpc=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-native/host-libs/usr --with-isl=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-native/host-libs/usr --with-libelf=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-native/host-libs/usr --with-host-libstdcxx='-static-libgcc -Wl,-Bstatic,-lstdc++,-Bdynamic -lm' --with-pkgversion='GNU Arm Embedded Toolchain 10.3-2021.10' --with-multilib-list=rmprofile,aprofile
        Thread model: single
        Supported LTO compression algorithms: zlib
        gcc version 10.3.1 20210824 (release) (GNU Arm Embedded Toolchain 10.3-2021.10) 
        COLLECT_GCC_OPTIONS='-I' '/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/include' '-marm' '-mcpu=cortex-r52' '-gdwarf-2' '-std=c++14' '-fverbose-asm' '-fno-threadsafe-statics' '-fno-rtti' '-Wno-reorder' '-O3' '-std=c++14' '-fno-omit-frame-pointer' '-fexceptions' '-v' '-o' 'CMakeFiles/cmTC_69cb6.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-mfloat-abi=soft' '-mlibarch=armv8-r+crc' '-march=armv8-r+crc'
         /home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/cc1plus -quiet -v -I /home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/include -imultilib thumb/v7/nofp -iprefix /home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/ -isysroot /home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi -D__USES_INITFINI__ /home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/share/cmake-3.30/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpbase CMakeCXXCompilerABI.cpp -marm -mcpu=cortex-r52 -mfloat-abi=soft -mlibarch=armv8-r+crc -march=armv8-r+crc -auxbase-strip CMakeFiles/cmTC_69cb6.dir/CMakeCXXCompilerABI.cpp.obj -gdwarf-2 -O3 -Wno-reorder -std=c++14 -std=c++14 -version -fverbose-asm -fno-threadsafe-statics -fno-rtti -fno-omit-frame-pointer -fexceptions -o /tmp/ccg4zjBC.s
        GNU C++14 (GNU Arm Embedded Toolchain 10.3-2021.10) version 10.3.1 20210824 (release) (arm-none-eabi)
        	compiled by GNU C version 4.8.4, GMP version 6.1.0, MPFR version 3.1.4, MPC version 1.0.3, isl version isl-0.18-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring duplicate directory "/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include/c++/10.3.1"
        ignoring duplicate directory "/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include/c++/10.3.1/arm-none-eabi/thumb/v7/nofp"
        ignoring duplicate directory "/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include/c++/10.3.1/backward"
        ignoring duplicate directory "/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/10.3.1/include"
        ignoring nonexistent directory "/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/usr/local/include"
        ignoring duplicate directory "/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/10.3.1/include-fixed"
        ignoring duplicate directory "/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include"
        ignoring nonexistent directory "/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/usr/include"
        ignoring nonexistent directory "/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/include"
        #include "..." search starts here:
        #include <...> search starts here:
         /home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include/c++/10.3.1
         /home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include/c++/10.3.1/arm-none-eabi/thumb/v7/nofp
         /home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include/c++/10.3.1/backward
         /home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/include
         /home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/include-fixed
         /home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include
        End of search list.
        GNU C++14 (GNU Arm Embedded Toolchain 10.3-2021.10) version 10.3.1 20210824 (release) (arm-none-eabi)
        	compiled by GNU C version 4.8.4, GMP version 6.1.0, MPFR version 3.1.4, MPC version 1.0.3, isl version isl-0.18-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        Compiler executable checksum: 62a2c0a0f0d30bb0bbe5c686601adbb0
        COLLECT_GCC_OPTIONS='-I' '/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/include' '-marm' '-mcpu=cortex-r52' '-gdwarf-2' '-std=c++14' '-fverbose-asm' '-fno-threadsafe-statics' '-fno-rtti' '-Wno-reorder' '-O3' '-std=c++14' '-fno-omit-frame-pointer' '-fexceptions' '-v' '-o' 'CMakeFiles/cmTC_69cb6.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-mfloat-abi=soft' '-mlibarch=armv8-r+crc' '-march=armv8-r+crc'
         /home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/bin/as -v -I /home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/include -march=armv8-r+crc -mfloat-abi=soft -meabi=5 -o CMakeFiles/cmTC_69cb6.dir/CMakeCXXCompilerABI.cpp.obj /tmp/ccg4zjBC.s
        GNU assembler version 2.36.1 (arm-none-eabi) using BFD version (GNU Arm Embedded Toolchain 10.3-2021.10) 2.36.1.20210621
        COMPILER_PATH=/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/:/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/:/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/bin/
        LIBRARY_PATH=/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v7/nofp/:/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v7/nofp/:/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib/thumb/v7/nofp/:/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/:/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/:/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/:/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib/
        COLLECT_GCC_OPTIONS='-I' '/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/include' '-marm' '-mcpu=cortex-r52' '-gdwarf-2' '-std=c++14' '-fverbose-asm' '-fno-threadsafe-statics' '-fno-rtti' '-Wno-reorder' '-O3' '-std=c++14' '-fno-omit-frame-pointer' '-fexceptions' '-v' '-o' 'CMakeFiles/cmTC_69cb6.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-mfloat-abi=soft' '-mlibarch=armv8-r+crc' '-march=armv8-r+crc'
        Linking CXX static library libcmTC_69cb6.a
        /home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -P CMakeFiles/cmTC_69cb6.dir/cmake_clean_target.cmake
        /home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E cmake_link_script CMakeFiles/cmTC_69cb6.dir/link.txt --verbose=1
        /home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-ar qc libcmTC_69cb6.a CMakeFiles/cmTC_69cb6.dir/CMakeCXXCompilerABI.cpp.obj
        /home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-gcc-ranlib libcmTC_69cb6.a
        gmake[1]: Leaving directory '/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/uss_perception/CMakeFiles/CMakeScratch/TryCompile-kOZlAw'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:182 (message)"
      - "/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include/c++/10.3.1]
          add: [/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include/c++/10.3.1/arm-none-eabi/thumb/v7/nofp]
          add: [/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include/c++/10.3.1/backward]
          add: [/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/include]
          add: [/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/include-fixed]
          add: [/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include]
        end of search list found
        collapse include dir [/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include/c++/10.3.1] ==> [/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/include/c++/10.3.1]
        collapse include dir [/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include/c++/10.3.1/arm-none-eabi/thumb/v7/nofp] ==> [/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/include/c++/10.3.1/arm-none-eabi/thumb/v7/nofp]
        collapse include dir [/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include/c++/10.3.1/backward] ==> [/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/include/c++/10.3.1/backward]
        collapse include dir [/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/include] ==> [/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1/include]
        collapse include dir [/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/include-fixed] ==> [/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1/include-fixed]
        collapse include dir [/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include] ==> [/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/include]
        implicit include dirs: [/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/include/c++/10.3.1;/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/include/c++/10.3.1/arm-none-eabi/thumb/v7/nofp;/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/include/c++/10.3.1/backward;/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1/include;/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1/include-fixed;/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?))("|,| |$)]
        ignore line: [Change Dir: '/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/uss_perception/CMakeFiles/CMakeScratch/TryCompile-kOZlAw']
        ignore line: []
        ignore line: [Run Build Command(s): /home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E env VERBOSE=1 /usr/bin/gmake -f Makefile cmTC_69cb6/fast]
        ignore line: [/usr/bin/gmake  -f CMakeFiles/cmTC_69cb6.dir/build.make CMakeFiles/cmTC_69cb6.dir/build]
        ignore line: [gmake[1]: Entering directory '/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/uss_perception/CMakeFiles/CMakeScratch/TryCompile-kOZlAw']
        ignore line: [Building CXX object CMakeFiles/cmTC_69cb6.dir/CMakeCXXCompilerABI.cpp.obj]
        ignore line: [/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-g++   -I/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/include -marm -mcpu=cortex-r52 -gdwarf-2 -std=c++14 -fverbose-asm -fno-exceptions -fno-threadsafe-statics -fno-rtti -Wno-reorder -O3 -std=c++14 -fno-omit-frame-pointer -fexceptions    -v -o CMakeFiles/cmTC_69cb6.dir/CMakeCXXCompilerABI.cpp.obj -c /home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/share/cmake-3.30/Modules/CMakeCXXCompilerABI.cpp]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-g++]
        ignore line: [Target: arm-none-eabi]
        ignore line: [Configured with: /mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/src/gcc/configure --target=arm-none-eabi --prefix=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-native --libexecdir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-native/lib --infodir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-native/share/doc/gcc-arm-none-eabi/info --mandir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-native/share/doc/gcc-arm-none-eabi/man --htmldir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-native/share/doc/gcc-arm-none-eabi/html --pdfdir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-native/share/doc/gcc-arm-none-eabi/pdf --enable-languages=c,c++ --enable-plugins --disable-decimal-float --disable-libffi --disable-libgomp --disable-libmudflap --disable-libquadmath --disable-libssp --disable-libstdcxx-pch --disable-nls --disable-shared --disable-threads --disable-tls --with-gnu-as --with-gnu-ld --with-newlib --with-headers=yes --with-python-dir=share/gcc-arm-none-eabi --with-sysroot=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-native/arm-none-eabi --build=x86_64-linux-gnu --host=x86_64-linux-gnu --with-gmp=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-native/host-libs/usr --with-mpfr=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-native/host-libs/usr --with-mpc=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-native/host-libs/usr --with-isl=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-native/host-libs/usr --with-libelf=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-native/host-libs/usr --with-host-libstdcxx='-static-libgcc -Wl,-Bstatic,-lstdc++,-Bdynamic -lm' --with-pkgversion='GNU Arm Embedded Toolchain 10.3-2021.10' --with-multilib-list=rmprofile,aprofile]
        ignore line: [Thread model: single]
        ignore line: [Supported LTO compression algorithms: zlib]
        ignore line: [gcc version 10.3.1 20210824 (release) (GNU Arm Embedded Toolchain 10.3-2021.10) ]
        ignore line: [COLLECT_GCC_OPTIONS='-I' '/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/include' '-marm' '-mcpu=cortex-r52' '-gdwarf-2' '-std=c++14' '-fverbose-asm' '-fno-threadsafe-statics' '-fno-rtti' '-Wno-reorder' '-O3' '-std=c++14' '-fno-omit-frame-pointer' '-fexceptions' '-v' '-o' 'CMakeFiles/cmTC_69cb6.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-mfloat-abi=soft' '-mlibarch=armv8-r+crc' '-march=armv8-r+crc']
        ignore line: [ /home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/cc1plus -quiet -v -I /home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/include -imultilib thumb/v7/nofp -iprefix /home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/ -isysroot /home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi -D__USES_INITFINI__ /home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/share/cmake-3.30/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpbase CMakeCXXCompilerABI.cpp -marm -mcpu=cortex-r52 -mfloat-abi=soft -mlibarch=armv8-r+crc -march=armv8-r+crc -auxbase-strip CMakeFiles/cmTC_69cb6.dir/CMakeCXXCompilerABI.cpp.obj -gdwarf-2 -O3 -Wno-reorder -std=c++14 -std=c++14 -version -fverbose-asm -fno-threadsafe-statics -fno-rtti -fno-omit-frame-pointer -fexceptions -o /tmp/ccg4zjBC.s]
        ignore line: [GNU C++14 (GNU Arm Embedded Toolchain 10.3-2021.10) version 10.3.1 20210824 (release) (arm-none-eabi)]
        ignore line: [	compiled by GNU C version 4.8.4  GMP version 6.1.0  MPFR version 3.1.4  MPC version 1.0.3  isl version isl-0.18-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring duplicate directory "/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include/c++/10.3.1"]
        ignore line: [ignoring duplicate directory "/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include/c++/10.3.1/arm-none-eabi/thumb/v7/nofp"]
        ignore line: [ignoring duplicate directory "/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include/c++/10.3.1/backward"]
        ignore line: [ignoring duplicate directory "/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/10.3.1/include"]
        ignore line: [ignoring nonexistent directory "/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/usr/local/include"]
        ignore line: [ignoring duplicate directory "/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/10.3.1/include-fixed"]
        ignore line: [ignoring duplicate directory "/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include"]
        ignore line: [ignoring nonexistent directory "/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/usr/include"]
        ignore line: [ignoring nonexistent directory "/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include/c++/10.3.1]
        ignore line: [ /home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include/c++/10.3.1/arm-none-eabi/thumb/v7/nofp]
        ignore line: [ /home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include/c++/10.3.1/backward]
        ignore line: [ /home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/include]
        ignore line: [ /home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/include-fixed]
        ignore line: [ /home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include]
        ignore line: [End of search list.]
        ignore line: [GNU C++14 (GNU Arm Embedded Toolchain 10.3-2021.10) version 10.3.1 20210824 (release) (arm-none-eabi)]
        ignore line: [	compiled by GNU C version 4.8.4  GMP version 6.1.0  MPFR version 3.1.4  MPC version 1.0.3  isl version isl-0.18-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [Compiler executable checksum: 62a2c0a0f0d30bb0bbe5c686601adbb0]
        ignore line: [COLLECT_GCC_OPTIONS='-I' '/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/include' '-marm' '-mcpu=cortex-r52' '-gdwarf-2' '-std=c++14' '-fverbose-asm' '-fno-threadsafe-statics' '-fno-rtti' '-Wno-reorder' '-O3' '-std=c++14' '-fno-omit-frame-pointer' '-fexceptions' '-v' '-o' 'CMakeFiles/cmTC_69cb6.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-mfloat-abi=soft' '-mlibarch=armv8-r+crc' '-march=armv8-r+crc']
        ignore line: [ /home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/bin/as -v -I /home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/include -march=armv8-r+crc -mfloat-abi=soft -meabi=5 -o CMakeFiles/cmTC_69cb6.dir/CMakeCXXCompilerABI.cpp.obj /tmp/ccg4zjBC.s]
        ignore line: [GNU assembler version 2.36.1 (arm-none-eabi) using BFD version (GNU Arm Embedded Toolchain 10.3-2021.10) 2.36.1.20210621]
        ignore line: [COMPILER_PATH=/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/:/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/:/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/bin/]
        ignore line: [LIBRARY_PATH=/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v7/nofp/:/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v7/nofp/:/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib/thumb/v7/nofp/:/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/:/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/:/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/:/home/<USER>/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-I' '/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/include' '-marm' '-mcpu=cortex-r52' '-gdwarf-2' '-std=c++14' '-fverbose-asm' '-fno-threadsafe-statics' '-fno-rtti' '-Wno-reorder' '-O3' '-std=c++14' '-fno-omit-frame-pointer' '-fexceptions' '-v' '-o' 'CMakeFiles/cmTC_69cb6.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-mfloat-abi=soft' '-mlibarch=armv8-r+crc' '-march=armv8-r+crc']
        ignore line: [Linking CXX static library libcmTC_69cb6.a]
        ignore line: [/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -P CMakeFiles/cmTC_69cb6.dir/cmake_clean_target.cmake]
        ignore line: [/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E cmake_link_script CMakeFiles/cmTC_69cb6.dir/link.txt --verbose=1]
        ignore line: [/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-ar qc libcmTC_69cb6.a CMakeFiles/cmTC_69cb6.dir/CMakeCXXCompilerABI.cpp.obj]
        ignore line: [/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-gcc-ranlib libcmTC_69cb6.a]
        ignore line: [gmake[1]: Leaving directory '/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/uss_perception/CMakeFiles/CMakeScratch/TryCompile-kOZlAw']
        ignore line: []
        ignore line: []
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
...
