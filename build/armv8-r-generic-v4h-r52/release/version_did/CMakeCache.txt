# This is the CMakeCache file.
# For build in directory: /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/version_did
# It was generated by CMake: /home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//No help, variable specified on the command line.
CMAKE_BUILD_TYPE:UNINITIALIZED=RELEASE

//Enable/Disable color output during build.
CMAKE_COLOR_MAKEFILE:BOOL=ON

//No help, variable specified on the command line.
CMAKE_EXPORT_COMPILE_COMMANDS:UNINITIALIZED=ON

//No help, variable specified on the command line.
CMAKE_EXPORT_NO_PACKAGE_REGISTRY:UNINITIALIZED=ON

//No help, variable specified on the command line.
CMAKE_FIND_PACKAGE_NO_PACKAGE_REGISTRY:UNINITIALIZED=ON

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/version_did/CMakeFiles/pkgRedirects

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target

//Path to a program.
CMAKE_MAKE_PROGRAM:FILEPATH=/usr/bin/gmake

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=version_did

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//The CMake toolchain file
CMAKE_TOOLCHAIN_FILE:FILEPATH=/home/<USER>/code/mcu_app_YT_workspace/crosstool/package/tools/cmake/toolchain_files/armv8-r-generic-gcc.cmake

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=OFF

//No help, variable specified on the command line.
IMOBUILDER_BUILD_ID:UNINITIALIZED=

//No help, variable specified on the command line.
IMOBUILDER_PLATFORM:UNINITIALIZED=armv8-r-generic-v4h-r52

//No help, variable specified on the command line.
IMOBUILDER_PRODUCT:UNINITIALIZED=Chery/D055/develop

//No help, variable specified on the command line.
ZX_CUSTOMER_PROJECT:UNINITIALIZED=cpj_chery

//No help, variable specified on the command line.
ZX_SOC_TOOLCHAIN:UNINITIALIZED=/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10

//No help, variable specified on the command line.
ZX_VEH_VARIANT:UNINITIALIZED=icar05

//Value Computed by CMake
version_did_BINARY_DIR:STATIC=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/version_did

//Value Computed by CMake
version_did_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
version_did_SOURCE_DIR:STATIC=/home/<USER>/code/mcu_app_YT_workspace/src/version_did


########################
# INTERNAL cache entries
########################

//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/version_did
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=30
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=5
//ADVANCED property for variable: CMAKE_COLOR_MAKEFILE
CMAKE_COLOR_MAKEFILE-ADVANCED:INTERNAL=1
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/ctest
//Path to cache edit program executable.
CMAKE_EDIT_COMMAND:INTERNAL=/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/ccmake
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Unix Makefiles
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/home/<USER>/code/mcu_app_YT_workspace/src/version_did
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/share/cmake-3.30
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//uname command
CMAKE_UNAME:INTERNAL=/usr/bin/uname
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
ZX_DID_2001:INTERNAL=
ZX_DID_2002:INTERNAL=
ZX_DID_F012:INTERNAL=704000856AA
ZX_DID_F013:INTERNAL=S0000008356
ZX_DID_F031:INTERNAL=704000856AA
ZX_DID_F032:INTERNAL=00.01.00
ZX_DID_F089:INTERNAL=1.0.2
ZX_DID_F100:INTERNAL=
ZX_DID_F101:INTERNAL=
ZX_DID_F10A:INTERNAL=704000856AA
ZX_DID_F10B:INTERNAL=
ZX_DID_F10D:INTERNAL=
ZX_DID_F17F:INTERNAL=
ZX_DID_F180:INTERNAL=V140v1
ZX_DID_F181:INTERNAL=
ZX_DID_F187:INTERNAL=702000518AA
ZX_DID_F188:INTERNAL=00.00.01
ZX_DID_F189:INTERNAL=01.00.03
ZX_DID_F18A:INTERNAL=9GE
ZX_DID_F18E:INTERNAL=
ZX_DID_F191:INTERNAL=0.0.1
ZX_DID_F193:INTERNAL=
ZX_DID_F195:INTERNAL=V110v1
ZX_DID_F197:INTERNAL=IDM
ZX_DID_F19E:INTERNAL=
ZX_DID_F1B2:INTERNAL=
ZX_DID_F289:INTERNAL=V110v1

