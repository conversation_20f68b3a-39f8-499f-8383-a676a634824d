# version_didConfig.cmake.in
#
# Usage:
#   find_package(version_did REQUIRED)
#   version_did_generate_cpp(VERSION_DID_SOURCES VERSION_DID_HEADERS DESTINATION generated)
#   add_library(<target> ${VERSION_DID_SOURCES} <sources>)
#
set(version_did_FOUND TRUE)
set(version_did_DIR ${CMAKE_CURRENT_LIST_DIR})

function(VERSION_DID_GENERATE_CPP SRCS HDRS)
    cmake_parse_arguments(version_did "" "DESTINATION" "" ${ARGN})

    if (NOT ${version_did_DESTINATION})
        set(version_did_DESTINATION generated)
    endif()

    if (NOT IS_ABSOLUTE ${version_did_DESTINATION})
        set(_destination ${PROJECT_BINARY_DIR}/${version_did_DESTINATION})
    endif()

    file(COPY ${version_did_DIR}/version_did.cpp DESTINATION ${_destination})
    file(COPY ${version_did_DIR}/version_did.hpp DESTINATION ${_destination})

    set(${HDRS} ${SRCS} PROPERTIES GENERATED TRUE)

    set(${HDRS} ${_destination}/version_did.hpp PARENT_SCOPE)
    set(${SRCS} ${_destination}/version_did.cpp PARENT_SCOPE)
endfunction()
