//=============================================================================
//  C O P Y R I G H T
//-----------------------------------------------------------------------------
// @copyright (c) 2022 by iMotion AI. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: generated by serialize::zx_version.cmake
//=============================================================================
#ifndef ZX_VERSION_DID__GENERATED_INCLUDED_HPP
#define ZX_VERSION_DID__GENERATED_INCLUDED_HPP

#include "cstdint"

// did general define
#define APP_INFO_LEN                    (0x24u)
#define DIA_DID_READ_MAX_LENGTH         (0xFFu)
#define DIA_DID_DEFLAUT_VALUE           (0x20u) //space
#define DIA_DID_F19E_DEFLAUT_VALUE      (0x00u) 

// did info length define
#define DEM_CURRENT_STATUS_LENGTH (125u)
#define DEM_CURRENT_PERATION_CYCLE_STATUS_LENGTH (125u)
#define DEM_HISTORY_STATUS_LENGTH (125u)
#define FIMFID_STATUS_LENGTH (20u)
#define VEHICLE_MANUFACTURER_LOGISTIC_HARDWARE_PART_NUMBER_LENGTH    (16u)
#define VEHICLE_MANUFACTURER_LOGISTIC_SOFTWARE_PART_NUMBER_LENGTH    (16u)
#define VEHICLE_MANUFACTURER_CALIBRATION_DATA_PART_NUMBER_LENGTH (16u)
#define VEHICLE_MANUFACTURER_LOGISITIC_ASSEMBLY_PART_NUMBER_LENGTH (16u)
#define BOOT_SOFTWARE_ID_LENGTH (32u)
#define VEHICLE_MANUFACTURER_SPARE_PART_NUMBER_LENGTH (16u)
#define VEHICLE_MANUFACTURER_ECU_SOFTWARE_VERSION_NUMBER_LENGTH (8u)
#define SYSTEM_SUPPLIER_ID_LENGTH (3u)  
#define VEHICLE_MANUFACTURER_ECU_HARDWARE_VERSION_NUMBER_LENGTH (5u)
#define SYSTEM_SUPPLIER_ECU_HARDWARE_VERSION_NUMBER_LENGTH (9u)
#define SYSTEM_SUPPLIER_ECU_SOFTWARE_VERSION_NUMBER_LENGTH (9u)
#define SYSTEM_NAME_ID_LENGTH (9u)
#define ODX_ECU_VAIANT_VERSION_NUMBER_LENGTH (13u)
#define VARIANT_COUNT          (3u)

extern const uint8_t g_DidData_F010_au8[APP_INFO_LEN];
extern const uint8_t g_DidData_F012_au8[APP_INFO_LEN];
extern const uint8_t g_DidData_F013_au8[APP_INFO_LEN];
extern const uint8_t g_DidData_F013_au8_t2x[VARIANT_COUNT][APP_INFO_LEN];
extern const uint8_t g_DidData_F019_au8[APP_INFO_LEN];
extern const uint8_t g_DidData_F031_au8[APP_INFO_LEN];
extern const uint8_t g_DidData_F032_au8[APP_INFO_LEN];
extern const uint8_t g_DidData_F089_au8[APP_INFO_LEN];
extern const uint8_t g_DidData_F100_au8[APP_INFO_LEN];
extern const uint8_t g_DidData_F101_au8[APP_INFO_LEN];
extern const uint8_t g_DidData_F10A_au8[APP_INFO_LEN];
extern const uint8_t g_DidData_F10B_au8[APP_INFO_LEN];
extern const uint8_t g_DidData_F10D_au8[APP_INFO_LEN];
extern const uint8_t g_DidData_F17F_au8[APP_INFO_LEN];
extern const uint8_t g_DidData_F180_au8[APP_INFO_LEN];
extern const uint8_t g_DidData_F180_au8_t2x[VARIANT_COUNT][APP_INFO_LEN];
extern const uint8_t g_DidData_F181_au8[APP_INFO_LEN];
extern const uint8_t g_DidData_F184_au8[APP_INFO_LEN];
extern const uint8_t g_DidData_F187_au8[APP_INFO_LEN];
extern const uint8_t g_DidData_F187_au8_t2x[VARIANT_COUNT][APP_INFO_LEN];
extern const uint8_t g_DidData_F188_au8[APP_INFO_LEN];
extern const uint8_t g_DidData_F189_au8[APP_INFO_LEN];
extern const uint8_t g_DidData_F18A_au8[APP_INFO_LEN];
extern const uint8_t g_DidData_F18B_au8[APP_INFO_LEN];
extern const uint8_t g_DidData_F18C_au8[APP_INFO_LEN];
extern const uint8_t g_DidData_F18E_au8[APP_INFO_LEN];
extern const uint8_t g_DidData_F191_au8[APP_INFO_LEN];
extern const uint8_t g_DidData_F192_au8[APP_INFO_LEN];
extern const uint8_t g_DidData_F193_au8[APP_INFO_LEN];
extern const uint8_t g_DidData_F194_au8[APP_INFO_LEN];
extern const uint8_t g_DidData_F195_au8[APP_INFO_LEN];
extern const uint8_t g_DidData_F195_au8_t2x[VARIANT_COUNT][APP_INFO_LEN];
extern const uint8_t g_DidData_F197_au8[APP_INFO_LEN];
extern const uint8_t g_DidData_F19E_au8[APP_INFO_LEN];
extern const uint8_t g_DidData_F1B2_au8[APP_INFO_LEN];
extern const uint8_t g_DidData_2001_au8[APP_INFO_LEN];
extern const uint8_t g_DidData_2002_au8[APP_INFO_LEN];
extern const uint8_t g_DidData_0106_au8[APP_INFO_LEN];

#endif // ZX_VERSION_DID__GENERATED_INCLUDED_HPP
