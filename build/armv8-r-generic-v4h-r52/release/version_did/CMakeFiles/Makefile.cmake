# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.30

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "CMakeFiles/3.30.5/CMakeSystem.cmake"
  "/home/<USER>/code/mcu_app_YT_workspace/crosstool/package/tools/cmake/toolchain_files/armv8-r-generic-gcc.cmake"
  "/home/<USER>/code/mcu_app_YT_workspace/src/version_did/CMakeLists.txt"
  "/home/<USER>/code/mcu_app_YT_workspace/src/version_did/modules/zx_version_did_cpj_chery.cmake"
  "/home/<USER>/code/mcu_app_YT_workspace/src/version_did/templates/version_did.cpp.in"
  "/home/<USER>/code/mcu_app_YT_workspace/src/version_did/templates/version_did.hpp.in"
  "/home/<USER>/code/mcu_app_YT_workspace/src/version_did/version_didConfig.cmake.in"
  "/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/share/cmake-3.30/Modules/CMakeDetermineSystem.cmake"
  "/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/share/cmake-3.30/Modules/CMakeGenericSystem.cmake"
  "/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/share/cmake-3.30/Modules/CMakeInitializeConfigs.cmake"
  "/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/share/cmake-3.30/Modules/CMakeSystem.cmake.in"
  "/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/share/cmake-3.30/Modules/CMakeSystemSpecificInformation.cmake"
  "/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/share/cmake-3.30/Modules/CMakeSystemSpecificInitialize.cmake"
  "/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/share/cmake-3.30/Modules/CMakeUnixFindMake.cmake"
  "/home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/share/cmake-3.30/Modules/Platform/Generic.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/3.30.5/CMakeSystem.cmake"
  "generated/version_didConfig.cmake"
  "generated/version_did.cpp"
  "generated/version_did.hpp"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  )
