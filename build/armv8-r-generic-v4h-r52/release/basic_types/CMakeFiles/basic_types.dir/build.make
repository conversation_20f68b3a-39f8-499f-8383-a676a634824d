# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.30

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake

# The command to remove a file.
RM = /home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/code/mcu_app_YT_workspace/src/basic_types

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/basic_types

# Include any dependencies generated for this target.
include CMakeFiles/basic_types.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/basic_types.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/basic_types.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/basic_types.dir/flags.make

CMakeFiles/basic_types.dir/src/common/geographical_transformation.cpp.obj: CMakeFiles/basic_types.dir/flags.make
CMakeFiles/basic_types.dir/src/common/geographical_transformation.cpp.obj: /home/<USER>/code/mcu_app_YT_workspace/src/basic_types/src/common/geographical_transformation.cpp
CMakeFiles/basic_types.dir/src/common/geographical_transformation.cpp.obj: CMakeFiles/basic_types.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/basic_types/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/basic_types.dir/src/common/geographical_transformation.cpp.obj"
	/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/basic_types.dir/src/common/geographical_transformation.cpp.obj -MF CMakeFiles/basic_types.dir/src/common/geographical_transformation.cpp.obj.d -o CMakeFiles/basic_types.dir/src/common/geographical_transformation.cpp.obj -c /home/<USER>/code/mcu_app_YT_workspace/src/basic_types/src/common/geographical_transformation.cpp

CMakeFiles/basic_types.dir/src/common/geographical_transformation.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/basic_types.dir/src/common/geographical_transformation.cpp.i"
	/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/code/mcu_app_YT_workspace/src/basic_types/src/common/geographical_transformation.cpp > CMakeFiles/basic_types.dir/src/common/geographical_transformation.cpp.i

CMakeFiles/basic_types.dir/src/common/geographical_transformation.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/basic_types.dir/src/common/geographical_transformation.cpp.s"
	/home/<USER>/code/mcu_app_YT_workspace/crosstool/toolchain/armv8-r/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/code/mcu_app_YT_workspace/src/basic_types/src/common/geographical_transformation.cpp -o CMakeFiles/basic_types.dir/src/common/geographical_transformation.cpp.s

# Object files for target basic_types
basic_types_OBJECTS = \
"CMakeFiles/basic_types.dir/src/common/geographical_transformation.cpp.obj"

# External object files for target basic_types
basic_types_EXTERNAL_OBJECTS =

libbasic_types.a: CMakeFiles/basic_types.dir/src/common/geographical_transformation.cpp.obj
libbasic_types.a: CMakeFiles/basic_types.dir/build.make
libbasic_types.a: CMakeFiles/basic_types.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/basic_types/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX static library libbasic_types.a"
	$(CMAKE_COMMAND) -P CMakeFiles/basic_types.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/basic_types.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/basic_types.dir/build: libbasic_types.a
.PHONY : CMakeFiles/basic_types.dir/build

CMakeFiles/basic_types.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/basic_types.dir/cmake_clean.cmake
.PHONY : CMakeFiles/basic_types.dir/clean

CMakeFiles/basic_types.dir/depend:
	cd /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/basic_types && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/code/mcu_app_YT_workspace/src/basic_types /home/<USER>/code/mcu_app_YT_workspace/src/basic_types /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/basic_types /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/basic_types /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/basic_types/CMakeFiles/basic_types.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/basic_types.dir/depend

