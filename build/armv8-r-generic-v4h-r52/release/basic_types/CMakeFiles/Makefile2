# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.30

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake

# The command to remove a file.
RM = /home/<USER>/crosstool/package/tools/cmake/cmake-3.30.5-linux-x86_64/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/code/mcu_app_YT_workspace/src/basic_types

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/basic_types

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/basic_types.dir/all
all: test/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall: test/preinstall
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/basic_types.dir/clean
clean: test/clean
.PHONY : clean

#=============================================================================
# Directory level rules for directory test

# Recursive "all" directory target.
test/all: test/gtest/all
.PHONY : test/all

# Recursive "preinstall" directory target.
test/preinstall: test/gtest/preinstall
.PHONY : test/preinstall

# Recursive "clean" directory target.
test/clean: test/gtest/clean
.PHONY : test/clean

#=============================================================================
# Directory level rules for directory test/gtest

# Recursive "all" directory target.
test/gtest/all:
.PHONY : test/gtest/all

# Recursive "preinstall" directory target.
test/gtest/preinstall:
.PHONY : test/gtest/preinstall

# Recursive "clean" directory target.
test/gtest/clean:
.PHONY : test/gtest/clean

#=============================================================================
# Target rules for target CMakeFiles/basic_types.dir

# All Build rule for target.
CMakeFiles/basic_types.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/basic_types.dir/build.make CMakeFiles/basic_types.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/basic_types.dir/build.make CMakeFiles/basic_types.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/basic_types/CMakeFiles --progress-num=1,2 "Built target basic_types"
.PHONY : CMakeFiles/basic_types.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/basic_types.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/basic_types/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/basic_types.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/code/mcu_app_YT_workspace/build/armv8-r-generic-v4h-r52/release/basic_types/CMakeFiles 0
.PHONY : CMakeFiles/basic_types.dir/rule

# Convenience name for target.
basic_types: CMakeFiles/basic_types.dir/rule
.PHONY : basic_types

# clean rule for target.
CMakeFiles/basic_types.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/basic_types.dir/build.make CMakeFiles/basic_types.dir/clean
.PHONY : CMakeFiles/basic_types.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

