#----------------------------------------------------------------
# Generated CMake target import file for configuration "RELEASE".
#----------------------------------------------------------------

# Commands may need to know the format version.
set(CMAKE_IMPORT_FILE_VERSION 1)

# Import target "zos::basic_types" for configuration "RELEASE"
set_property(TARGET zos::basic_types APPEND PROPERTY IMPORTED_CONFIGURATIONS RELEASE)
set_target_properties(zos::basic_types PROPERTIES
  IMPORTED_LINK_INTERFACE_LANGUAGES_RELEASE "CXX"
  IMPORTED_LOCATION_RELEASE "${_IMPORT_PREFIX}/lib/libbasic_types.a"
  )

list(APPEND _cmake_import_check_targets zos::basic_types )
list(APPEND _cmake_import_check_files_for_zos::basic_types "${_IMPORT_PREFIX}/lib/libbasic_types.a" )

# Commands beyond this point should not need to know the version.
set(CMAKE_IMPORT_FILE_VERSION)
