/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/lib/libbasic_types.a
/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/lib/cmake/basic_types/basic_types-targets.cmake
/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/lib/cmake/basic_types/basic_types-targets-release.cmake
/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/lib/cmake/basic_types/basic_typesConfigVersion.cmake
/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/lib/cmake/basic_types/basic_typesConfig.cmake
/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/include/basic_types/basic_types/types.hpp
/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/include/basic_types/basic_types/serialization/serialization_traits.hpp
/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/include/basic_types/basic_types/common/vehicle_location.hpp
/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/include/basic_types/basic_types/common/sensor_id.hpp
/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/include/basic_types/basic_types/common/geographical_transformation.hpp
/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/include/basic_types/basic_types/common/coordinate.hpp
/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/include/basic_types/basic_types/container/array.hpp
/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/include/basic_types/basic_types/container/ordered_buffer.hpp
/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/include/basic_types/basic_types/container/traits.hpp
/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/include/basic_types/basic_types/container/vector.hpp
/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/include/basic_types/basic_types/container/deque.hpp
/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/include/basic_types/basic_types/container/object_pool.h
/home/<USER>/code/mcu_app_YT_workspace/output/armv8-r-generic-v4h-r52/release/target/include/basic_types/basic_types/container/string.hpp