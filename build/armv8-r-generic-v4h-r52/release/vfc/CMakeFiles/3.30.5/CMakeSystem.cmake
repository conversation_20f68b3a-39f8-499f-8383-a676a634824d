set(CMAKE_HOST_SYSTEM "Linux-6.11.0-26-generic")
set(CMAKE_HOST_SYSTEM_NAME "Linux")
set(CMAKE_HOST_SYSTEM_VERSION "6.11.0-26-generic")
set(CMAKE_HOST_SYSTEM_PROCESSOR "x86_64")

include("/home/<USER>/code/mcu_app_YT_workspace/crosstool/package/tools/cmake/toolchain_files/armv8-r-generic-gcc.cmake")

set(CMAKE_SYSTEM "Generic")
set(CMAKE_SYSTEM_NAME "Generic")
set(CMAKE_SYSTEM_VERSION "")
set(CMAKE_SYSTEM_PROCESSOR "armv8-r")

set(CMAKE_CROSSCOMPILING "TRUE")

set(CMAKE_SYSTEM_LOADED 1)
